# Configurações do Servidor
PORT=3001
NODE_ENV=development

# Configurações do Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Secret (para tokens customizados se necessário)
JWT_SECRET=your-super-secret-jwt-key-here

# CORS Origins (separados por vírgula)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Upload Settings
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Database Connection (caso precise de conexão direta)
DATABASE_URL=postgresql://user:password@host:port/database

# Logs
LOG_LEVEL=info
