<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gráficos - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-car-wash"></i> Lavagem</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html" class="active"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li><a href="listview.html"><i class="fas fa-list"></i> Listview</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                        <li><a href="configuracoes.html"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Gráficos e Análises</h1>
            </div>
            
            <div class="top-bar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            <!-- Enhanced Header with Real-time Status -->
            <div class="enhanced-header">
                <div class="header-main">
                    <div class="header-title">
                        <div class="title-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="title-content">
                            <h1>Dashboard de Gráficos</h1>
                            <p>Análise avançada e visualização de dados da frota</p>
                            <div class="header-badges">
                                <span class="badge bg-success" id="dataStatus">
                                    <i class="fas fa-check-circle"></i> Dados Atualizados
                                </span>
                                <span class="badge bg-info" id="lastUpdate">
                                    <i class="fas fa-clock"></i> Atualizado há 2 min
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="refreshCharts()" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i>
                                <span>Atualizar</span>
                            </button>
                            <div class="btn-group">
                                <button class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download"></i> Exportar
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" onclick="exportCharts('pdf')">
                                        <i class="fas fa-file-pdf"></i> Exportar PDF
                                    </a></li>
                                    <li><a class="dropdown-item" onclick="exportCharts('excel')">
                                        <i class="fas fa-file-excel"></i> Exportar Excel
                                    </a></li>
                                    <li><a class="dropdown-item" onclick="exportCharts('json')">
                                        <i class="fas fa-file-code"></i> Exportar JSON
                                    </a></li>
                                </ul>
                            </div>
                            <button class="btn btn-outline-secondary" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Real-time Metrics Bar -->
                <div class="metrics-bar">
                    <div class="metric-item">
                        <div class="metric-icon bg-primary">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-value" id="realTimeFuel">1,245L</span>
                            <span class="metric-label">Combustível Hoje</span>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-icon bg-success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-value" id="realTimeCost">R$ 8,450</span>
                            <span class="metric-label">Custos Hoje</span>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-icon bg-warning">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-value" id="realTimeKm">2,890km</span>
                            <span class="metric-label">Km Rodados</span>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-icon bg-info">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-value" id="realTimeEfficiency">12.5km/L</span>
                            <span class="metric-label">Eficiência Média</span>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-icon bg-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="metric-content">
                            <span class="metric-value" id="realTimeAlerts">3</span>
                            <span class="metric-label">Alertas Ativos</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters Panel -->
            <div class="advanced-filters">
                <div class="filters-header">
                    <h3><i class="fas fa-filter"></i> Filtros Avançados</h3>
                    <div class="filters-toggle">
                        <button class="btn btn-sm btn-outline-primary" onclick="toggleFiltersPanel()">
                            <i class="fas fa-chevron-down" id="filtersToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="filters-content" id="filtersContent">
                    <div class="row g-3">
                        <!-- Período -->
                        <div class="col-md-3">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-calendar-alt"></i> Período
                                </label>
                                <select class="form-select" id="periodFilter" onchange="updateCharts()">
                                    <option value="1">Hoje</option>
                                    <option value="7">Últimos 7 dias</option>
                                    <option value="30" selected>Últimos 30 dias</option>
                                    <option value="90">Últimos 3 meses</option>
                                    <option value="365">Último ano</option>
                                    <option value="custom">Período personalizado</option>
                                </select>
                            </div>
                        </div>

                        <!-- Veículo -->
                        <div class="col-md-3">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-car"></i> Veículo
                                </label>
                                <select class="form-select" id="vehicleFilter" onchange="updateCharts()">
                                    <option value="">Todos os veículos</option>
                                </select>
                            </div>
                        </div>

                        <!-- Categoria -->
                        <div class="col-md-3">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-tags"></i> Categoria
                                </label>
                                <select class="form-select" id="categoryFilter" onchange="updateCharts()">
                                    <option value="all">Todas as categorias</option>
                                    <option value="fuel">Combustível</option>
                                    <option value="maintenance">Manutenção</option>
                                    <option value="costs">Custos</option>
                                    <option value="performance">Performance</option>
                                </select>
                            </div>
                        </div>

                        <!-- Tipo de Visualização -->
                        <div class="col-md-3">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-eye"></i> Visualização
                                </label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="viewType" id="viewCharts" value="charts" checked>
                                    <label class="btn btn-outline-primary" for="viewCharts">
                                        <i class="fas fa-chart-line"></i>
                                    </label>

                                    <input type="radio" class="btn-check" name="viewType" id="viewTable" value="table">
                                    <label class="btn btn-outline-primary" for="viewTable">
                                        <i class="fas fa-table"></i>
                                    </label>

                                    <input type="radio" class="btn-check" name="viewType" id="viewGrid" value="grid">
                                    <label class="btn btn-outline-primary" for="viewGrid">
                                        <i class="fas fa-th"></i>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Segunda linha de filtros -->
                    <div class="row g-3 mt-2">
                        <!-- Data personalizada -->
                        <div class="col-md-3" id="customDateRange" style="display: none;">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-calendar-range"></i> Data Inicial
                                </label>
                                <input type="date" class="form-control" id="startDate" onchange="updateCharts()">
                            </div>
                        </div>

                        <div class="col-md-3" id="customDateRangeEnd" style="display: none;">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-calendar-range"></i> Data Final
                                </label>
                                <input type="date" class="form-control" id="endDate" onchange="updateCharts()">
                            </div>
                        </div>

                        <!-- Comparação -->
                        <div class="col-md-3">
                            <div class="filter-group">
                                <label class="filter-label">
                                    <i class="fas fa-balance-scale"></i> Comparar com
                                </label>
                                <select class="form-select" id="compareFilter" onchange="updateCharts()">
                                    <option value="">Sem comparação</option>
                                    <option value="previous">Período anterior</option>
                                    <option value="year">Mesmo período ano passado</option>
                                    <option value="average">Média histórica</option>
                                </select>
                            </div>
                        </div>

                        <!-- Ações -->
                        <div class="col-md-3">
                            <div class="filter-group">
                                <label class="filter-label">&nbsp;</label>
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                        <i class="fas fa-times"></i> Limpar
                                    </button>
                                    <button class="btn btn-outline-info" onclick="saveFilters()">
                                        <i class="fas fa-save"></i> Salvar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filtros rápidos -->
                    <div class="quick-filters mt-3">
                        <label class="filter-label">
                            <i class="fas fa-bolt"></i> Filtros Rápidos:
                        </label>
                        <div class="quick-filter-buttons">
                            <button class="btn btn-sm btn-outline-primary" onclick="applyQuickFilter('today')">Hoje</button>
                            <button class="btn btn-sm btn-outline-primary" onclick="applyQuickFilter('week')">Esta Semana</button>
                            <button class="btn btn-sm btn-outline-primary" onclick="applyQuickFilter('month')">Este Mês</button>
                            <button class="btn btn-sm btn-outline-primary" onclick="applyQuickFilter('quarter')">Trimestre</button>
                            <button class="btn btn-sm btn-outline-primary" onclick="applyQuickFilter('year')">Este Ano</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Charts Grid -->
            <div class="charts-grid" id="chartsGrid">
                <!-- Primary Charts Row -->
                <div class="charts-row primary-charts">
                    <div class="chart-container large">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-primary">
                                        <i class="fas fa-gas-pump"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Consumo de Combustível</h4>
                                        <p>Análise detalhada do consumo por período</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('fuelConsumptionChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('fuelConsumptionChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="fuelChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="fuelChartLoading">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="fuelConsumptionChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Média Diária:</span>
                                        <span class="insight-value" id="fuelDailyAvg">125L</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Tendência:</span>
                                        <span class="insight-value trend-up" id="fuelTrend">
                                            <i class="fas fa-arrow-up"></i> +5.2%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container medium">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-success">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Custos por Categoria</h4>
                                        <p>Distribuição de gastos operacionais</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('costsByCategoryChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('costsByCategoryChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="costsChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="costsChartLoading">
                                    <div class="spinner-border text-success" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="costsByCategoryChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Maior Gasto:</span>
                                        <span class="insight-value" id="highestCost">Combustível</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Economia:</span>
                                        <span class="insight-value trend-down" id="costSaving">
                                            <i class="fas fa-arrow-down"></i> -3.1%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Secondary Charts Row -->
                <div class="charts-row secondary-charts">
                    <div class="chart-container wide">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-warning">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Evolução de Custos</h4>
                                        <p>Tendência de gastos ao longo do tempo</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('costsEvolutionChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('costsEvolutionChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="evolutionChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="evolutionChartLoading">
                                    <div class="spinner-border text-warning" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="costsEvolutionChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Variação Mensal:</span>
                                        <span class="insight-value trend-up" id="monthlyVariation">
                                            <i class="fas fa-arrow-up"></i> +8.3%
                                        </span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Pico:</span>
                                        <span class="insight-value" id="costPeak">R$ 15,200</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container small">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-info">
                                        <i class="fas fa-car"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Status da Frota</h4>
                                        <p>Situação atual dos veículos</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('fleetStatusChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('fleetStatusChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="fleetChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="fleetChartLoading">
                                    <div class="spinner-border text-info" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="fleetStatusChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Ativos:</span>
                                        <span class="insight-value" id="activeVehicles">85%</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Manutenção:</span>
                                        <span class="insight-value" id="maintenanceVehicles">15%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tertiary Charts Row -->
                <div class="charts-row tertiary-charts">
                    <div class="chart-container medium">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-danger">
                                        <i class="fas fa-wrench"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Manutenções por Mês</h4>
                                        <p>Frequência de manutenções</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('maintenanceChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('maintenanceChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="maintenanceChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="maintenanceChartLoading">
                                    <div class="spinner-border text-danger" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="maintenanceChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Este Mês:</span>
                                        <span class="insight-value" id="thisMonthMaintenance">12</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Média:</span>
                                        <span class="insight-value" id="avgMaintenance">8.5</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container medium">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-secondary">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Quilometragem por Veículo</h4>
                                        <p>Comparativo de uso</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('mileageChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('mileageChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="mileageChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="mileageChartLoading">
                                    <div class="spinner-border text-secondary" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="mileageChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Maior Km:</span>
                                        <span class="insight-value" id="highestKm">45,200km</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Média:</span>
                                        <span class="insight-value" id="avgKm">28,500km</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quaternary Charts Row -->
                <div class="charts-row quaternary-charts">
                    <div class="chart-container full">
                        <div class="enhanced-chart-card">
                            <div class="chart-card-header">
                                <div class="header-left">
                                    <div class="chart-icon bg-dark">
                                        <i class="fas fa-chart-area"></i>
                                    </div>
                                    <div class="chart-title">
                                        <h4>Análise Comparativa Mensal</h4>
                                        <p>Comparação de métricas principais</p>
                                    </div>
                                </div>
                                <div class="header-right">
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('comparativeChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="fullscreenChart('comparativeChart')">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                    <div class="chart-status">
                                        <span class="status-indicator active" id="comparativeChartStatus"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="chart-body">
                                <div class="chart-loading" id="comparativeChartLoading">
                                    <div class="spinner-border text-dark" role="status">
                                        <span class="visually-hidden">Carregando...</span>
                                    </div>
                                </div>
                                <canvas id="comparativeChart"></canvas>
                                <div class="chart-insights">
                                    <div class="insight-item">
                                        <span class="insight-label">Melhor Mês:</span>
                                        <span class="insight-value" id="bestMonth">Setembro</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Eficiência:</span>
                                        <span class="insight-value trend-up" id="overallEfficiency">
                                            <i class="fas fa-arrow-up"></i> +12.8%
                                        </span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-label">Economia:</span>
                                        <span class="insight-value trend-down" id="overallSavings">
                                            <i class="fas fa-arrow-down"></i> R$ 2,340
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Summary -->
            <div class="enhanced-statistics">
                <div class="statistics-header">
                    <h3><i class="fas fa-chart-pie"></i> Resumo Estatístico</h3>
                    <div class="statistics-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshStatistics()">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="exportStatistics()">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>

                <div class="statistics-grid">
                    <div class="enhanced-stat-card primary">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-gas-pump"></i>
                            </div>
                            <div class="stat-trend">
                                <span class="trend-indicator up">
                                    <i class="fas fa-arrow-up"></i> 5.2%
                                </span>
                            </div>
                        </div>
                        <div class="stat-card-body">
                            <div class="stat-value" id="totalFuelConsumed">2,450L</div>
                            <div class="stat-label">Combustível Consumido</div>
                            <div class="stat-period" id="fuelPeriod">Últimos 30 dias</div>
                        </div>
                        <div class="stat-card-footer">
                            <div class="stat-comparison">
                                <span class="comparison-label">vs. período anterior:</span>
                                <span class="comparison-value positive">+125L</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card success">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-trend">
                                <span class="trend-indicator down">
                                    <i class="fas fa-arrow-down"></i> 3.1%
                                </span>
                            </div>
                        </div>
                        <div class="stat-card-body">
                            <div class="stat-value" id="totalCosts">R$ 18,450</div>
                            <div class="stat-label">Custos Totais</div>
                            <div class="stat-period" id="costsPeriod">Últimos 30 dias</div>
                        </div>
                        <div class="stat-card-footer">
                            <div class="stat-comparison">
                                <span class="comparison-label">vs. período anterior:</span>
                                <span class="comparison-value negative">-R$ 590</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card warning">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-wrench"></i>
                            </div>
                            <div class="stat-trend">
                                <span class="trend-indicator up">
                                    <i class="fas fa-arrow-up"></i> 15.8%
                                </span>
                            </div>
                        </div>
                        <div class="stat-card-body">
                            <div class="stat-value" id="totalMaintenances">23</div>
                            <div class="stat-label">Manutenções</div>
                            <div class="stat-period" id="maintenancePeriod">Últimos 30 dias</div>
                        </div>
                        <div class="stat-card-footer">
                            <div class="stat-comparison">
                                <span class="comparison-label">vs. período anterior:</span>
                                <span class="comparison-value positive">+3</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card info">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="stat-trend">
                                <span class="trend-indicator up">
                                    <i class="fas fa-arrow-up"></i> 8.7%
                                </span>
                            </div>
                        </div>
                        <div class="stat-card-body">
                            <div class="stat-value" id="totalKilometers">45,280 km</div>
                            <div class="stat-label">Quilometragem</div>
                            <div class="stat-period" id="kmPeriod">Últimos 30 dias</div>
                        </div>
                        <div class="stat-card-footer">
                            <div class="stat-comparison">
                                <span class="comparison-label">vs. período anterior:</span>
                                <span class="comparison-value positive">+3,620km</span>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Statistics -->
                    <div class="enhanced-stat-card secondary">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="stat-trend">
                                <span class="trend-indicator up">
                                    <i class="fas fa-arrow-up"></i> 2.3%
                                </span>
                            </div>
                        </div>
                        <div class="stat-card-body">
                            <div class="stat-value" id="avgEfficiency">12.8 km/L</div>
                            <div class="stat-label">Eficiência Média</div>
                            <div class="stat-period">Últimos 30 dias</div>
                        </div>
                        <div class="stat-card-footer">
                            <div class="stat-comparison">
                                <span class="comparison-label">vs. período anterior:</span>
                                <span class="comparison-value positive">+0.3km/L</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card danger">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-trend">
                                <span class="trend-indicator down">
                                    <i class="fas fa-arrow-down"></i> 25.0%
                                </span>
                            </div>
                        </div>
                        <div class="stat-card-body">
                            <div class="stat-value" id="totalAlerts">6</div>
                            <div class="stat-label">Alertas Ativos</div>
                            <div class="stat-period">Últimos 30 dias</div>
                        </div>
                        <div class="stat-card-footer">
                            <div class="stat-comparison">
                                <span class="comparison-label">vs. período anterior:</span>
                                <span class="comparison-value negative">-2</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/graficos.js"></script>
    <script src="js/enhanced-dashboard.js"></script>
    <script src="fix-graficos.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
