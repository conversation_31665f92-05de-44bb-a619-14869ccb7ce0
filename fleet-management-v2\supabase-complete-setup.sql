-- ========================================
-- SCRIPT COMPLETO DE SETUP DO SUPABASE
-- Sistema de Gestão de Frotas v2.0
-- ========================================

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================
-- CRIAÇÃO DAS TABELAS
-- ========================================

-- Tabela de usuários (estende auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de veículos
CREATE TABLE public.vehicles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    placa TEXT NOT NULL,
    modelo TEXT NOT NULL,
    marca TEXT NOT NULL,
    ano INTEGER NOT NULL,
    cor TEXT,
    combustivel TEXT NOT NULL,
    km_atual INTEGER DEFAULT 0,
    status TEXT DEFAULT 'ativo' CHECK (status IN ('ativo', 'inativo', 'manutencao')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de fornecedores
CREATE TABLE public.suppliers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    nome TEXT NOT NULL,
    tipo TEXT NOT NULL CHECK (tipo IN ('posto', 'oficina', 'lavajato', 'outros')),
    endereco TEXT,
    telefone TEXT,
    email TEXT,
    cnpj TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de abastecimentos
CREATE TABLE public.fuel_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES public.suppliers(id),
    data_abastecimento TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    litros DECIMAL(10,3) NOT NULL,
    valor_litro DECIMAL(10,3) NOT NULL,
    valor_total DECIMAL(10,2) NOT NULL,
    motorista TEXT,
    cupom_fiscal TEXT,
    nota_fiscal TEXT,
    status_data TIMESTAMP WITH TIME ZONE,
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de manutenções
CREATE TABLE public.maintenance_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES public.suppliers(id),
    data_manutencao TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    tipo_manutencao TEXT NOT NULL,
    descricao TEXT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'concluida' CHECK (status IN ('agendada', 'em_andamento', 'concluida', 'cancelada')),
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de lavagens
CREATE TABLE public.washing_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    data_lavagem TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    tipo_lavagem TEXT NOT NULL,
    local_lavagem TEXT NOT NULL CHECK (local_lavagem IN ('interno', 'externo')),
    nome_lavajato TEXT,
    endereco_lavajato TEXT,
    responsavel TEXT,
    valor DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'realizada' CHECK (status IN ('agendada', 'realizada', 'cancelada')),
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de revisões
CREATE TABLE public.revision_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES public.suppliers(id),
    data_revisao TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    km_proxima_revisao INTEGER,
    tipo_revisao TEXT NOT NULL,
    itens_verificados TEXT[],
    valor DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'concluida' CHECK (status IN ('agendada', 'concluida', 'cancelada')),
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de controle de caixa
CREATE TABLE public.cash_flow (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    data_transacao TIMESTAMP WITH TIME ZONE NOT NULL,
    tipo TEXT NOT NULL CHECK (tipo IN ('entrada', 'saida')),
    categoria TEXT NOT NULL,
    descricao TEXT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    referencia_id UUID,
    referencia_tipo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- ÍNDICES PARA PERFORMANCE
-- ========================================

CREATE INDEX idx_vehicles_user_id ON public.vehicles(user_id);
CREATE INDEX idx_vehicles_placa ON public.vehicles(placa);
CREATE INDEX idx_fuel_records_user_id ON public.fuel_records(user_id);
CREATE INDEX idx_fuel_records_vehicle_id ON public.fuel_records(vehicle_id);
CREATE INDEX idx_fuel_records_data ON public.fuel_records(data_abastecimento);
CREATE INDEX idx_maintenance_records_user_id ON public.maintenance_records(user_id);
CREATE INDEX idx_maintenance_records_vehicle_id ON public.maintenance_records(vehicle_id);
CREATE INDEX idx_washing_records_user_id ON public.washing_records(user_id);
CREATE INDEX idx_washing_records_vehicle_id ON public.washing_records(vehicle_id);
CREATE INDEX idx_revision_records_user_id ON public.revision_records(user_id);
CREATE INDEX idx_revision_records_vehicle_id ON public.revision_records(vehicle_id);
CREATE INDEX idx_cash_flow_user_id ON public.cash_flow(user_id);
CREATE INDEX idx_cash_flow_data ON public.cash_flow(data_transacao);

-- ========================================
-- TRIGGERS PARA UPDATED_AT
-- ========================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON public.vehicles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON public.suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_fuel_records_updated_at BEFORE UPDATE ON public.fuel_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON public.maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_washing_records_updated_at BEFORE UPDATE ON public.washing_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_revision_records_updated_at BEFORE UPDATE ON public.revision_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cash_flow_updated_at BEFORE UPDATE ON public.cash_flow FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
