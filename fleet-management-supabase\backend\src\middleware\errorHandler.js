// Middleware para tratamento de erros
const errorHandler = (err, req, res, next) => {
  console.error('Erro capturado:', err);

  // Erro de validação do Joi
  if (err.isJoi) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: err.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }

  // Erro do Supabase
  if (err.code) {
    switch (err.code) {
      case '23505': // Unique violation
        return res.status(409).json({
          error: 'Dados duplicados',
          message: 'Este registro já existe'
        });
      case '23503': // Foreign key violation
        return res.status(400).json({
          error: 'Referência inválida',
          message: 'Registro referenciado não existe'
        });
      case '23502': // Not null violation
        return res.status(400).json({
          error: 'Campo obrigatório',
          message: 'Campos obrigatórios não podem estar vazios'
        });
      default:
        return res.status(500).json({
          error: 'Erro de banco de dados',
          message: process.env.NODE_ENV === 'development' ? err.message : 'Erro interno'
        });
    }
  }

  // Erro de autenticação
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Token inválido',
      message: 'Token de acesso inválido ou expirado'
    });
  }

  // Erro de sintaxe JSON
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json({
      error: 'JSON inválido',
      message: 'Formato de dados inválido'
    });
  }

  // Erro padrão
  const status = err.status || err.statusCode || 500;
  const message = err.message || 'Erro interno do servidor';

  res.status(status).json({
    error: status >= 500 ? 'Erro interno do servidor' : message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      details: err
    })
  });
};

module.exports = errorHandler;
