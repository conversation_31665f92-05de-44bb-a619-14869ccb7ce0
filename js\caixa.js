class CaixaSystem {
    constructor() {
        this.transacoes = [];
        this.saldoInicial = 0;
        this.init();
    }

    init() {
        this.loadTransacoes();
        this.setupEventListeners();
        this.populateYearFilter();
        this.setDefaultDates();
        this.sincronizarDados();
        this.updateDisplay();
    }

    setupEventListeners() {
        document.getElementById('entradaForm').addEventListener('submit', (e) => this.handleEntrada(e));
        document.getElementById('saidaForm').addEventListener('submit', (e) => this.handleSaida(e));
        document.getElementById('filtroMes').addEventListener('change', () => this.aplicarFiltros());
        document.getElementById('filtroAno').addEventListener('change', () => this.aplicarFiltros());
        document.getElementById('filtroTipo').addEventListener('change', () => this.aplicarFiltros());
    }

    setDefaultDates() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('dataEntrada').value = today;
        document.getElementById('dataSaida').value = today;
    }

    populateYearFilter() {
        const yearSelect = document.getElementById('filtroAno');
        const currentYear = new Date().getFullYear();
        
        for (let year = currentYear - 2; year <= currentYear + 1; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            if (year === currentYear) option.selected = true;
            yearSelect.appendChild(option);
        }
    }

    loadTransacoes() {
        const saved = localStorage.getItem('caixa_transacoes');
        if (saved) {
            try {
                this.transacoes = JSON.parse(saved);
            } catch (error) {
                console.error('Erro ao carregar transações:', error);
                this.transacoes = [];
            }
        }

        const saldoSaved = localStorage.getItem('caixa_saldo_inicial');
        if (saldoSaved) {
            this.saldoInicial = parseFloat(saldoSaved) || 0;
        }
    }

    saveTransacoes() {
        localStorage.setItem('caixa_transacoes', JSON.stringify(this.transacoes));
        localStorage.setItem('caixa_saldo_inicial', this.saldoInicial.toString());
    }

    handleEntrada(e) {
        e.preventDefault();
        
        const valor = parseFloat(document.getElementById('valorEntrada').value);
        const descricao = document.getElementById('descricaoEntrada').value;
        const data = document.getElementById('dataEntrada').value;

        const transacao = {
            id: Date.now(),
            tipo: 'entrada',
            valor: valor,
            descricao: descricao,
            data: data,
            origem: 'manual',
            timestamp: new Date().toISOString()
        };

        this.transacoes.push(transacao);
        this.saveTransacoes();
        this.updateDisplay();
        
        // Limpar formulário
        e.target.reset();
        this.setDefaultDates();
        
        alert('Entrada registrada com sucesso!');
    }

    handleSaida(e) {
        e.preventDefault();
        
        const valor = parseFloat(document.getElementById('valorSaida').value);
        const descricao = document.getElementById('descricaoSaida').value;
        const data = document.getElementById('dataSaida').value;

        const transacao = {
            id: Date.now(),
            tipo: 'saida',
            valor: valor,
            descricao: descricao,
            data: data,
            origem: 'manual',
            timestamp: new Date().toISOString()
        };

        this.transacoes.push(transacao);
        this.saveTransacoes();
        this.updateDisplay();
        
        // Limpar formulário
        e.target.reset();
        this.setDefaultDates();
        
        alert('Saída registrada com sucesso!');
    }

    sincronizarDados() {
        console.log('🔄 Sincronizando dados do sistema...');
        
        // Remover transações automáticas antigas
        this.transacoes = this.transacoes.filter(t => t.origem === 'manual');
        
        // Sincronizar abastecimentos
        this.sincronizarAbastecimentos();
        
        // Sincronizar manutenções
        this.sincronizarManutencoes();
        
        // Sincronizar lavagens
        this.sincronizarLavagens();
        
        this.saveTransacoes();
        console.log('✅ Sincronização concluída!');
    }

    sincronizarAbastecimentos() {
        const abastecimentos = JSON.parse(localStorage.getItem('abastecimentos') || '[]');
        
        abastecimentos.forEach(abast => {
            const transacao = {
                id: `abast_${abast.id}`,
                tipo: 'saida',
                valor: abast.valorTotal,
                descricao: `Abastecimento - ${abast.posto} (${abast.litros}L)`,
                data: abast.data,
                origem: 'abastecimento',
                timestamp: abast.timestamp || new Date().toISOString(),
                veiculoId: abast.veiculoId
            };
            
            // Verificar se já existe
            if (!this.transacoes.find(t => t.id === transacao.id)) {
                this.transacoes.push(transacao);
            }
        });
    }

    sincronizarManutencoes() {
        const manutencoes = JSON.parse(localStorage.getItem('manutencoes') || '[]');
        
        manutencoes.forEach(manut => {
            const valor = manut.valorReal || manut.valorEstimado;
            if (valor && valor > 0) {
                const transacao = {
                    id: `manut_${manut.id}`,
                    tipo: 'saida',
                    valor: valor,
                    descricao: `Manutenção - ${manut.oficina} (${manut.problema})`,
                    data: manut.dataAgendamento,
                    origem: 'manutencao',
                    timestamp: manut.timestamp || new Date().toISOString(),
                    veiculoId: manut.veiculoId
                };
                
                // Verificar se já existe
                if (!this.transacoes.find(t => t.id === transacao.id)) {
                    this.transacoes.push(transacao);
                }
            }
        });
    }

    sincronizarLavagens() {
        const lavagens = JSON.parse(localStorage.getItem('lavagens') || '[]');
        
        lavagens.forEach(lavagem => {
            if (lavagem.valor && lavagem.valor > 0) {
                const transacao = {
                    id: `lavagem_${lavagem.id}`,
                    tipo: 'saida',
                    valor: lavagem.valor,
                    descricao: `Lavagem - ${lavagem.local} (${lavagem.tipoLavagem})`,
                    data: lavagem.dataLavagem,
                    origem: 'lavagem',
                    timestamp: lavagem.dataRegistro || new Date().toISOString(),
                    veiculoId: lavagem.veiculoId
                };
                
                // Verificar se já existe
                if (!this.transacoes.find(t => t.id === transacao.id)) {
                    this.transacoes.push(transacao);
                }
            }
        });
    }

    aplicarFiltros() {
        const mes = document.getElementById('filtroMes').value;
        const ano = document.getElementById('filtroAno').value;
        const tipo = document.getElementById('filtroTipo').value;

        let transacoesFiltradas = [...this.transacoes];

        if (mes) {
            transacoesFiltradas = transacoesFiltradas.filter(t => {
                const dataMes = new Date(t.data).getMonth() + 1;
                return dataMes.toString().padStart(2, '0') === mes;
            });
        }

        if (ano) {
            transacoesFiltradas = transacoesFiltradas.filter(t => {
                const dataAno = new Date(t.data).getFullYear();
                return dataAno.toString() === ano;
            });
        }

        if (tipo) {
            if (tipo === 'entrada' || tipo === 'saida') {
                transacoesFiltradas = transacoesFiltradas.filter(t => t.tipo === tipo);
            } else {
                transacoesFiltradas = transacoesFiltradas.filter(t => t.origem === tipo);
            }
        }

        this.updateDisplay(transacoesFiltradas);
    }

    limparFiltros() {
        document.getElementById('filtroMes').value = '';
        document.getElementById('filtroAno').value = '';
        document.getElementById('filtroTipo').value = '';
        this.updateDisplay();
    }

    updateDisplay(transacoesFiltradas = null) {
        const transacoes = transacoesFiltradas || this.transacoes;
        
        this.updateSaldos(transacoes);
        this.updateHistorico(transacoes);
        this.updateResumoMensal(transacoes);
    }

    updateSaldos(transacoes) {
        const totalEntradas = transacoes
            .filter(t => t.tipo === 'entrada')
            .reduce((sum, t) => sum + t.valor, 0);
            
        const totalSaidas = transacoes
            .filter(t => t.tipo === 'saida')
            .reduce((sum, t) => sum + t.valor, 0);
            
        const saldoAtual = this.saldoInicial + totalEntradas - totalSaidas;
        
        document.getElementById('saldoAtual').textContent = Utils.formatCurrency(saldoAtual);
        document.getElementById('totalEntradas').textContent = Utils.formatCurrency(totalEntradas);
        document.getElementById('totalSaidas').textContent = Utils.formatCurrency(totalSaidas);
        document.getElementById('totalTransacoes').textContent = transacoes.length;
        
        // Aplicar cor baseada no saldo
        const saldoElement = document.getElementById('saldoAtual');
        saldoElement.className = saldoAtual >= 0 ? 'balance-amount balance-positive' : 'balance-amount balance-negative';
    }

    updateHistorico(transacoes) {
        const tbody = document.getElementById('historicoTransacoes');
        tbody.innerHTML = '';

        // Ordenar por data (mais recente primeiro)
        const transacoesOrdenadas = transacoes.sort((a, b) => new Date(b.data) - new Date(a.data));

        let saldoAcumulado = this.saldoInicial;

        // Calcular saldo acumulado (do mais antigo para o mais recente)
        const transacoesComSaldo = transacoesOrdenadas.reverse().map(transacao => {
            if (transacao.tipo === 'entrada') {
                saldoAcumulado += transacao.valor;
            } else {
                saldoAcumulado -= transacao.valor;
            }
            return { ...transacao, saldoAcumulado };
        });

        // Reverter para mostrar mais recente primeiro
        transacoesComSaldo.reverse().forEach(transacao => {
            const row = document.createElement('tr');
            row.className = transacao.tipo === 'entrada' ? 'transaction-income' : 'transaction-expense';

            const tipoIcon = this.getTipoIcon(transacao.origem || transacao.tipo);
            const tipoText = this.getTipoText(transacao.origem || transacao.tipo);

            row.innerHTML = `
                <td>${Utils.formatDate(transacao.data)}</td>
                <td>
                    <span class="badge ${transacao.tipo === 'entrada' ? 'bg-success' : 'bg-danger'}">
                        ${tipoIcon} ${tipoText}
                    </span>
                </td>
                <td>${transacao.descricao}</td>
                <td class="text-success">${transacao.tipo === 'entrada' ? Utils.formatCurrency(transacao.valor) : '-'}</td>
                <td class="text-danger">${transacao.tipo === 'saida' ? Utils.formatCurrency(transacao.valor) : '-'}</td>
                <td class="${transacao.saldoAcumulado >= 0 ? 'text-success' : 'text-danger'}">
                    ${Utils.formatCurrency(transacao.saldoAcumulado)}
                </td>
                <td>
                    ${transacao.origem === 'manual' ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="caixaSystem.deleteTransacao('${transacao.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : `
                        <span class="text-muted">
                            <i class="fas fa-sync-alt" title="Sincronizado automaticamente"></i>
                        </span>
                    `}
                </td>
            `;

            tbody.appendChild(row);
        });

        if (transacoes.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        Nenhuma transação encontrada
                    </td>
                </tr>
            `;
        }
    }

    updateResumoMensal(transacoes) {
        const resumoContainer = document.getElementById('resumoMensal');

        // Agrupar por mês/ano
        const resumoPorMes = {};

        transacoes.forEach(transacao => {
            const data = new Date(transacao.data);
            const chave = `${data.getFullYear()}-${(data.getMonth() + 1).toString().padStart(2, '0')}`;

            if (!resumoPorMes[chave]) {
                resumoPorMes[chave] = {
                    mes: data.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' }),
                    entradas: 0,
                    saidas: 0,
                    transacoes: 0
                };
            }

            if (transacao.tipo === 'entrada') {
                resumoPorMes[chave].entradas += transacao.valor;
            } else {
                resumoPorMes[chave].saidas += transacao.valor;
            }
            resumoPorMes[chave].transacoes++;
        });

        // Ordenar por data (mais recente primeiro)
        const mesesOrdenados = Object.keys(resumoPorMes).sort().reverse();

        if (mesesOrdenados.length === 0) {
            resumoContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-calendar-times fa-2x mb-2"></i><br>
                    Nenhum dado mensal disponível
                </div>
            `;
            return;
        }

        resumoContainer.innerHTML = mesesOrdenados.map(chave => {
            const resumo = resumoPorMes[chave];
            const saldo = resumo.entradas - resumo.saidas;

            return `
                <div class="monthly-summary">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-primary">${resumo.mes}</h6>
                            <small class="text-muted">${resumo.transacoes} transações</small>
                        </div>
                        <div class="col-md-3">
                            <div class="text-success">
                                <i class="fas fa-arrow-up"></i> Entradas<br>
                                <strong>${Utils.formatCurrency(resumo.entradas)}</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-danger">
                                <i class="fas fa-arrow-down"></i> Saídas<br>
                                <strong>${Utils.formatCurrency(resumo.saidas)}</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="${saldo >= 0 ? 'text-success' : 'text-danger'}">
                                <i class="fas fa-balance-scale"></i> Saldo<br>
                                <strong>${Utils.formatCurrency(saldo)}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    getTipoIcon(tipo) {
        const icons = {
            'entrada': '<i class="fas fa-plus-circle"></i>',
            'saida': '<i class="fas fa-minus-circle"></i>',
            'manual': '<i class="fas fa-hand-paper"></i>',
            'abastecimento': '<i class="fas fa-gas-pump"></i>',
            'manutencao': '<i class="fas fa-wrench"></i>',
            'lavagem': '<i class="fas fa-car-wash"></i>'
        };
        return icons[tipo] || '<i class="fas fa-circle"></i>';
    }

    getTipoText(tipo) {
        const texts = {
            'entrada': 'Entrada',
            'saida': 'Saída',
            'manual': 'Manual',
            'abastecimento': 'Abastecimento',
            'manutencao': 'Manutenção',
            'lavagem': 'Lavagem'
        };
        return texts[tipo] || 'Outros';
    }

    deleteTransacao(id) {
        if (confirm('Deseja realmente excluir esta transação?')) {
            this.transacoes = this.transacoes.filter(t => t.id !== id);
            this.saveTransacoes();
            this.updateDisplay();
            alert('Transação excluída com sucesso!');
        }
    }

    exportarDados() {
        const dados = {
            saldoInicial: this.saldoInicial,
            transacoes: this.transacoes,
            dataExportacao: new Date().toISOString()
        };

        const dataStr = JSON.stringify(dados, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `caixa_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    definirSaldoInicial() {
        const novoSaldo = prompt('Digite o saldo inicial do caixa:', this.saldoInicial.toFixed(2));

        if (novoSaldo !== null) {
            const valor = parseFloat(novoSaldo);
            if (!isNaN(valor)) {
                this.saldoInicial = valor;
                this.saveTransacoes();
                this.updateDisplay();
                alert('Saldo inicial definido com sucesso!');
            } else {
                alert('Valor inválido. Digite um número válido.');
            }
        }
    }
}

// Funções globais
let caixaSystem;

document.addEventListener('DOMContentLoaded', function() {
    caixaSystem = new CaixaSystem();
});

function aplicarFiltros() {
    caixaSystem.aplicarFiltros();
}

function limparFiltros() {
    caixaSystem.limparFiltros();
}

function sincronizarDados() {
    caixaSystem.sincronizarDados();
    caixaSystem.updateDisplay();
    alert('Dados sincronizados com sucesso!');
}

function exportarDados() {
    caixaSystem.exportarDados();
}

function definirSaldoInicial() {
    caixaSystem.definirSaldoInicial();
}
