class CaixaSystem {
    constructor() {
        this.transacoes = [];
        this.saldoInicial = 0;
        this.charts = {};
        this.metricas = {};
        this.init();
    }

    init() {
        this.loadTransacoes();
        this.setupEventListeners();
        this.populateYearFilter();
        this.setDefaultDates();
        this.sincronizarDados();
        this.updateDisplay();
    }

    setupEventListeners() {
        document.getElementById('entradaForm').addEventListener('submit', (e) => this.handleEntrada(e));
        document.getElementById('saidaForm').addEventListener('submit', (e) => this.handleSaida(e));
        document.getElementById('filtroMes').addEventListener('change', () => this.aplicarFiltros());
        document.getElementById('filtroAno').addEventListener('change', () => this.aplicarFiltros());
        document.getElementById('filtroTipo').addEventListener('change', () => this.aplicarFiltros());
    }

    setDefaultDates() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('dataEntrada').value = today;
        document.getElementById('dataSaida').value = today;
    }

    populateYearFilter() {
        const yearSelect = document.getElementById('filtroAno');
        const currentYear = new Date().getFullYear();
        
        for (let year = currentYear - 2; year <= currentYear + 1; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            if (year === currentYear) option.selected = true;
            yearSelect.appendChild(option);
        }
    }

    loadTransacoes() {
        const saved = localStorage.getItem('caixa_transacoes');
        if (saved) {
            try {
                this.transacoes = JSON.parse(saved);
            } catch (error) {
                console.error('Erro ao carregar transações:', error);
                this.transacoes = [];
            }
        }

        const saldoSaved = localStorage.getItem('caixa_saldo_inicial');
        if (saldoSaved) {
            this.saldoInicial = parseFloat(saldoSaved) || 0;
        }
    }

    saveTransacoes() {
        localStorage.setItem('caixa_transacoes', JSON.stringify(this.transacoes));
        localStorage.setItem('caixa_saldo_inicial', this.saldoInicial.toString());
    }

    handleEntrada(e) {
        e.preventDefault();
        
        const valor = parseFloat(document.getElementById('valorEntrada').value);
        const descricao = document.getElementById('descricaoEntrada').value;
        const data = document.getElementById('dataEntrada').value;

        const transacao = {
            id: Date.now(),
            tipo: 'entrada',
            valor: valor,
            descricao: descricao,
            data: data,
            origem: 'manual',
            timestamp: new Date().toISOString()
        };

        this.transacoes.push(transacao);
        this.saveTransacoes();
        this.updateDisplay();
        
        // Limpar formulário
        e.target.reset();
        this.setDefaultDates();
        
        alert('Entrada registrada com sucesso!');
    }

    handleSaida(e) {
        e.preventDefault();
        
        const valor = parseFloat(document.getElementById('valorSaida').value);
        const descricao = document.getElementById('descricaoSaida').value;
        const data = document.getElementById('dataSaida').value;

        const transacao = {
            id: Date.now(),
            tipo: 'saida',
            valor: valor,
            descricao: descricao,
            data: data,
            origem: 'manual',
            timestamp: new Date().toISOString()
        };

        this.transacoes.push(transacao);
        this.saveTransacoes();
        this.updateDisplay();
        
        // Limpar formulário
        e.target.reset();
        this.setDefaultDates();
        
        alert('Saída registrada com sucesso!');
    }

    sincronizarDados() {
        console.log('🔄 Sincronizando dados do sistema...');
        
        // Remover transações automáticas antigas
        this.transacoes = this.transacoes.filter(t => t.origem === 'manual');
        
        // Sincronizar abastecimentos
        this.sincronizarAbastecimentos();
        
        // Sincronizar manutenções
        this.sincronizarManutencoes();
        
        // Sincronizar lavagens
        this.sincronizarLavagens();
        
        this.saveTransacoes();
        console.log('✅ Sincronização concluída!');
    }

    sincronizarAbastecimentos() {
        const abastecimentos = JSON.parse(localStorage.getItem('abastecimentos') || '[]');
        
        abastecimentos.forEach(abast => {
            const transacao = {
                id: `abast_${abast.id}`,
                tipo: 'saida',
                valor: abast.valorTotal,
                descricao: `Abastecimento - ${abast.posto} (${abast.litros}L)`,
                data: abast.data,
                origem: 'abastecimento',
                timestamp: abast.timestamp || new Date().toISOString(),
                veiculoId: abast.veiculoId
            };
            
            // Verificar se já existe
            if (!this.transacoes.find(t => t.id === transacao.id)) {
                this.transacoes.push(transacao);
            }
        });
    }

    sincronizarManutencoes() {
        const manutencoes = JSON.parse(localStorage.getItem('manutencoes') || '[]');
        
        manutencoes.forEach(manut => {
            const valor = manut.valorReal || manut.valorEstimado;
            if (valor && valor > 0) {
                const transacao = {
                    id: `manut_${manut.id}`,
                    tipo: 'saida',
                    valor: valor,
                    descricao: `Manutenção - ${manut.oficina} (${manut.problema})`,
                    data: manut.dataAgendamento,
                    origem: 'manutencao',
                    timestamp: manut.timestamp || new Date().toISOString(),
                    veiculoId: manut.veiculoId
                };
                
                // Verificar se já existe
                if (!this.transacoes.find(t => t.id === transacao.id)) {
                    this.transacoes.push(transacao);
                }
            }
        });
    }

    sincronizarLavagens() {
        const lavagens = JSON.parse(localStorage.getItem('lavagens') || '[]');
        
        lavagens.forEach(lavagem => {
            if (lavagem.valor && lavagem.valor > 0) {
                const transacao = {
                    id: `lavagem_${lavagem.id}`,
                    tipo: 'saida',
                    valor: lavagem.valor,
                    descricao: `Lavagem - ${lavagem.local} (${lavagem.tipoLavagem})`,
                    data: lavagem.dataLavagem,
                    origem: 'lavagem',
                    timestamp: lavagem.dataRegistro || new Date().toISOString(),
                    veiculoId: lavagem.veiculoId
                };
                
                // Verificar se já existe
                if (!this.transacoes.find(t => t.id === transacao.id)) {
                    this.transacoes.push(transacao);
                }
            }
        });
    }

    aplicarFiltros() {
        const mes = document.getElementById('filtroMes').value;
        const ano = document.getElementById('filtroAno').value;
        const tipo = document.getElementById('filtroTipo').value;

        let transacoesFiltradas = [...this.transacoes];

        if (mes) {
            transacoesFiltradas = transacoesFiltradas.filter(t => {
                const dataMes = new Date(t.data).getMonth() + 1;
                return dataMes.toString().padStart(2, '0') === mes;
            });
        }

        if (ano) {
            transacoesFiltradas = transacoesFiltradas.filter(t => {
                const dataAno = new Date(t.data).getFullYear();
                return dataAno.toString() === ano;
            });
        }

        if (tipo) {
            if (tipo === 'entrada' || tipo === 'saida') {
                transacoesFiltradas = transacoesFiltradas.filter(t => t.tipo === tipo);
            } else {
                transacoesFiltradas = transacoesFiltradas.filter(t => t.origem === tipo);
            }
        }

        this.updateDisplay(transacoesFiltradas);
    }

    limparFiltros() {
        document.getElementById('filtroMes').value = '';
        document.getElementById('filtroAno').value = '';
        document.getElementById('filtroTipo').value = '';
        this.updateDisplay();
    }

    updateDisplay(transacoesFiltradas = null) {
        const transacoes = transacoesFiltradas || this.transacoes;

        this.updateSaldos(transacoes);
        this.updateEstatisticasRapidas(transacoes);
        this.updateMetricasAvancadas(transacoes);
        this.updateCharts(transacoes);
        this.updateAlertas(transacoes);
        this.updateInsights(transacoes);
        this.updateHistorico(transacoes);
        this.updateResumoMensal(transacoes);
    }

    updateSaldos(transacoes) {
        const totalEntradas = transacoes
            .filter(t => t.tipo === 'entrada')
            .reduce((sum, t) => sum + t.valor, 0);
            
        const totalSaidas = transacoes
            .filter(t => t.tipo === 'saida')
            .reduce((sum, t) => sum + t.valor, 0);
            
        const saldoAtual = this.saldoInicial + totalEntradas - totalSaidas;
        
        document.getElementById('saldoAtual').textContent = Utils.formatCurrency(saldoAtual);
        document.getElementById('totalEntradas').textContent = Utils.formatCurrency(totalEntradas);
        document.getElementById('totalSaidas').textContent = Utils.formatCurrency(totalSaidas);
        document.getElementById('totalTransacoes').textContent = transacoes.length;
        
        // Aplicar cor baseada no saldo
        const saldoElement = document.getElementById('saldoAtual');
        saldoElement.className = saldoAtual >= 0 ? 'balance-amount balance-positive' : 'balance-amount balance-negative';

        // Calcular média mensal
        const mesesUnicos = [...new Set(transacoes.map(t => t.data.substring(0, 7)))];
        const mediaMensal = mesesUnicos.length > 0 ? (totalEntradas - totalSaidas) / mesesUnicos.length : 0;
        document.getElementById('mediaMensal').textContent = Utils.formatCurrency(Math.abs(mediaMensal));
    }

    updateEstatisticasRapidas(transacoes) {
        const hoje = new Date().toISOString().split('T')[0];

        // Transações hoje
        const transacoesHoje = transacoes.filter(t => t.data === hoje).length;
        document.getElementById('transacoesHoje').textContent = transacoesHoje;

        // Gasto médio por dia (últimos 30 dias)
        const dataLimite = new Date();
        dataLimite.setDate(dataLimite.getDate() - 30);

        const gastosUltimos30Dias = transacoes
            .filter(t => t.tipo === 'saida' && new Date(t.data) >= dataLimite)
            .reduce((sum, t) => sum + t.valor, 0);

        const gastoMedioDia = gastosUltimos30Dias / 30;
        document.getElementById('gastoMedioDia').textContent = Utils.formatCurrency(gastoMedioDia);

        // Dias até zerar (baseado no gasto médio)
        const saldoAtual = this.calcularSaldoAtual(transacoes);
        const diasAteZerar = gastoMedioDia > 0 ? Math.floor(saldoAtual / gastoMedioDia) : Infinity;

        if (diasAteZerar === Infinity || diasAteZerar < 0) {
            document.getElementById('diasAteZerar').textContent = '∞';
        } else {
            document.getElementById('diasAteZerar').textContent = diasAteZerar + ' dias';
        }

        // Economia mensal (diferença entre entrada e saída do mês atual)
        const mesAtual = new Date().toISOString().substring(0, 7);
        const transacoesMesAtual = transacoes.filter(t => t.data.startsWith(mesAtual));

        const entradasMes = transacoesMesAtual.filter(t => t.tipo === 'entrada').reduce((sum, t) => sum + t.valor, 0);
        const saidasMes = transacoesMesAtual.filter(t => t.tipo === 'saida').reduce((sum, t) => sum + t.valor, 0);
        const economiaMensal = entradasMes - saidasMes;

        document.getElementById('economiaMensal').textContent = Utils.formatCurrency(Math.abs(economiaMensal));
        document.getElementById('economiaMensal').className = economiaMensal >= 0 ? 'h5 text-success' : 'h5 text-danger';

        // Status financeiro
        const statusElement = document.getElementById('statusFinanceiro');
        let statusClass = 'bg-success';
        let statusText = 'Saudável';

        if (saldoAtual < 0) {
            statusClass = 'bg-danger';
            statusText = 'Crítico';
        } else if (saldoAtual < 1000) {
            statusClass = 'bg-warning';
            statusText = 'Atenção';
        } else if (diasAteZerar < 30 && diasAteZerar > 0) {
            statusClass = 'bg-warning';
            statusText = 'Cuidado';
        }

        statusElement.innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;
    }

    updateMetricasAvancadas(transacoes) {
        // Maior entrada
        const entradas = transacoes.filter(t => t.tipo === 'entrada');
        const maiorEntrada = entradas.length > 0 ? entradas.reduce((max, t) => t.valor > max.valor ? t : max) : null;

        if (maiorEntrada) {
            document.getElementById('maiorEntrada').textContent = Utils.formatCurrency(maiorEntrada.valor);
            document.getElementById('dataMaiorEntrada').textContent = Utils.formatDate(maiorEntrada.data);
        }

        // Maior saída
        const saidas = transacoes.filter(t => t.tipo === 'saida');
        const maiorSaida = saidas.length > 0 ? saidas.reduce((max, t) => t.valor > max.valor ? t : max) : null;

        if (maiorSaida) {
            document.getElementById('maiorSaida').textContent = Utils.formatCurrency(maiorSaida.valor);
            document.getElementById('dataMaiorSaida').textContent = Utils.formatDate(maiorSaida.data);
        }

        // Gastos por categoria
        const totalSaidas = saidas.reduce((sum, t) => sum + t.valor, 0);

        const gastoCombustivel = transacoes
            .filter(t => t.origem === 'abastecimento')
            .reduce((sum, t) => sum + t.valor, 0);

        const gastoManutencao = transacoes
            .filter(t => t.origem === 'manutencao')
            .reduce((sum, t) => sum + t.valor, 0);

        document.getElementById('gastoCombustivel').textContent = Utils.formatCurrency(gastoCombustivel);
        document.getElementById('gastoManutencao').textContent = Utils.formatCurrency(gastoManutencao);

        if (totalSaidas > 0) {
            const percentualCombustivel = (gastoCombustivel / totalSaidas * 100).toFixed(1);
            const percentualManutencao = (gastoManutencao / totalSaidas * 100).toFixed(1);

            document.getElementById('percentualCombustivel').textContent = `${percentualCombustivel}% do total`;
            document.getElementById('percentualManutencao').textContent = `${percentualManutencao}% do total`;
        }
    }

    updateCharts(transacoes) {
        this.createSaldoChart(transacoes);
        this.createGastosChart(transacoes);
    }

    createSaldoChart(transacoes) {
        const ctx = document.getElementById('saldoChart');
        if (!ctx) return;

        // Destruir gráfico anterior se existir
        if (this.charts.saldo) {
            this.charts.saldo.destroy();
        }

        // Preparar dados por mês
        const dadosPorMes = {};
        let saldoAcumulado = this.saldoInicial;

        // Ordenar transações por data
        const transacoesOrdenadas = [...transacoes].sort((a, b) => new Date(a.data) - new Date(b.data));

        transacoesOrdenadas.forEach(transacao => {
            const mesAno = transacao.data.substring(0, 7);

            if (!dadosPorMes[mesAno]) {
                dadosPorMes[mesAno] = {
                    entradas: 0,
                    saidas: 0,
                    saldo: saldoAcumulado
                };
            }

            if (transacao.tipo === 'entrada') {
                dadosPorMes[mesAno].entradas += transacao.valor;
                saldoAcumulado += transacao.valor;
            } else {
                dadosPorMes[mesAno].saidas += transacao.valor;
                saldoAcumulado -= transacao.valor;
            }

            dadosPorMes[mesAno].saldo = saldoAcumulado;
        });

        const labels = Object.keys(dadosPorMes).sort().map(mes => {
            const [ano, mesNum] = mes.split('-');
            return new Date(ano, mesNum - 1).toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
        });

        const saldos = Object.keys(dadosPorMes).sort().map(mes => dadosPorMes[mes].saldo);
        const entradas = Object.keys(dadosPorMes).sort().map(mes => dadosPorMes[mes].entradas);
        const saidas = Object.keys(dadosPorMes).sort().map(mes => dadosPorMes[mes].saidas);

        this.charts.saldo = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Saldo',
                        data: saldos,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: 'Entradas',
                        data: entradas,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        fill: false,
                        tension: 0.4
                    },
                    {
                        label: 'Saídas',
                        data: saidas,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        fill: false,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + Utils.formatCurrency(context.parsed.y);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    createGastosChart(transacoes) {
        const ctx = document.getElementById('gastosChart');
        if (!ctx) return;

        // Destruir gráfico anterior se existir
        if (this.charts.gastos) {
            this.charts.gastos.destroy();
        }

        // Calcular gastos por categoria
        const gastosPorCategoria = {
            'Combustível': 0,
            'Manutenção': 0,
            'Lavagem': 0,
            'Manual': 0
        };

        transacoes.filter(t => t.tipo === 'saida').forEach(transacao => {
            switch (transacao.origem) {
                case 'abastecimento':
                    gastosPorCategoria['Combustível'] += transacao.valor;
                    break;
                case 'manutencao':
                    gastosPorCategoria['Manutenção'] += transacao.valor;
                    break;
                case 'lavagem':
                    gastosPorCategoria['Lavagem'] += transacao.valor;
                    break;
                default:
                    gastosPorCategoria['Manual'] += transacao.valor;
                    break;
            }
        });

        // Filtrar categorias com valores > 0
        const labels = [];
        const data = [];
        const colors = ['#ffc107', '#17a2b8', '#6f42c1', '#fd7e14'];
        const backgroundColors = [];

        let colorIndex = 0;
        Object.entries(gastosPorCategoria).forEach(([categoria, valor]) => {
            if (valor > 0) {
                labels.push(categoria);
                data.push(valor);
                backgroundColors.push(colors[colorIndex % colors.length]);
                colorIndex++;
            }
        });

        if (data.length === 0) {
            // Mostrar mensagem quando não há dados
            ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
            return;
        }

        this.charts.gastos = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + Utils.formatCurrency(context.parsed) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }

    updateAlertas(transacoes) {
        const alertasContainer = document.getElementById('alertasFinanceiros');
        if (!alertasContainer) return;

        const alertas = [];
        const agora = new Date();
        const mesAtual = agora.getMonth();
        const anoAtual = agora.getFullYear();

        // Calcular gastos do mês atual
        const gastosMesAtual = transacoes
            .filter(t => {
                const dataTransacao = new Date(t.data);
                return t.tipo === 'saida' &&
                       dataTransacao.getMonth() === mesAtual &&
                       dataTransacao.getFullYear() === anoAtual;
            })
            .reduce((sum, t) => sum + t.valor, 0);

        // Calcular média de gastos dos últimos 3 meses
        const ultimosTresMeses = [];
        for (let i = 1; i <= 3; i++) {
            const data = new Date(anoAtual, mesAtual - i, 1);
            ultimosTresMeses.push({
                mes: data.getMonth(),
                ano: data.getFullYear()
            });
        }

        const gastosUltimosTresMeses = ultimosTresMeses.map(periodo => {
            return transacoes
                .filter(t => {
                    const dataTransacao = new Date(t.data);
                    return t.tipo === 'saida' &&
                           dataTransacao.getMonth() === periodo.mes &&
                           dataTransacao.getFullYear() === periodo.ano;
                })
                .reduce((sum, t) => sum + t.valor, 0);
        });

        const mediaGastos = gastosUltimosTresMeses.reduce((sum, gasto) => sum + gasto, 0) / 3;

        // Alerta de gastos elevados
        if (gastosMesAtual > mediaGastos * 1.2) {
            alertas.push({
                tipo: 'warning',
                titulo: 'Gastos Elevados',
                mensagem: `Os gastos deste mês (${Utils.formatCurrency(gastosMesAtual)}) estão 20% acima da média dos últimos 3 meses.`
            });
        }

        // Alerta de saldo baixo
        const saldoAtual = this.calcularSaldoAtual(transacoes);
        if (saldoAtual < 1000 && saldoAtual > 0) {
            alertas.push({
                tipo: 'danger',
                titulo: 'Saldo Baixo',
                mensagem: `O saldo atual está baixo (${Utils.formatCurrency(saldoAtual)}). Considere fazer um aporte.`
            });
        }

        // Alerta de saldo negativo
        if (saldoAtual < 0) {
            alertas.push({
                tipo: 'danger',
                titulo: 'Saldo Negativo',
                mensagem: `O caixa está no vermelho (${Utils.formatCurrency(saldoAtual)}). Ação imediata necessária!`
            });
        }

        // Renderizar alertas
        if (alertas.length === 0) {
            alertasContainer.innerHTML = `
                <div class="alert alert-success alert-card">
                    <i class="fas fa-check-circle"></i>
                    <strong>Tudo certo!</strong> Não há alertas financeiros no momento.
                </div>
            `;
        } else {
            alertasContainer.innerHTML = alertas.map(alerta => `
                <div class="alert alert-${alerta.tipo} alert-card">
                    <i class="fas fa-${alerta.tipo === 'danger' ? 'exclamation-triangle' : 'exclamation-circle'}"></i>
                    <strong>${alerta.titulo}:</strong> ${alerta.mensagem}
                </div>
            `).join('');
        }
    }

    updateInsights(transacoes) {
        const insightsContainer = document.getElementById('insightsRecomendacoes');
        if (!insightsContainer) return;

        const insights = [];

        // Análise de padrões de gastos
        const gastosPorCategoria = this.calcularGastosPorCategoria(transacoes);
        const totalGastos = Object.values(gastosPorCategoria).reduce((sum, valor) => sum + valor, 0);

        if (totalGastos > 0) {
            const categoriaComMaiorGasto = Object.entries(gastosPorCategoria)
                .reduce((max, [categoria, valor]) => valor > max.valor ? { categoria, valor } : max, { categoria: '', valor: 0 });

            if (categoriaComMaiorGasto.valor > 0) {
                const percentual = (categoriaComMaiorGasto.valor / totalGastos * 100).toFixed(1);
                insights.push({
                    icone: 'chart-pie',
                    titulo: 'Maior Categoria de Gasto',
                    mensagem: `${categoriaComMaiorGasto.categoria} representa ${percentual}% dos seus gastos (${Utils.formatCurrency(categoriaComMaiorGasto.valor)}).`
                });
            }
        }

        // Análise de tendência
        const ultimosMeses = this.calcularTendenciaMensal(transacoes);
        if (ultimosMeses.length >= 2) {
            const mesAtual = ultimosMeses[ultimosMeses.length - 1];
            const mesAnterior = ultimosMeses[ultimosMeses.length - 2];

            const diferencaGastos = mesAtual.gastos - mesAnterior.gastos;
            const percentualMudanca = mesAnterior.gastos > 0 ? (diferencaGastos / mesAnterior.gastos * 100).toFixed(1) : 0;

            if (Math.abs(percentualMudanca) > 10) {
                insights.push({
                    icone: percentualMudanca > 0 ? 'trending-up' : 'trending-down',
                    titulo: 'Tendência de Gastos',
                    mensagem: `Seus gastos ${percentualMudanca > 0 ? 'aumentaram' : 'diminuíram'} ${Math.abs(percentualMudanca)}% em relação ao mês anterior.`
                });
            }
        }

        // Recomendação de economia
        if (gastosPorCategoria['Combustível'] > gastosPorCategoria['Manutenção'] * 3) {
            insights.push({
                icone: 'lightbulb',
                titulo: 'Oportunidade de Economia',
                mensagem: 'Considere revisar rotas e hábitos de condução para reduzir gastos com combustível.'
            });
        }

        // Renderizar insights
        if (insights.length === 0) {
            insightsContainer.innerHTML = `
                <div class="text-muted text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i><br>
                    Adicione mais transações para gerar insights personalizados.
                </div>
            `;
        } else {
            insightsContainer.innerHTML = insights.map(insight => `
                <div class="d-flex align-items-start mb-3">
                    <div class="me-3">
                        <i class="fas fa-${insight.icone} text-primary"></i>
                    </div>
                    <div>
                        <strong>${insight.titulo}:</strong><br>
                        <small class="text-muted">${insight.mensagem}</small>
                    </div>
                </div>
            `).join('');
        }
    }

    calcularSaldoAtual(transacoes) {
        const totalEntradas = transacoes.filter(t => t.tipo === 'entrada').reduce((sum, t) => sum + t.valor, 0);
        const totalSaidas = transacoes.filter(t => t.tipo === 'saida').reduce((sum, t) => sum + t.valor, 0);
        return this.saldoInicial + totalEntradas - totalSaidas;
    }

    calcularGastosPorCategoria(transacoes) {
        const gastos = {
            'Combustível': 0,
            'Manutenção': 0,
            'Lavagem': 0,
            'Manual': 0
        };

        transacoes.filter(t => t.tipo === 'saida').forEach(transacao => {
            switch (transacao.origem) {
                case 'abastecimento':
                    gastos['Combustível'] += transacao.valor;
                    break;
                case 'manutencao':
                    gastos['Manutenção'] += transacao.valor;
                    break;
                case 'lavagem':
                    gastos['Lavagem'] += transacao.valor;
                    break;
                default:
                    gastos['Manual'] += transacao.valor;
                    break;
            }
        });

        return gastos;
    }

    calcularTendenciaMensal(transacoes) {
        const dadosPorMes = {};

        transacoes.forEach(transacao => {
            const mesAno = transacao.data.substring(0, 7);

            if (!dadosPorMes[mesAno]) {
                dadosPorMes[mesAno] = { entradas: 0, gastos: 0 };
            }

            if (transacao.tipo === 'entrada') {
                dadosPorMes[mesAno].entradas += transacao.valor;
            } else {
                dadosPorMes[mesAno].gastos += transacao.valor;
            }
        });

        return Object.keys(dadosPorMes)
            .sort()
            .map(mes => ({
                mes,
                ...dadosPorMes[mes]
            }));
    }

    updateHistorico(transacoes) {
        const tbody = document.getElementById('historicoTransacoes');
        tbody.innerHTML = '';

        // Ordenar por data (mais recente primeiro)
        const transacoesOrdenadas = transacoes.sort((a, b) => new Date(b.data) - new Date(a.data));

        let saldoAcumulado = this.saldoInicial;

        // Calcular saldo acumulado (do mais antigo para o mais recente)
        const transacoesComSaldo = transacoesOrdenadas.reverse().map(transacao => {
            if (transacao.tipo === 'entrada') {
                saldoAcumulado += transacao.valor;
            } else {
                saldoAcumulado -= transacao.valor;
            }
            return { ...transacao, saldoAcumulado };
        });

        // Reverter para mostrar mais recente primeiro
        transacoesComSaldo.reverse().forEach(transacao => {
            const row = document.createElement('tr');
            row.className = transacao.tipo === 'entrada' ? 'transaction-income' : 'transaction-expense';

            const tipoIcon = this.getTipoIcon(transacao.origem || transacao.tipo);
            const tipoText = this.getTipoText(transacao.origem || transacao.tipo);

            row.innerHTML = `
                <td>${Utils.formatDate(transacao.data)}</td>
                <td>
                    <span class="badge ${transacao.tipo === 'entrada' ? 'bg-success' : 'bg-danger'}">
                        ${tipoIcon} ${tipoText}
                    </span>
                </td>
                <td>${transacao.descricao}</td>
                <td class="text-success">${transacao.tipo === 'entrada' ? Utils.formatCurrency(transacao.valor) : '-'}</td>
                <td class="text-danger">${transacao.tipo === 'saida' ? Utils.formatCurrency(transacao.valor) : '-'}</td>
                <td class="${transacao.saldoAcumulado >= 0 ? 'text-success' : 'text-danger'}">
                    ${Utils.formatCurrency(transacao.saldoAcumulado)}
                </td>
                <td>
                    ${transacao.origem === 'manual' ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="caixaSystem.deleteTransacao('${transacao.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : `
                        <span class="text-muted">
                            <i class="fas fa-sync-alt" title="Sincronizado automaticamente"></i>
                        </span>
                    `}
                </td>
            `;

            tbody.appendChild(row);
        });

        if (transacoes.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        Nenhuma transação encontrada
                    </td>
                </tr>
            `;
        }
    }

    updateResumoMensal(transacoes) {
        const resumoContainer = document.getElementById('resumoMensal');

        // Agrupar por mês/ano
        const resumoPorMes = {};

        transacoes.forEach(transacao => {
            const data = new Date(transacao.data);
            const chave = `${data.getFullYear()}-${(data.getMonth() + 1).toString().padStart(2, '0')}`;

            if (!resumoPorMes[chave]) {
                resumoPorMes[chave] = {
                    mes: data.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' }),
                    entradas: 0,
                    saidas: 0,
                    transacoes: 0
                };
            }

            if (transacao.tipo === 'entrada') {
                resumoPorMes[chave].entradas += transacao.valor;
            } else {
                resumoPorMes[chave].saidas += transacao.valor;
            }
            resumoPorMes[chave].transacoes++;
        });

        // Ordenar por data (mais recente primeiro)
        const mesesOrdenados = Object.keys(resumoPorMes).sort().reverse();

        if (mesesOrdenados.length === 0) {
            resumoContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-calendar-times fa-2x mb-2"></i><br>
                    Nenhum dado mensal disponível
                </div>
            `;
            return;
        }

        resumoContainer.innerHTML = mesesOrdenados.map(chave => {
            const resumo = resumoPorMes[chave];
            const saldo = resumo.entradas - resumo.saidas;

            return `
                <div class="monthly-summary">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-primary">${resumo.mes}</h6>
                            <small class="text-muted">${resumo.transacoes} transações</small>
                        </div>
                        <div class="col-md-3">
                            <div class="text-success">
                                <i class="fas fa-arrow-up"></i> Entradas<br>
                                <strong>${Utils.formatCurrency(resumo.entradas)}</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-danger">
                                <i class="fas fa-arrow-down"></i> Saídas<br>
                                <strong>${Utils.formatCurrency(resumo.saidas)}</strong>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="${saldo >= 0 ? 'text-success' : 'text-danger'}">
                                <i class="fas fa-balance-scale"></i> Saldo<br>
                                <strong>${Utils.formatCurrency(saldo)}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    getTipoIcon(tipo) {
        const icons = {
            'entrada': '<i class="fas fa-plus-circle"></i>',
            'saida': '<i class="fas fa-minus-circle"></i>',
            'manual': '<i class="fas fa-hand-paper"></i>',
            'abastecimento': '<i class="fas fa-gas-pump"></i>',
            'manutencao': '<i class="fas fa-wrench"></i>',
            'lavagem': '<i class="fas fa-car-wash"></i>'
        };
        return icons[tipo] || '<i class="fas fa-circle"></i>';
    }

    getTipoText(tipo) {
        const texts = {
            'entrada': 'Entrada',
            'saida': 'Saída',
            'manual': 'Manual',
            'abastecimento': 'Abastecimento',
            'manutencao': 'Manutenção',
            'lavagem': 'Lavagem'
        };
        return texts[tipo] || 'Outros';
    }

    deleteTransacao(id) {
        if (confirm('Deseja realmente excluir esta transação?')) {
            this.transacoes = this.transacoes.filter(t => t.id !== id);
            this.saveTransacoes();
            this.updateDisplay();
            alert('Transação excluída com sucesso!');
        }
    }

    exportarDados() {
        const dados = {
            saldoInicial: this.saldoInicial,
            transacoes: this.transacoes,
            dataExportacao: new Date().toISOString()
        };

        const dataStr = JSON.stringify(dados, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `caixa_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    definirSaldoInicial() {
        // Formatar valor atual para exibição
        const valorAtual = this.saldoInicial.toFixed(2).replace('.', ',');
        const novoSaldo = prompt('Digite o saldo inicial do caixa:\n(Use vírgula para decimais, ex: 1000,50)', valorAtual);

        if (novoSaldo !== null && novoSaldo.trim() !== '') {
            // Converter vírgula para ponto e remover espaços
            const valorLimpo = novoSaldo.trim().replace(',', '.');
            const valor = parseFloat(valorLimpo);

            if (!isNaN(valor) && isFinite(valor)) {
                this.saldoInicial = valor;
                this.saveTransacoes();
                this.updateDisplay();
                alert(`Saldo inicial definido com sucesso!\nNovo saldo: ${Utils.formatCurrency(valor)}`);
            } else {
                alert('Valor inválido. Digite um número válido.\nExemplo: 1000 ou 1000,50');
            }
        }
    }

    gerarRelatorioPDF() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Configurações
        const pageWidth = doc.internal.pageSize.width;
        const margin = 20;
        let yPosition = margin;

        // Título
        doc.setFontSize(20);
        doc.setFont(undefined, 'bold');
        doc.text('Relatório Financeiro - Controle de Caixa', pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 15;

        // Data do relatório
        doc.setFontSize(10);
        doc.setFont(undefined, 'normal');
        doc.text(`Gerado em: ${new Date().toLocaleString('pt-BR')}`, pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 20;

        // Resumo financeiro
        const saldoAtual = this.calcularSaldoAtual(this.transacoes);
        const totalEntradas = this.transacoes.filter(t => t.tipo === 'entrada').reduce((sum, t) => sum + t.valor, 0);
        const totalSaidas = this.transacoes.filter(t => t.tipo === 'saida').reduce((sum, t) => sum + t.valor, 0);

        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text('RESUMO FINANCEIRO', margin, yPosition);
        yPosition += 10;

        doc.setFontSize(11);
        doc.setFont(undefined, 'normal');
        doc.text(`Saldo Inicial: ${Utils.formatCurrency(this.saldoInicial)}`, margin, yPosition);
        yPosition += 7;
        doc.text(`Total de Entradas: ${Utils.formatCurrency(totalEntradas)}`, margin, yPosition);
        yPosition += 7;
        doc.text(`Total de Saídas: ${Utils.formatCurrency(totalSaidas)}`, margin, yPosition);
        yPosition += 7;
        doc.setFont(undefined, 'bold');
        doc.text(`Saldo Atual: ${Utils.formatCurrency(saldoAtual)}`, margin, yPosition);
        yPosition += 15;

        // Gastos por categoria
        const gastosPorCategoria = this.calcularGastosPorCategoria(this.transacoes);
        doc.setFontSize(14);
        doc.text('GASTOS POR CATEGORIA', margin, yPosition);
        yPosition += 10;

        doc.setFontSize(11);
        doc.setFont(undefined, 'normal');
        Object.entries(gastosPorCategoria).forEach(([categoria, valor]) => {
            if (valor > 0) {
                const percentual = totalSaidas > 0 ? (valor / totalSaidas * 100).toFixed(1) : 0;
                doc.text(`${categoria}: ${Utils.formatCurrency(valor)} (${percentual}%)`, margin, yPosition);
                yPosition += 7;
            }
        });
        yPosition += 10;

        // Últimas transações
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text('ÚLTIMAS TRANSAÇÕES', margin, yPosition);
        yPosition += 10;

        const ultimasTransacoes = [...this.transacoes]
            .sort((a, b) => new Date(b.data) - new Date(a.data))
            .slice(0, 10);

        doc.setFontSize(9);
        doc.setFont(undefined, 'normal');

        // Cabeçalho da tabela
        doc.text('Data', margin, yPosition);
        doc.text('Tipo', margin + 30, yPosition);
        doc.text('Descrição', margin + 60, yPosition);
        doc.text('Valor', margin + 120, yPosition);
        yPosition += 7;

        // Linha separadora
        doc.line(margin, yPosition - 2, pageWidth - margin, yPosition - 2);

        ultimasTransacoes.forEach(transacao => {
            if (yPosition > 270) { // Nova página se necessário
                doc.addPage();
                yPosition = margin;
            }

            doc.text(Utils.formatDate(transacao.data), margin, yPosition);
            doc.text(transacao.tipo === 'entrada' ? 'Entrada' : 'Saída', margin + 30, yPosition);

            // Truncar descrição se muito longa
            let descricao = transacao.descricao;
            if (descricao.length > 25) {
                descricao = descricao.substring(0, 22) + '...';
            }
            doc.text(descricao, margin + 60, yPosition);

            const valorFormatado = Utils.formatCurrency(transacao.valor);
            doc.text(valorFormatado, margin + 120, yPosition);
            yPosition += 6;
        });

        // Salvar PDF
        const nomeArquivo = `relatorio_caixa_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(nomeArquivo);
    }
}

// Classe utilitária para formatação
class Utils {
    static formatCurrency(value) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    }

    static formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    static formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('pt-BR');
    }
}

// Funções globais
let caixaSystem;

document.addEventListener('DOMContentLoaded', function() {
    try {
        caixaSystem = new CaixaSystem();
    } catch (error) {
        console.error('Erro ao inicializar CaixaSystem:', error);
        alert('Erro ao inicializar o sistema. Recarregue a página.');
    }
});

function aplicarFiltros() {
    caixaSystem.aplicarFiltros();
}

function limparFiltros() {
    caixaSystem.limparFiltros();
}

function sincronizarDados() {
    caixaSystem.sincronizarDados();
    caixaSystem.updateDisplay();
    alert('Dados sincronizados com sucesso!');
}

function exportarDados() {
    caixaSystem.exportarDados();
}

function definirSaldoInicial() {
    if (!caixaSystem) {
        alert('Sistema não inicializado. Recarregue a página.');
        return;
    }
    caixaSystem.definirSaldoInicial();
}

function gerarRelatorioPDF() {
    caixaSystem.gerarRelatorioPDF();
}
