<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Sistema - Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Teste do Sistema de Gestão de Frotas</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Status dos Dados</h5>
                    </div>
                    <div class="card-body">
                        <div id="status-veiculos" class="mb-2">
                            <i class="fas fa-car"></i> Veículos: <span id="count-veiculos">Carregando...</span>
                        </div>
                        <div id="status-abastecimentos" class="mb-2">
                            <i class="fas fa-gas-pump"></i> Abastecimentos: <span id="count-abastecimentos">Carregando...</span>
                        </div>
                        <div id="status-manutencoes" class="mb-2">
                            <i class="fas fa-wrench"></i> Manutenções: <span id="count-manutencoes">Carregando...</span>
                        </div>
                        <div id="status-lavagens" class="mb-2">
                            <i class="fas fa-car-wash"></i> Lavagens: <span id="count-lavagens">Carregando...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Teste de Dropdown de Veículos</h5>
                    </div>
                    <div class="card-body">
                        <label for="test-veiculo">Selecione um veículo:</label>
                        <select id="test-veiculo" class="form-select">
                            <option value="">Carregando veículos...</option>
                        </select>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="testVehicleSelection()">
                                Testar Seleção
                            </button>
                        </div>
                        <div id="test-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Ações de Teste</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success me-2" onclick="addTestVehicle()">
                            <i class="fas fa-plus"></i> Adicionar Veículo de Teste
                        </button>
                        <button class="btn btn-warning me-2" onclick="clearAllData()">
                            <i class="fas fa-trash"></i> Limpar Todos os Dados
                        </button>
                        <button class="btn btn-info" onclick="refreshData()">
                            <i class="fas fa-refresh"></i> Atualizar Dados
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para carregar e exibir status dos dados
        function loadDataStatus() {
            // Veículos
            const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            document.getElementById('count-veiculos').textContent = vehicles.length;
            
            // Abastecimentos
            const fuel = JSON.parse(localStorage.getItem('abastecimentos') || '[]');
            document.getElementById('count-abastecimentos').textContent = fuel.length;
            
            // Manutenções
            const maintenance = JSON.parse(localStorage.getItem('manutencoes') || '[]');
            document.getElementById('count-manutencoes').textContent = maintenance.length;
            
            // Lavagens
            const washing = JSON.parse(localStorage.getItem('lavagens') || '[]');
            document.getElementById('count-lavagens').textContent = washing.length;
            
            // Carregar dropdown de veículos
            loadVehicleDropdown();
        }
        
        // Função para carregar dropdown de veículos
        function loadVehicleDropdown() {
            const select = document.getElementById('test-veiculo');
            const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            
            select.innerHTML = '<option value="">Selecione um veículo</option>';
            
            if (vehicles.length === 0) {
                select.innerHTML = '<option value="">Nenhum veículo cadastrado</option>';
                return;
            }
            
            vehicles.forEach(vehicle => {
                const option = document.createElement('option');
                option.value = vehicle.id;
                option.textContent = `${vehicle.plate} - ${vehicle.brand} ${vehicle.model}`;
                select.appendChild(option);
            });
        }
        
        // Função para testar seleção de veículo
        function testVehicleSelection() {
            const select = document.getElementById('test-veiculo');
            const result = document.getElementById('test-result');
            
            if (select.value) {
                const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
                const selectedVehicle = vehicles.find(v => v.id == select.value);
                
                result.innerHTML = `
                    <div class="alert alert-success">
                        <strong>Veículo selecionado:</strong><br>
                        ID: ${selectedVehicle.id}<br>
                        Placa: ${selectedVehicle.plate}<br>
                        Marca: ${selectedVehicle.brand}<br>
                        Modelo: ${selectedVehicle.model}<br>
                        Ano: ${selectedVehicle.year}
                    </div>
                `;
            } else {
                result.innerHTML = '<div class="alert alert-warning">Nenhum veículo selecionado</div>';
            }
        }
        
        // Função para adicionar veículo de teste
        function addTestVehicle() {
            const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            const testVehicle = {
                id: Date.now(),
                plate: 'TST-' + Math.floor(Math.random() * 1000),
                brand: 'Toyota',
                model: 'Corolla',
                year: 2023,
                fuel: 'flex',
                status: 'ativo',
                km: 0
            };
            
            vehicles.push(testVehicle);
            localStorage.setItem('frotas_vehicles', JSON.stringify(vehicles));
            
            refreshData();
            alert('Veículo de teste adicionado com sucesso!');
        }
        
        // Função para limpar todos os dados
        function clearAllData() {
            if (confirm('Tem certeza que deseja limpar todos os dados? Esta ação não pode ser desfeita.')) {
                localStorage.removeItem('frotas_vehicles');
                localStorage.removeItem('abastecimentos');
                localStorage.removeItem('manutencoes');
                localStorage.removeItem('lavagens');
                
                refreshData();
                alert('Todos os dados foram limpos!');
            }
        }
        
        // Função para atualizar dados
        function refreshData() {
            loadDataStatus();
        }
        
        // Carregar dados quando a página carregar
        document.addEventListener('DOMContentLoaded', loadDataStatus);
    </script>
</body>
</html>
