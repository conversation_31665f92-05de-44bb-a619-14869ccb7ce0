// Sistema de Lavagem
class LavagemSystem {
    constructor() {
        this.lavagens = JSON.parse(localStorage.getItem('lavagens')) || [];
        this.veiculos = this.getVeiculosFromStorage();
        this.filteredData = [...this.lavagens];
        
        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        console.log('🚀 Inicializando sistema de lavagem...');

        this.loadVeiculos();
        this.loadLavagens();
        this.setupEventListeners();
        this.updateStats();

        // Se não há veículos, criar dados de teste
        if (this.veiculos.length === 0) {
            console.log('⚠️ Nenhum veículo encontrado, criando dados de teste...');
            this.createTestData();
        }

        console.log('✅ Sistema de lavagem inicializado:', {
            veiculos: this.veiculos.length,
            lavagens: this.lavagens.length
        });
    }

    // Criar dados de teste se necessário
    createTestData() {
        const testVehicles = [
            { id: 1, brand: 'Toyota', model: 'Corolla', year: 2020, plate: 'ABC-1234', status: 'ativo' },
            { id: 2, brand: 'Honda', model: 'Civic', year: 2019, plate: 'DEF-5678', status: 'ativo' },
            { id: 3, brand: 'Ford', model: 'Focus', year: 2021, plate: 'GHI-9012', status: 'ativo' }
        ];

        // Salvar veículos de teste se não existirem
        const existingVehicles = localStorage.getItem('frotas_vehicles');
        if (!existingVehicles) {
            localStorage.setItem('frotas_vehicles', JSON.stringify(testVehicles));
            this.veiculos = this.getVeiculosFromStorage();
            this.loadVeiculos(); // Recarregar dropdown
            console.log('✅ Veículos de teste criados');
        }
    }

    // Carregar veículos do sistema
    getVeiculosFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                return vehicles.map(v => ({
                    id: v.id,
                    modelo: `${v.brand} ${v.model} ${v.year}`,
                    placa: v.plate,
                    status: v.status
                }));
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
                return [];
            }
        }
        return [];
    }

    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }
    }

    // Carregar lavagens na tabela
    loadLavagens() {
        const tbody = document.getElementById('lavagensTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="fas fa-spray-can fa-2x mb-2"></i><br>
                        Nenhuma lavagem registrada
                    </td>
                </tr>
            `;
            return;
        }

        this.filteredData.forEach(lavagem => {
            const veiculo = this.veiculos.find(v => v.id == lavagem.veiculoId);
            const row = document.createElement('tr');

            // Determinar local de exibição
            let localDisplay = lavagem.localLavagem || lavagem.local || '-';
            if (localDisplay === 'interno') {
                localDisplay = 'Interno (Garagem)';
            } else if (localDisplay === 'externo') {
                localDisplay = lavagem.nomeLavajato ? `${lavagem.nomeLavajato}` : 'Externo (Lava-jato)';
            }

            // Determinar tipo de lavagem
            let tipoDisplay = lavagem.tipoLavagem || '-';
            tipoDisplay = tipoDisplay.charAt(0).toUpperCase() + tipoDisplay.slice(1);

            // Determinar status
            let statusDisplay = lavagem.status || 'realizada';
            let statusClass = '';
            switch(statusDisplay) {
                case 'agendada':
                    statusClass = 'badge bg-warning';
                    statusDisplay = 'Agendada';
                    break;
                case 'realizada':
                    statusClass = 'badge bg-success';
                    statusDisplay = 'Realizada';
                    break;
                case 'cancelada':
                    statusClass = 'badge bg-danger';
                    statusDisplay = 'Cancelada';
                    break;
                default:
                    statusClass = 'badge bg-secondary';
                    statusDisplay = 'Indefinido';
            }

            row.innerHTML = `
                <td>${this.formatDate(lavagem.dataLavagem)}</td>
                <td>${lavagem.veiculo || (veiculo ? `${veiculo.modelo} - ${veiculo.placa}` : 'Veículo não encontrado')}</td>
                <td>${tipoDisplay}</td>
                <td>${localDisplay}</td>
                <td>${lavagem.responsavel}</td>
                <td>R$ ${lavagem.valor.toFixed(2)}</td>
                <td><span class="${statusClass}">${statusDisplay}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editLavagem(${lavagem.id})" title="Editar">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteLavagem(${lavagem.id})" title="Excluir">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de lavagem
        const lavagemForm = document.getElementById('lavagemForm');
        if (lavagemForm) {
            lavagemForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Campo local - mostrar/ocultar campos do lava-jato
        const localLavagemSelect = document.getElementById('localLavagem');
        if (localLavagemSelect) {
            localLavagemSelect.addEventListener('change', (e) => {
                const lavajatoFields = document.getElementById('lavajatoFields');
                if (lavajatoFields) {
                    if (e.target.value === 'externo') {
                        lavajatoFields.style.display = 'block';
                    } else {
                        lavajatoFields.style.display = 'none';
                        // Limpar campos quando ocultar
                        const nomeLavajato = document.getElementById('nomeLavajato');
                        const enderecoLavajato = document.getElementById('enderecoLavajato');
                        if (nomeLavajato) nomeLavajato.value = '';
                        if (enderecoLavajato) enderecoLavajato.value = '';
                    }
                }
            });
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.applyFilters());
        }

        const filterPeriodo = document.getElementById('filterPeriodo');
        if (filterPeriodo) {
            filterPeriodo.addEventListener('change', () => this.applyFilters());
        }

        const filterTipo = document.getElementById('filterTipo');
        if (filterTipo) {
            filterTipo.addEventListener('change', () => this.applyFilters());
        }

        const filterLocal = document.getElementById('filterLocal');
        if (filterLocal) {
            filterLocal.addEventListener('change', () => this.applyFilters());
        }
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();

        console.log('🚿 Processando formulário de lavagem...');

        const formData = new FormData(e.target);

        // Debug: mostrar dados do formulário
        console.log('📋 Dados do formulário:', {
            veiculo: formData.get('veiculo'),
            dataLavagem: formData.get('dataLavagem'),
            tipoLavagem: formData.get('tipoLavagem'),
            localLavagem: formData.get('localLavagem'),
            responsavel: formData.get('responsavel'),
            valor: formData.get('valor')
        });

        // Debug: verificar dados do formulário
        console.log('📋 Dados do formulário:', {
            veiculo: formData.get('veiculo'),
            dataLavagem: formData.get('dataLavagem'),
            tipoLavagem: formData.get('tipoLavagem'),
            localLavagem: formData.get('localLavagem'),
            responsavel: formData.get('responsavel'),
            valor: formData.get('valor')
        });

        // Validar campos obrigatórios
        const veiculoValue = formData.get('veiculo');
        if (!veiculoValue || veiculoValue === '' || veiculoValue === 'null') {
            alert('Por favor, selecione um veículo.');
            console.error('❌ Veículo não selecionado:', veiculoValue);
            return;
        }

        if (!formData.get('dataLavagem')) {
            alert('Por favor, informe a data/hora da lavagem.');
            return;
        }

        if (!formData.get('tipoLavagem')) {
            alert('Por favor, selecione o tipo de lavagem.');
            return;
        }

        if (!formData.get('localLavagem')) {
            alert('Por favor, selecione o local da lavagem.');
            return;
        }

        if (!formData.get('responsavel')) {
            alert('Por favor, informe o responsável.');
            return;
        }

        if (!formData.get('valor') || parseFloat(formData.get('valor')) <= 0) {
            alert('Por favor, informe um valor válido.');
            return;
        }

        // Verificar se estamos editando ou criando
        const editingId = e.target.getAttribute('data-editing-id');
        const isEditing = editingId && editingId !== '';

        console.log(isEditing ? '🔧 Modo edição detectado' : '➕ Modo criação detectado');

        // Buscar dados do veículo
        const veiculoId = parseInt(formData.get('veiculo'));
        const veiculo = this.veiculos.find(v => v.id === veiculoId);

        console.log('🚗 Dados do veículo:', {
            veiculoId: veiculoId,
            veiculo: veiculo,
            veiculosDisponiveis: this.veiculos.length
        });

        if (!veiculo) {
            console.error('❌ Veículo não encontrado com ID:', veiculoId);
            console.log('📋 Veículos disponíveis:', this.veiculos);
        }

        const lavagemData = {
            veiculoId: veiculoId,
            veiculo: veiculo ? `${veiculo.modelo} - ${veiculo.placa}` : 'Veículo não encontrado',
            dataLavagem: formData.get('dataLavagem'),
            tipoLavagem: formData.get('tipoLavagem'),
            localLavagem: formData.get('localLavagem'),
            nomeLavajato: formData.get('nomeLavajato') || '',
            enderecoLavajato: formData.get('enderecoLavajato') || '',
            responsavel: formData.get('responsavel'),
            valor: parseFloat(formData.get('valor')),
            kmAtual: formData.get('kmAtual') ? parseInt(formData.get('kmAtual')) : null,
            status: formData.get('status') || 'realizada',
            observacoes: formData.get('observacoes') || '',
            servicos: formData.getAll('servicos')
        };

        let lavagem;
        let mensagemSucesso;

        if (isEditing) {
            // Modo edição - atualizar lavagem existente
            const lavagemIndex = this.lavagens.findIndex(l => l.id == editingId);
            if (lavagemIndex === -1) {
                alert('Erro: Lavagem não encontrada para edição.');
                return;
            }

            // Manter ID e data de registro originais
            lavagem = {
                ...lavagemData,
                id: parseInt(editingId),
                dataRegistro: this.lavagens[lavagemIndex].dataRegistro,
                dataAtualizacao: new Date().toISOString()
            };

            this.lavagens[lavagemIndex] = lavagem;
            mensagemSucesso = 'Lavagem atualizada com sucesso!';
            console.log('✅ Lavagem atualizada:', lavagem);

        } else {
            // Modo criação - nova lavagem
            lavagem = {
                ...lavagemData,
                id: Date.now(),
                dataRegistro: new Date().toISOString()
            };

            this.lavagens.push(lavagem);
            mensagemSucesso = 'Lavagem registrada com sucesso!';
            console.log('✅ Lavagem criada:', lavagem);
        }

        // Salvar e atualizar interface
        this.saveLavagens();
        this.filteredData = [...this.lavagens];
        this.loadLavagens();
        this.updateStats();

        // Fechar modal e limpar formulário
        try {
            const modal = bootstrap.Modal.getInstance(document.getElementById('lavagemModal'));
            if (modal) {
                modal.hide();
            }
        } catch (error) {
            console.error('Erro ao fechar modal:', error);
        }

        // Limpar formulário e resetar estado de edição
        e.target.reset();
        e.target.removeAttribute('data-editing-id');

        // Resetar texto do botão e título do modal
        const submitButton = e.target.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-save"></i> Salvar Lavagem';
        }

        const modalTitle = document.querySelector('#lavagemModal .modal-title');
        if (modalTitle) {
            modalTitle.textContent = 'Nova Lavagem';
        }

        this.showNotification(mensagemSucesso, 'success');
        console.log('🎉 Operação concluída com sucesso!');
    }

    // Salvar lavagens no localStorage
    saveLavagens() {
        localStorage.setItem('lavagens', JSON.stringify(this.lavagens));
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const filterPeriodo = document.getElementById('filterPeriodo')?.value || '';
        const filterTipo = document.getElementById('filterTipo')?.value || '';
        const filterLocal = document.getElementById('filterLocal')?.value || '';

        this.filteredData = this.lavagens.filter(lavagem => {
            const veiculo = this.veiculos.find(v => v.id == lavagem.veiculoId);

            // Filtro de busca
            const searchMatch = !searchTerm ||
                (veiculo && (
                    veiculo.placa.toLowerCase().includes(searchTerm) ||
                    veiculo.modelo.toLowerCase().includes(searchTerm)
                )) ||
                lavagem.tipoLavagem.toLowerCase().includes(searchTerm) ||
                (lavagem.localLavagem && lavagem.localLavagem.toLowerCase().includes(searchTerm)) ||
                lavagem.responsavel.toLowerCase().includes(searchTerm) ||
                (lavagem.nomeLavajato && lavagem.nomeLavajato.toLowerCase().includes(searchTerm));

            // Filtro de período
            let periodoMatch = true;
            if (filterPeriodo) {
                const dataLavagem = new Date(lavagem.dataLavagem);
                const hoje = new Date();

                switch(filterPeriodo) {
                    case 'hoje':
                        periodoMatch = dataLavagem.toDateString() === hoje.toDateString();
                        break;
                    case 'semana':
                        const inicioSemana = new Date(hoje);
                        inicioSemana.setDate(hoje.getDate() - hoje.getDay());
                        periodoMatch = dataLavagem >= inicioSemana;
                        break;
                    case 'mes':
                        periodoMatch = dataLavagem.getMonth() === hoje.getMonth() &&
                                     dataLavagem.getFullYear() === hoje.getFullYear();
                        break;
                    case 'trimestre':
                        const trimestreAtual = Math.floor(hoje.getMonth() / 3);
                        const trimestreLavagem = Math.floor(dataLavagem.getMonth() / 3);
                        periodoMatch = trimestreLavagem === trimestreAtual &&
                                     dataLavagem.getFullYear() === hoje.getFullYear();
                        break;
                }
            }

            // Filtro de tipo
            const tipoMatch = !filterTipo || lavagem.tipoLavagem === filterTipo;

            // Filtro de local
            const localMatch = !filterLocal || lavagem.localLavagem === filterLocal;

            return searchMatch && periodoMatch && tipoMatch && localMatch;
        });

        this.loadLavagens();
        this.updateStats();

        console.log(`🔍 Filtros aplicados: ${this.filteredData.length} de ${this.lavagens.length} lavagens`);
    }

    // Atualizar estatísticas
    updateStats() {
        const totalLavagens = this.filteredData.length;
        const valorTotal = this.filteredData.reduce((sum, l) => sum + l.valor, 0);
        const valorMedio = totalLavagens > 0 ? valorTotal / totalLavagens : 0;

        // Atualizar elementos da interface
        const totalElement = document.getElementById('totalLavagens');
        const valorTotalElement = document.getElementById('valorTotalLavagens');
        const valorMedioElement = document.getElementById('valorMedioLavagem');

        if (totalElement) totalElement.textContent = totalLavagens;
        if (valorTotalElement) valorTotalElement.textContent = `R$ ${valorTotal.toFixed(2)}`;
        if (valorMedioElement) valorMedioElement.textContent = `R$ ${valorMedio.toFixed(2)}`;
    }

    // Formatar data
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    // Mostrar notificação
    showNotification(message, type = 'info') {
        // Implementação básica de notificação
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Funções globais
function showLavagemModal() {
    console.log('🚿 Abrindo modal para nova lavagem...');

    // Verificar se o sistema está inicializado
    if (!lavagemSystem) {
        console.error('❌ Sistema de lavagem não inicializado');
        alert('Sistema não inicializado. Recarregue a página.');
        return;
    }

    // Recarregar veículos antes de abrir o modal
    lavagemSystem.loadVeiculos();

    try {
        // Limpar e resetar formulário completamente
        const form = document.getElementById('lavagemForm');
        if (form) {
            form.reset();
            form.removeAttribute('data-editing-id'); // Remover ID de edição se existir
        }

        // Definir data/hora atual
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        const dataLavagemInput = document.getElementById('dataLavagem');
        if (dataLavagemInput) {
            dataLavagemInput.value = localDateTime;
        }

        // Ocultar campos do lava-jato por padrão
        const lavajatoFields = document.getElementById('lavajatoFields');
        if (lavajatoFields) {
            lavajatoFields.style.display = 'none';
        }

        // Limpar checkboxes de serviços
        const servicosCheckboxes = document.querySelectorAll('input[name="servicos"]');
        servicosCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Resetar texto do botão e título do modal para nova lavagem
        const submitButton = form ? form.querySelector('button[type="submit"]') : null;
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-save"></i> Salvar Lavagem';
        }

        const modalTitle = document.querySelector('#lavagemModal .modal-title');
        if (modalTitle) {
            modalTitle.textContent = 'Nova Lavagem';
        }

        // Abrir modal
        const modalElement = document.getElementById('lavagemModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('✅ Modal de nova lavagem aberto com sucesso');
        } else {
            console.error('❌ Elemento modal não encontrado');
            alert('Erro: Modal não encontrado. Verifique se a página carregou corretamente.');
        }

    } catch (error) {
        console.error('❌ Erro ao abrir modal:', error);
        alert('Erro ao abrir formulário: ' + error.message);
    }
}

function editLavagem(id) {
    console.log('🔧 Editando lavagem ID:', id);

    if (!lavagemSystem) {
        console.error('❌ Sistema de lavagem não inicializado');
        alert('Sistema não inicializado. Recarregue a página.');
        return;
    }

    // Buscar a lavagem pelo ID
    const lavagem = lavagemSystem.lavagens.find(l => l.id == id);
    if (!lavagem) {
        console.error('❌ Lavagem não encontrada:', id);
        alert('Lavagem não encontrada.');
        return;
    }

    console.log('📋 Dados da lavagem encontrada:', lavagem);

    try {
        // Recarregar veículos antes de abrir o modal
        lavagemSystem.loadVeiculos();

        // Preencher o formulário com os dados da lavagem
        const form = document.getElementById('lavagemForm');
        if (!form) {
            console.error('❌ Formulário não encontrado');
            alert('Formulário não encontrado.');
            return;
        }

        // Preencher campos do formulário
        const veiculoSelect = document.getElementById('veiculo');
        const dataLavagemInput = document.getElementById('dataLavagem');
        const tipoLavagemSelect = document.getElementById('tipoLavagem');
        const localLavagemSelect = document.getElementById('localLavagem');
        const nomeLavajatoInput = document.getElementById('nomeLavajato');
        const enderecoLavajatoInput = document.getElementById('enderecoLavajato');
        const responsavelInput = document.getElementById('responsavel');
        const valorInput = document.getElementById('valor');
        const kmAtualInput = document.getElementById('kmAtual');
        const statusSelect = document.getElementById('status');
        const observacoesTextarea = document.getElementById('observacoes');

        // Preencher campos básicos
        if (veiculoSelect) veiculoSelect.value = lavagem.veiculoId || '';
        if (dataLavagemInput) {
            // Converter data para formato datetime-local
            const dataFormatada = new Date(lavagem.dataLavagem);
            const localDateTime = new Date(dataFormatada.getTime() - dataFormatada.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            dataLavagemInput.value = localDateTime;
        }
        if (tipoLavagemSelect) tipoLavagemSelect.value = lavagem.tipoLavagem || '';
        if (localLavagemSelect) {
            localLavagemSelect.value = lavagem.localLavagem || lavagem.local || '';

            // Mostrar/ocultar campos do lava-jato baseado no local
            const lavajatoFields = document.getElementById('lavajatoFields');
            if (lavajatoFields) {
                if (lavagem.localLavagem === 'externo' || lavagem.local === 'externo') {
                    lavajatoFields.style.display = 'block';
                    if (nomeLavajatoInput) nomeLavajatoInput.value = lavagem.nomeLavajato || '';
                    if (enderecoLavajatoInput) enderecoLavajatoInput.value = lavagem.enderecoLavajato || '';
                } else {
                    lavajatoFields.style.display = 'none';
                }
            }
        }
        if (responsavelInput) responsavelInput.value = lavagem.responsavel || '';
        if (valorInput) valorInput.value = lavagem.valor || '';
        if (kmAtualInput) kmAtualInput.value = lavagem.kmAtual || '';
        if (statusSelect) statusSelect.value = lavagem.status || 'realizada';
        if (observacoesTextarea) observacoesTextarea.value = lavagem.observacoes || '';

        // Preencher serviços (checkboxes)
        if (lavagem.servicos && Array.isArray(lavagem.servicos)) {
            const servicosCheckboxes = document.querySelectorAll('input[name="servicos"]');
            servicosCheckboxes.forEach(checkbox => {
                checkbox.checked = lavagem.servicos.includes(checkbox.value);
            });
        }

        // Marcar que estamos editando (adicionar ID ao formulário)
        form.setAttribute('data-editing-id', id);

        // Alterar texto do botão de submit
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-save"></i> Atualizar Lavagem';
        }

        // Alterar título do modal
        const modalTitle = document.querySelector('#lavagemModal .modal-title');
        if (modalTitle) {
            modalTitle.textContent = 'Editar Lavagem';
        }

        // Abrir o modal
        const modalElement = document.getElementById('lavagemModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('✅ Modal de edição aberto com sucesso');
        } else {
            console.error('❌ Elemento modal não encontrado');
            alert('Erro: Modal não encontrado.');
        }

    } catch (error) {
        console.error('❌ Erro ao abrir modal de edição:', error);
        alert('Erro ao abrir formulário de edição: ' + error.message);
    }
}

function deleteLavagem(id) {
    if (confirm('Tem certeza que deseja excluir esta lavagem?')) {
        lavagemSystem.lavagens = lavagemSystem.lavagens.filter(l => l.id !== id);
        lavagemSystem.saveLavagens();
        lavagemSystem.filteredData = [...lavagemSystem.lavagens];
        lavagemSystem.loadLavagens();
        lavagemSystem.updateStats();
        lavagemSystem.showNotification('Lavagem excluída com sucesso!', 'success');
    }
}

function exportLavagens() {
    // Implementar exportação
    console.log('Exportar lavagens');
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.lavagemSystem = new LavagemSystem();
});
