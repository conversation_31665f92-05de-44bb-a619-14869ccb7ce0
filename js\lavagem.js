// Sistema de Lavagem
class LavagemSystem {
    constructor() {
        this.lavagens = JSON.parse(localStorage.getItem('lavagens')) || [];
        this.veiculos = this.getVeiculosFromStorage();
        this.filteredData = [...this.lavagens];
        
        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        this.loadVeiculos();
        this.loadLavagens();
        this.setupEventListeners();
        this.updateStats();
    }

    // Carregar veículos do sistema
    getVeiculosFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                return vehicles.map(v => ({
                    id: v.id,
                    modelo: `${v.brand} ${v.model} ${v.year}`,
                    placa: v.plate,
                    status: v.status
                }));
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
                return [];
            }
        }
        return [];
    }

    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }
    }

    // Carregar lavagens na tabela
    loadLavagens() {
        const tbody = document.getElementById('lavagensTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="fas fa-spray-can fa-2x mb-2"></i><br>
                        Nenhuma lavagem registrada
                    </td>
                </tr>
            `;
            return;
        }

        this.filteredData.forEach(lavagem => {
            const veiculo = this.veiculos.find(v => v.id == lavagem.veiculoId);
            const row = document.createElement('tr');

            // Determinar local de exibição
            let localDisplay = lavagem.localLavagem || lavagem.local || '-';
            if (localDisplay === 'interno') {
                localDisplay = 'Interno (Garagem)';
            } else if (localDisplay === 'externo') {
                localDisplay = lavagem.nomeLavajato ? `${lavagem.nomeLavajato}` : 'Externo (Lava-jato)';
            }

            // Determinar tipo de lavagem
            let tipoDisplay = lavagem.tipoLavagem || '-';
            tipoDisplay = tipoDisplay.charAt(0).toUpperCase() + tipoDisplay.slice(1);

            // Determinar status
            let statusDisplay = lavagem.status || 'realizada';
            let statusClass = '';
            switch(statusDisplay) {
                case 'agendada':
                    statusClass = 'badge bg-warning';
                    statusDisplay = 'Agendada';
                    break;
                case 'realizada':
                    statusClass = 'badge bg-success';
                    statusDisplay = 'Realizada';
                    break;
                case 'cancelada':
                    statusClass = 'badge bg-danger';
                    statusDisplay = 'Cancelada';
                    break;
                default:
                    statusClass = 'badge bg-secondary';
                    statusDisplay = 'Indefinido';
            }

            row.innerHTML = `
                <td>${this.formatDate(lavagem.dataLavagem)}</td>
                <td>${lavagem.veiculo || (veiculo ? `${veiculo.modelo} - ${veiculo.placa}` : 'Veículo não encontrado')}</td>
                <td>${tipoDisplay}</td>
                <td>${localDisplay}</td>
                <td>${lavagem.responsavel}</td>
                <td>R$ ${lavagem.valor.toFixed(2)}</td>
                <td><span class="${statusClass}">${statusDisplay}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editLavagem(${lavagem.id})" title="Editar">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteLavagem(${lavagem.id})" title="Excluir">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de lavagem
        const lavagemForm = document.getElementById('lavagemForm');
        if (lavagemForm) {
            lavagemForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Campo local - mostrar/ocultar campos do lava-jato
        const localLavagemSelect = document.getElementById('localLavagem');
        if (localLavagemSelect) {
            localLavagemSelect.addEventListener('change', (e) => {
                const lavajatoFields = document.getElementById('lavajatoFields');
                if (lavajatoFields) {
                    if (e.target.value === 'externo') {
                        lavajatoFields.style.display = 'block';
                    } else {
                        lavajatoFields.style.display = 'none';
                        // Limpar campos quando ocultar
                        const nomeLavajato = document.getElementById('nomeLavajato');
                        const enderecoLavajato = document.getElementById('enderecoLavajato');
                        if (nomeLavajato) nomeLavajato.value = '';
                        if (enderecoLavajato) enderecoLavajato.value = '';
                    }
                }
            });
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.applyFilters());
        }

        const filterPeriodo = document.getElementById('filterPeriodo');
        if (filterPeriodo) {
            filterPeriodo.addEventListener('change', () => this.applyFilters());
        }

        const filterTipo = document.getElementById('filterTipo');
        if (filterTipo) {
            filterTipo.addEventListener('change', () => this.applyFilters());
        }

        const filterLocal = document.getElementById('filterLocal');
        if (filterLocal) {
            filterLocal.addEventListener('change', () => this.applyFilters());
        }
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();

        console.log('🚿 Processando formulário de lavagem...');

        const formData = new FormData(e.target);

        // Debug: mostrar dados do formulário
        console.log('📋 Dados do formulário:', {
            veiculo: formData.get('veiculo'),
            dataLavagem: formData.get('dataLavagem'),
            tipoLavagem: formData.get('tipoLavagem'),
            localLavagem: formData.get('localLavagem'),
            responsavel: formData.get('responsavel'),
            valor: formData.get('valor')
        });

        // Validar campos obrigatórios
        if (!formData.get('veiculo')) {
            alert('Por favor, selecione um veículo.');
            return;
        }

        if (!formData.get('dataLavagem')) {
            alert('Por favor, informe a data/hora da lavagem.');
            return;
        }

        if (!formData.get('tipoLavagem')) {
            alert('Por favor, selecione o tipo de lavagem.');
            return;
        }

        if (!formData.get('localLavagem')) {
            alert('Por favor, selecione o local da lavagem.');
            return;
        }

        if (!formData.get('responsavel')) {
            alert('Por favor, informe o responsável.');
            return;
        }

        if (!formData.get('valor') || parseFloat(formData.get('valor')) <= 0) {
            alert('Por favor, informe um valor válido.');
            return;
        }

        // Buscar dados do veículo
        const veiculoId = parseInt(formData.get('veiculo'));
        const veiculo = this.veiculos.find(v => v.id === veiculoId);

        const lavagem = {
            id: Date.now(),
            veiculoId: veiculoId,
            veiculo: veiculo ? `${veiculo.modelo} - ${veiculo.placa}` : 'Veículo não encontrado',
            dataLavagem: formData.get('dataLavagem'),
            tipoLavagem: formData.get('tipoLavagem'),
            localLavagem: formData.get('localLavagem'),
            nomeLavajato: formData.get('nomeLavajato') || '',
            enderecoLavajato: formData.get('enderecoLavajato') || '',
            responsavel: formData.get('responsavel'),
            valor: parseFloat(formData.get('valor')),
            kmAtual: formData.get('kmAtual') ? parseInt(formData.get('kmAtual')) : null,
            status: formData.get('status') || 'realizada',
            observacoes: formData.get('observacoes') || '',
            servicos: formData.getAll('servicos'),
            dataRegistro: new Date().toISOString()
        };

        console.log('✅ Lavagem criada:', lavagem);

        this.lavagens.push(lavagem);
        this.saveLavagens();
        this.filteredData = [...this.lavagens];
        this.loadLavagens();
        this.updateStats();

        // Fechar modal
        try {
            const modal = bootstrap.Modal.getInstance(document.getElementById('lavagemModal'));
            if (modal) {
                modal.hide();
            }
        } catch (error) {
            console.error('Erro ao fechar modal:', error);
        }

        // Limpar formulário
        e.target.reset();

        this.showNotification('Lavagem registrada com sucesso!', 'success');
        console.log('🎉 Lavagem salva com sucesso!');
    }

    // Salvar lavagens no localStorage
    saveLavagens() {
        localStorage.setItem('lavagens', JSON.stringify(this.lavagens));
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const filterPeriodo = document.getElementById('filterPeriodo')?.value || '';
        const filterTipo = document.getElementById('filterTipo')?.value || '';
        const filterLocal = document.getElementById('filterLocal')?.value || '';

        this.filteredData = this.lavagens.filter(lavagem => {
            const veiculo = this.veiculos.find(v => v.id == lavagem.veiculoId);

            // Filtro de busca
            const searchMatch = !searchTerm ||
                (veiculo && (
                    veiculo.placa.toLowerCase().includes(searchTerm) ||
                    veiculo.modelo.toLowerCase().includes(searchTerm)
                )) ||
                lavagem.tipoLavagem.toLowerCase().includes(searchTerm) ||
                (lavagem.localLavagem && lavagem.localLavagem.toLowerCase().includes(searchTerm)) ||
                lavagem.responsavel.toLowerCase().includes(searchTerm) ||
                (lavagem.nomeLavajato && lavagem.nomeLavajato.toLowerCase().includes(searchTerm));

            // Filtro de período
            let periodoMatch = true;
            if (filterPeriodo) {
                const dataLavagem = new Date(lavagem.dataLavagem);
                const hoje = new Date();

                switch(filterPeriodo) {
                    case 'hoje':
                        periodoMatch = dataLavagem.toDateString() === hoje.toDateString();
                        break;
                    case 'semana':
                        const inicioSemana = new Date(hoje);
                        inicioSemana.setDate(hoje.getDate() - hoje.getDay());
                        periodoMatch = dataLavagem >= inicioSemana;
                        break;
                    case 'mes':
                        periodoMatch = dataLavagem.getMonth() === hoje.getMonth() &&
                                     dataLavagem.getFullYear() === hoje.getFullYear();
                        break;
                    case 'trimestre':
                        const trimestreAtual = Math.floor(hoje.getMonth() / 3);
                        const trimestreLavagem = Math.floor(dataLavagem.getMonth() / 3);
                        periodoMatch = trimestreLavagem === trimestreAtual &&
                                     dataLavagem.getFullYear() === hoje.getFullYear();
                        break;
                }
            }

            // Filtro de tipo
            const tipoMatch = !filterTipo || lavagem.tipoLavagem === filterTipo;

            // Filtro de local
            const localMatch = !filterLocal || lavagem.localLavagem === filterLocal;

            return searchMatch && periodoMatch && tipoMatch && localMatch;
        });

        this.loadLavagens();
        this.updateStats();

        console.log(`🔍 Filtros aplicados: ${this.filteredData.length} de ${this.lavagens.length} lavagens`);
    }

    // Atualizar estatísticas
    updateStats() {
        const totalLavagens = this.filteredData.length;
        const valorTotal = this.filteredData.reduce((sum, l) => sum + l.valor, 0);
        const valorMedio = totalLavagens > 0 ? valorTotal / totalLavagens : 0;

        // Atualizar elementos da interface
        const totalElement = document.getElementById('totalLavagens');
        const valorTotalElement = document.getElementById('valorTotalLavagens');
        const valorMedioElement = document.getElementById('valorMedioLavagem');

        if (totalElement) totalElement.textContent = totalLavagens;
        if (valorTotalElement) valorTotalElement.textContent = `R$ ${valorTotal.toFixed(2)}`;
        if (valorMedioElement) valorMedioElement.textContent = `R$ ${valorMedio.toFixed(2)}`;
    }

    // Formatar data
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    // Mostrar notificação
    showNotification(message, type = 'info') {
        // Implementação básica de notificação
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Funções globais
function showLavagemModal() {
    console.log('🚿 Abrindo modal de lavagem...');

    // Verificar se o sistema está inicializado
    if (!lavagemSystem) {
        console.error('❌ Sistema de lavagem não inicializado');
        alert('Sistema não inicializado. Recarregue a página.');
        return;
    }

    // Recarregar veículos antes de abrir o modal
    lavagemSystem.loadVeiculos();

    // Definir data/hora atual
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    const dataLavagemInput = document.getElementById('dataLavagem');
    if (dataLavagemInput) {
        dataLavagemInput.value = localDateTime;
    }

    // Limpar formulário
    const form = document.getElementById('lavagemForm');
    if (form) {
        form.reset();
        // Redefine a data após o reset
        if (dataLavagemInput) {
            dataLavagemInput.value = localDateTime;
        }
    }

    // Abrir modal
    try {
        const modalElement = document.getElementById('lavagemModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('✅ Modal de lavagem aberto com sucesso');
        } else {
            console.error('❌ Elemento modal não encontrado');
            alert('Erro: Modal não encontrado. Verifique se a página carregou corretamente.');
        }
    } catch (error) {
        console.error('❌ Erro ao abrir modal:', error);
        alert('Erro ao abrir formulário: ' + error.message);
    }
}

function editLavagem(id) {
    // Implementar edição
    console.log('Editar lavagem:', id);
}

function deleteLavagem(id) {
    if (confirm('Tem certeza que deseja excluir esta lavagem?')) {
        lavagemSystem.lavagens = lavagemSystem.lavagens.filter(l => l.id !== id);
        lavagemSystem.saveLavagens();
        lavagemSystem.filteredData = [...lavagemSystem.lavagens];
        lavagemSystem.loadLavagens();
        lavagemSystem.updateStats();
        lavagemSystem.showNotification('Lavagem excluída com sucesso!', 'success');
    }
}

function exportLavagens() {
    // Implementar exportação
    console.log('Exportar lavagens');
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.lavagemSystem = new LavagemSystem();
});
