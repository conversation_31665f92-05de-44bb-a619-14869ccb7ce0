// Sistema de Lavagem
class LavagemSystem {
    constructor() {
        this.lavagens = JSON.parse(localStorage.getItem('lavagens')) || [];
        this.veiculos = this.getVeiculosFromStorage();
        this.filteredData = [...this.lavagens];
        
        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        this.loadVeiculos();
        this.loadLavagens();
        this.setupEventListeners();
        this.updateStats();
    }

    // Carregar veículos do sistema
    getVeiculosFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                return vehicles.map(v => ({
                    id: v.id,
                    modelo: `${v.brand} ${v.model} ${v.year}`,
                    placa: v.plate,
                    status: v.status
                }));
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
                return [];
            }
        }
        return [];
    }

    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }
    }

    // Carregar lavagens na tabela
    loadLavagens() {
        const tbody = document.getElementById('lavagensTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="fas fa-car-wash fa-2x mb-2"></i><br>
                        Nenhuma lavagem registrada
                    </td>
                </tr>
            `;
            return;
        }

        this.filteredData.forEach(lavagem => {
            const veiculo = this.veiculos.find(v => v.id == lavagem.veiculoId);
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${this.formatDate(lavagem.dataLavagem)}</td>
                <td>${veiculo ? veiculo.modelo : 'Veículo não encontrado'}</td>
                <td>${veiculo ? veiculo.placa : '-'}</td>
                <td>${lavagem.tipoLavagem}</td>
                <td>${lavagem.local}</td>
                <td>${lavagem.responsavel}</td>
                <td>R$ ${lavagem.valor.toFixed(2)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editLavagem(${lavagem.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteLavagem(${lavagem.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de lavagem
        const lavagemForm = document.getElementById('lavagemForm');
        if (lavagemForm) {
            lavagemForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.applyFilters());
        }
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const lavagem = {
            id: Date.now(),
            veiculoId: parseInt(formData.get('veiculo')),
            dataLavagem: formData.get('dataLavagem'),
            tipoLavagem: formData.get('tipoLavagem'),
            local: formData.get('local'),
            responsavel: formData.get('responsavel'),
            valor: parseFloat(formData.get('valor')),
            observacoes: formData.get('observacoes'),
            servicos: formData.getAll('servicos'),
            dataRegistro: new Date().toISOString()
        };

        this.lavagens.push(lavagem);
        this.saveLavagens();
        this.filteredData = [...this.lavagens];
        this.loadLavagens();
        this.updateStats();

        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('lavagemModal'));
        modal.hide();

        // Limpar formulário
        e.target.reset();

        this.showNotification('Lavagem registrada com sucesso!', 'success');
    }

    // Salvar lavagens no localStorage
    saveLavagens() {
        localStorage.setItem('lavagens', JSON.stringify(this.lavagens));
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        
        this.filteredData = this.lavagens.filter(lavagem => {
            const veiculo = this.veiculos.find(v => v.id == lavagem.veiculoId);
            const searchMatch = !searchTerm || 
                (veiculo && veiculo.placa.toLowerCase().includes(searchTerm)) ||
                lavagem.tipoLavagem.toLowerCase().includes(searchTerm) ||
                lavagem.local.toLowerCase().includes(searchTerm);
            
            return searchMatch;
        });

        this.loadLavagens();
        this.updateStats();
    }

    // Atualizar estatísticas
    updateStats() {
        const totalLavagens = this.filteredData.length;
        const valorTotal = this.filteredData.reduce((sum, l) => sum + l.valor, 0);
        const valorMedio = totalLavagens > 0 ? valorTotal / totalLavagens : 0;

        // Atualizar elementos da interface
        const totalElement = document.getElementById('totalLavagens');
        const valorTotalElement = document.getElementById('valorTotalLavagens');
        const valorMedioElement = document.getElementById('valorMedioLavagem');

        if (totalElement) totalElement.textContent = totalLavagens;
        if (valorTotalElement) valorTotalElement.textContent = `R$ ${valorTotal.toFixed(2)}`;
        if (valorMedioElement) valorMedioElement.textContent = `R$ ${valorMedio.toFixed(2)}`;
    }

    // Formatar data
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    // Mostrar notificação
    showNotification(message, type = 'info') {
        // Implementação básica de notificação
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Funções globais
function showLavagemModal() {
    const modal = new bootstrap.Modal(document.getElementById('lavagemModal'));
    modal.show();
}

function editLavagem(id) {
    // Implementar edição
    console.log('Editar lavagem:', id);
}

function deleteLavagem(id) {
    if (confirm('Tem certeza que deseja excluir esta lavagem?')) {
        lavagemSystem.lavagens = lavagemSystem.lavagens.filter(l => l.id !== id);
        lavagemSystem.saveLavagens();
        lavagemSystem.filteredData = [...lavagemSystem.lavagens];
        lavagemSystem.loadLavagens();
        lavagemSystem.updateStats();
        lavagemSystem.showNotification('Lavagem excluída com sucesso!', 'success');
    }
}

function exportLavagens() {
    // Implementar exportação
    console.log('Exportar lavagens');
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.lavagemSystem = new LavagemSystem();
});
