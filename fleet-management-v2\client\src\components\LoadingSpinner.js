import React from 'react';
import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const SpinnerContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: ${props => props.fullScreen ? '100vh' : '200px'};
  background: ${props => props.fullScreen ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent'};
`;

const Spinner = styled.div`
  width: ${props => props.size || '50px'};
  height: ${props => props.size || '50px'};
  border: 5px solid ${props => props.color ? `${props.color}30` : 'rgba(255, 255, 255, 0.3)'};
  border-top: 5px solid ${props => props.color || 'white'};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

const LoadingText = styled.p`
  color: ${props => props.color || 'white'};
  margin-top: 1rem;
  font-size: 1rem;
  opacity: 0.9;
`;

const LoadingSpinner = ({ 
  size = '50px', 
  color = null, 
  text = null, 
  fullScreen = true 
}) => {
  return (
    <SpinnerContainer fullScreen={fullScreen}>
      <div>
        <Spinner size={size} color={color} />
        {text && <LoadingText color={color}>{text}</LoadingText>}
      </div>
    </SpinnerContainer>
  );
};

export default LoadingSpinner;
