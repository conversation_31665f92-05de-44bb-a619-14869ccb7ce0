// Sistema de Relatórios
class ReportsManager {
    constructor() {
        this.reports = this.loadReportsHistory();
        this.scheduledReports = this.loadScheduledReports();
        this.init();
    }

    init() {
        this.loadReportsHistoryTable();
        this.populateVehicleSelects();
        this.setupEventListeners();
        this.setDefaultDates();
    }

    // Carregar histórico de relatórios
    loadReportsHistory() {
        const savedReports = localStorage.getItem('frotas_reports_history');
        if (savedReports) {
            return JSON.parse(savedReports);
        }

        // Histórico padrão
        return [
            {
                id: 1,
                name: 'Relatório de Combustível - Junho 2024',
                type: 'fuel',
                generatedAt: '2024-07-01 09:30:00',
                period: '01/06/2024 - 30/06/2024',
                size: '2.3 MB',
                format: 'pdf',
                status: 'completed'
            },
            {
                id: 2,
                name: '<PERSON><PERSON><PERSON><PERSON>nções - Q2 2024',
                type: 'maintenance',
                generatedAt: '2024-06-30 14:15:00',
                period: '01/04/2024 - 30/06/2024',
                size: '1.8 MB',
                format: 'excel',
                status: 'completed'
            },
            {
                id: 3,
                name: 'Custos Operacionais - Maio 2024',
                type: 'costs',
                generatedAt: '2024-06-01 11:45:00',
                period: '01/05/2024 - 31/05/2024',
                size: '1.2 MB',
                format: 'pdf',
                status: 'completed'
            },
            {
                id: 4,
                name: 'Status da Frota - Tempo Real',
                type: 'fleet',
                generatedAt: '2024-07-02 08:00:00',
                period: 'Atual',
                size: '856 KB',
                format: 'html',
                status: 'completed'
            },
            {
                id: 5,
                name: 'Performance Comparativa - Semestre',
                type: 'performance',
                generatedAt: '2024-06-15 16:20:00',
                period: '01/01/2024 - 30/06/2024',
                size: '3.1 MB',
                format: 'pdf',
                status: 'completed'
            }
        ];
    }

    // Carregar relatórios agendados
    loadScheduledReports() {
        const savedScheduled = localStorage.getItem('frotas_scheduled_reports');
        if (savedScheduled) {
            return JSON.parse(savedScheduled);
        }

        return [
            {
                id: 1,
                name: 'Relatório Mensal de Combustível',
                type: 'fuel',
                frequency: 'monthly',
                nextExecution: '2024-08-01',
                status: 'active'
            },
            {
                id: 2,
                name: 'Relatório Semanal de Manutenção',
                type: 'maintenance',
                frequency: 'weekly',
                nextExecution: '2024-07-05',
                status: 'active'
            }
        ];
    }

    // Salvar dados
    saveReportsHistory() {
        localStorage.setItem('frotas_reports_history', JSON.stringify(this.reports));
    }

    saveScheduledReports() {
        localStorage.setItem('frotas_scheduled_reports', JSON.stringify(this.scheduledReports));
    }

    // Configurar event listeners
    setupEventListeners() {
        // Busca em tempo real
        document.getElementById('searchReports').addEventListener('input', () => {
            this.filterReportsHistory();
        });
    }

    // Definir datas padrão
    setDefaultDates() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        
        document.getElementById('reportPeriodStart').value = firstDay.toISOString().split('T')[0];
        document.getElementById('reportPeriodEnd').value = today.toISOString().split('T')[0];
    }

    // Popular selects de veículos
    populateVehicleSelects() {
        const vehicles = [
            { id: 1, plate: 'ABC-1234', model: 'Civic' },
            { id: 2, plate: 'DEF-5678', model: 'Sprinter' },
            { id: 3, plate: 'GHI-9012', model: 'Accelo' },
            { id: 4, plate: 'JKL-3456', model: 'Onix' },
            { id: 5, plate: 'MNO-7890', model: 'CG 160' }
        ];

        const select = document.getElementById('reportVehicles');
        vehicles.forEach(vehicle => {
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = `${vehicle.plate} - ${vehicle.model}`;
            select.appendChild(option);
        });
    }

    // Carregar tabela de histórico
    loadReportsHistoryTable(reportsToShow = null) {
        const tbody = document.getElementById('reportsHistoryTable');
        const reports = reportsToShow || this.reports;

        tbody.innerHTML = '';

        reports.forEach(report => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="report-info">
                        <strong>${report.name}</strong>
                        <small class="text-muted d-block">ID: ${report.id}</small>
                    </div>
                </td>
                <td>
                    <span class="badge badge-${this.getTypeBadgeClass(report.type)}">
                        ${this.getTypeLabel(report.type)}
                    </span>
                </td>
                <td>${this.formatDateTime(report.generatedAt)}</td>
                <td>${report.period}</td>
                <td>${report.size}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.downloadReport(${report.id})" title="Download">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="reportsManager.viewReport(${report.id})" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="reportsManager.shareReport(${report.id})" title="Compartilhar">
                            <i class="fas fa-share"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="reportsManager.deleteReport(${report.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Filtrar histórico de relatórios
    filterReportsHistory() {
        const search = document.getElementById('searchReports').value.toLowerCase();
        
        const filteredReports = this.reports.filter(report => {
            return report.name.toLowerCase().includes(search) ||
                   this.getTypeLabel(report.type).toLowerCase().includes(search) ||
                   report.period.toLowerCase().includes(search);
        });

        this.loadReportsHistoryTable(filteredReports);
    }

    // Gerar relatório rápido
    generateQuickReport(type) {
        const reportTypes = {
            fuel: 'Consumo de Combustível',
            maintenance: 'Manutenções',
            costs: 'Custos Operacionais',
            fleet: 'Status da Frota',
            performance: 'Performance',
            suppliers: 'Fornecedores'
        };

        const reportName = reportTypes[type] || 'Relatório';
        
        // Simular geração de relatório
        this.showLoadingMessage(`Gerando ${reportName}...`);
        
        setTimeout(() => {
            const newReport = {
                id: Math.max(...this.reports.map(r => r.id)) + 1,
                name: `${reportName} - ${new Date().toLocaleDateString('pt-BR')}`,
                type: type,
                generatedAt: new Date().toISOString(),
                period: this.getDefaultPeriod(type),
                size: this.generateRandomSize(),
                format: 'pdf',
                status: 'completed'
            };

            this.reports.unshift(newReport);
            this.saveReportsHistory();
            this.loadReportsHistoryTable();

            console.log(`✅ ${reportName} gerado com sucesso!`);
            alert(`${reportName} gerado com sucesso!`);

            // Simular download automático
            this.downloadReport(newReport.id);
        }, 2000);
    }

    // Gerar relatório personalizado
    generateCustomReport() {
        const form = document.getElementById('customReportForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const name = document.getElementById('reportName').value;
        const type = document.getElementById('reportType').value;
        const startDate = document.getElementById('reportPeriodStart').value;
        const endDate = document.getElementById('reportPeriodEnd').value;
        const format = document.getElementById('reportFormat').value;

        // Simular geração
        this.showLoadingMessage(`Gerando ${name}...`);

        setTimeout(() => {
            const newReport = {
                id: Math.max(...this.reports.map(r => r.id)) + 1,
                name: name,
                type: type,
                generatedAt: new Date().toISOString(),
                period: `${this.formatDate(startDate)} - ${this.formatDate(endDate)}`,
                size: this.generateRandomSize(),
                format: format,
                status: 'completed'
            };

            this.reports.unshift(newReport);
            this.saveReportsHistory();
            this.loadReportsHistoryTable();

            const modal = bootstrap.Modal.getInstance(document.getElementById('customReportModal'));
            modal.hide();

            console.log(`✅ Relatório "${name}" gerado com sucesso!`);
            alert(`Relatório "${name}" gerado com sucesso!`);

            // Simular download automático
            this.downloadReport(newReport.id);
        }, 3000);
    }

    // Mostrar modal de relatório personalizado
    showCustomReportModal() {
        document.getElementById('customReportForm').reset();
        this.setDefaultDates();
        
        const modal = new bootstrap.Modal(document.getElementById('customReportModal'));
        modal.show();
    }

    // Mostrar modal de agendamento
    showScheduleModal() {
        console.log('ℹ️ Funcionalidade de agendamento será implementada em breve!');
        alert('Funcionalidade de agendamento será implementada em breve!');
    }

    // Download de relatório
    downloadReport(reportId) {
        const report = this.reports.find(r => r.id === reportId);
        if (!report) return;

        // Simular download
        const link = document.createElement('a');
        link.href = '#';
        link.download = `${report.name}.${report.format}`;
        link.click();

        console.log(`✅ Download iniciado: ${report.name}`);
        alert(`Download iniciado: ${report.name}`);
    }

    // Visualizar relatório
    viewReport(reportId) {
        const report = this.reports.find(r => r.id === reportId);
        if (!report) return;

        console.log(`ℹ️ Abrindo visualização: ${report.name}`);
        alert(`Abrindo visualização: ${report.name}`);
    }

    // Compartilhar relatório
    shareReport(reportId) {
        const report = this.reports.find(r => r.id === reportId);
        if (!report) return;

        console.log('ℹ️ Funcionalidade de compartilhamento será implementada em breve!');
        alert('Funcionalidade de compartilhamento será implementada em breve!');
    }

    // Excluir relatório
    deleteReport(reportId) {
        const report = this.reports.find(r => r.id === reportId);
        if (!report) return;

        if (confirm(`Deseja realmente excluir o relatório "${report.name}"?`)) {
            this.reports = this.reports.filter(r => r.id !== reportId);
            this.saveReportsHistory();
            this.loadReportsHistoryTable();

            console.log('✅ Relatório excluído com sucesso!');
            alert('Relatório excluído com sucesso!');
        }
    }

    // Mostrar mensagem de carregamento
    showLoadingMessage(message) {
        console.log(`ℹ️ ${message}`);
        // Não mostrar alert para mensagens de carregamento
    }

    // Utilitários
    getTypeBadgeClass(type) {
        switch (type) {
            case 'fuel': return 'primary';
            case 'maintenance': return 'warning';
            case 'costs': return 'success';
            case 'fleet': return 'info';
            case 'performance': return 'secondary';
            case 'suppliers': return 'dark';
            default: return 'secondary';
        }
    }

    getTypeLabel(type) {
        switch (type) {
            case 'fuel': return 'Combustível';
            case 'maintenance': return 'Manutenção';
            case 'costs': return 'Custos';
            case 'fleet': return 'Frota';
            case 'performance': return 'Performance';
            case 'suppliers': return 'Fornecedores';
            default: return 'Outros';
        }
    }

    formatDateTime(dateTime) {
        const date = new Date(dateTime);
        return date.toLocaleString('pt-BR');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    getDefaultPeriod(type) {
        const today = new Date();
        switch (type) {
            case 'fuel':
            case 'costs':
                const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                return `${firstDay.toLocaleDateString('pt-BR')} - ${lastDay.toLocaleDateString('pt-BR')}`;
            case 'maintenance':
                const threeMonthsAgo = new Date(today);
                threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                return `${threeMonthsAgo.toLocaleDateString('pt-BR')} - ${today.toLocaleDateString('pt-BR')}`;
            case 'fleet':
                return 'Tempo Real';
            default:
                return 'Últimos 30 dias';
        }
    }

    generateRandomSize() {
        const sizes = ['1.2 MB', '2.3 MB', '856 KB', '3.1 MB', '1.8 MB', '945 KB', '2.7 MB'];
        return sizes[Math.floor(Math.random() * sizes.length)];
    }
}

// Funções globais
function generateQuickReport(type) {
    if (window.reportsManager) {
        window.reportsManager.generateQuickReport(type);
    }
}

function showCustomReportModal() {
    if (window.reportsManager) {
        window.reportsManager.showCustomReportModal();
    }
}

function generateCustomReport() {
    if (window.reportsManager) {
        window.reportsManager.generateCustomReport();
    }
}

function showScheduleModal() {
    if (window.reportsManager) {
        window.reportsManager.showScheduleModal();
    }
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.reportsManager = new ReportsManager();
});
