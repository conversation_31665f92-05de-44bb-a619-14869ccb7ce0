const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { supabase } = require('../config/supabase');
const { authenticateToken, requireAdminOrSupervisor } = require('../middleware/auth');
const router = express.Router();

// Aplicar autenticação em todas as rotas
router.use(authenticateToken);

// Validações
const vehicleValidation = [
  body('plate').isLength({ min: 7, max: 8 }).withMessage('Placa deve ter 7 ou 8 caracteres'),
  body('brand').isLength({ min: 1 }).withMessage('Marca é obrigatória'),
  body('model').isLength({ min: 1 }).withMessage('Modelo é obrigatório'),
  body('type').isIn(['carro', 'moto', 'caminhao', 'van']).withMessage('Tipo inválido'),
  body('year').isInt({ min: 1900, max: new Date().getFullYear() + 1 }).withMessage('Ano inválido'),
  body('km').isInt({ min: 0 }).withMessage('Quilometragem deve ser um número positivo'),
  body('fuel_type').isIn(['gasolina', 'etanol', 'diesel', 'flex', 'gnv']).withMessage('Tipo de combustível inválido')
];

// GET /api/vehicles - Listar veículos
router.get('/', async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, type, search } = req.query;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('vehicles')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Filtros
    if (status) {
      query = query.eq('status', status);
    }

    if (type) {
      query = query.eq('type', type);
    }

    if (search) {
      query = query.or(`plate.ilike.%${search}%,brand.ilike.%${search}%,model.ilike.%${search}%`);
    }

    // Paginação
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    res.json({
      vehicles: data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/vehicles/:id - Buscar veículo por ID
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Veículo não encontrado'
        });
      }
      throw error;
    }

    res.json(data);
  } catch (error) {
    next(error);
  }
});

// POST /api/vehicles - Criar veículo
router.post('/', requireAdminOrSupervisor, vehicleValidation, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Dados inválidos',
        details: errors.array()
      });
    }

    const vehicleData = req.body;

    // Verificar se placa já existe
    const { data: existingVehicle } = await supabase
      .from('vehicles')
      .select('id')
      .eq('plate', vehicleData.plate)
      .single();

    if (existingVehicle) {
      return res.status(409).json({
        error: 'Placa já cadastrada'
      });
    }

    const { data, error } = await supabase
      .from('vehicles')
      .insert([vehicleData])
      .select()
      .single();

    if (error) throw error;

    res.status(201).json({
      message: 'Veículo criado com sucesso',
      vehicle: data
    });
  } catch (error) {
    next(error);
  }
});

// PUT /api/vehicles/:id - Atualizar veículo
router.put('/:id', requireAdminOrSupervisor, vehicleValidation, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Dados inválidos',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const vehicleData = req.body;

    // Verificar se placa já existe em outro veículo
    const { data: existingVehicle } = await supabase
      .from('vehicles')
      .select('id')
      .eq('plate', vehicleData.plate)
      .neq('id', id)
      .single();

    if (existingVehicle) {
      return res.status(409).json({
        error: 'Placa já cadastrada em outro veículo'
      });
    }

    const { data, error } = await supabase
      .from('vehicles')
      .update(vehicleData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Veículo não encontrado'
        });
      }
      throw error;
    }

    res.json({
      message: 'Veículo atualizado com sucesso',
      vehicle: data
    });
  } catch (error) {
    next(error);
  }
});

// DELETE /api/vehicles/:id - Excluir veículo
router.delete('/:id', requireAdminOrSupervisor, async (req, res, next) => {
  try {
    const { id } = req.params;

    // Verificar se veículo tem registros associados
    const { data: fuelRecords } = await supabase
      .from('fuel_records')
      .select('id')
      .eq('vehicle_id', id)
      .limit(1);

    if (fuelRecords && fuelRecords.length > 0) {
      return res.status(409).json({
        error: 'Não é possível excluir veículo com registros associados'
      });
    }

    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id);

    if (error) throw error;

    res.json({
      message: 'Veículo excluído com sucesso'
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/vehicles/stats - Estatísticas dos veículos
router.get('/stats/summary', async (req, res, next) => {
  try {
    const { data: vehicles, error } = await supabase
      .from('vehicles')
      .select('status, type');

    if (error) throw error;

    const stats = {
      total: vehicles.length,
      byStatus: {},
      byType: {}
    };

    vehicles.forEach(vehicle => {
      // Por status
      stats.byStatus[vehicle.status] = (stats.byStatus[vehicle.status] || 0) + 1;
      
      // Por tipo
      stats.byType[vehicle.type] = (stats.byType[vehicle.type] || 0) + 1;
    });

    res.json(stats);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
