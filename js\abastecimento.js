// Sistema de Abastecimento
class AbastecimentoSystem {
    constructor() {
        this.abastecimentos = JSON.parse(localStorage.getItem('abastecimentos')) || [];
        this.veiculos = JSON.parse(localStorage.getItem('veiculos')) || this.getDefaultVeiculos();
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filteredData = [...this.abastecimentos];
        
        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        this.loadVeiculos();
        this.loadAbastecimentos();
        this.setupEventListeners();
        this.updateStats();
    }

    // Dados padrão de veículos
    getDefaultVeiculos() {
        return [
            { id: 1, modelo: 'Honda Civic 2020', placa: 'ABC-1234', tipo: 'gasolina' },
            { id: 2, modelo: 'Toyota Corolla 2019', placa: 'DEF-5678', tipo: 'gasolina' },
            { id: 3, modelo: 'Hyundai HB20 2021', placa: 'GHI-9012', tipo: 'gasolina' },
            { id: 4, modelo: 'Volkswagen Gol 2020', placa: 'JKL-3456', tipo: 'gasolina' },
            { id: 5, modelo: 'Ford Ka 2019', placa: 'MNO-7890', tipo: 'gasolina' }
        ];
    }



    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        const filterVeiculo = document.getElementById('filterVeiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }

        if (filterVeiculo) {
            filterVeiculo.innerHTML = '<option value="">Todos os veículos</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                filterVeiculo.appendChild(option);
            });
        }
    }

    // Carregar abastecimentos na tabela
    loadAbastecimentos() {
        const tbody = document.getElementById('abastecimentosTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        pageData.forEach(abastecimento => {
            const veiculo = this.veiculos.find(v => v.id == abastecimento.veiculoId);
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${Utils.formatDate(abastecimento.data)}</td>
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td>${abastecimento.posto}</td>
                <td>${abastecimento.litros}L</td>
                <td>${Utils.formatCurrency(abastecimento.valorTotal)}</td>
                <td>${Utils.formatKm(abastecimento.kmAtual)}</td>
                <td>${abastecimento.consumo ? abastecimento.consumo.toFixed(1) + ' km/L' : 'N/A'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="abastecimentoSystem.editAbastecimento(${abastecimento.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="abastecimentoSystem.deleteAbastecimento(${abastecimento.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });

        this.updatePagination();
    }

    // Atualizar paginação
    updatePagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        pagination.innerHTML = '';

        // Botão anterior
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${this.currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="abastecimentoSystem.changePage(${this.currentPage - 1})">Anterior</a>`;
        pagination.appendChild(prevLi);

        // Números das páginas
        for (let i = 1; i <= totalPages; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === this.currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="abastecimentoSystem.changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
        }

        // Botão próximo
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${this.currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="abastecimentoSystem.changePage(${this.currentPage + 1})">Próximo</a>`;
        pagination.appendChild(nextLi);
    }

    // Mudar página
    changePage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.loadAbastecimentos();
        }
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de abastecimento
        const form = document.getElementById('abastecimentoForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Cálculo automático do valor total
        const litros = document.getElementById('litros');
        const valorLitro = document.getElementById('valorLitro');
        const valorTotal = document.getElementById('valorTotal');

        if (litros && valorLitro && valorTotal) {
            [litros, valorLitro].forEach(input => {
                input.addEventListener('input', () => {
                    const l = parseFloat(litros.value) || 0;
                    const vl = parseFloat(valorLitro.value) || 0;
                    valorTotal.value = (l * vl).toFixed(2);
                });
            });
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        const filterPeriodo = document.getElementById('filterPeriodo');
        const filterVeiculo = document.getElementById('filterVeiculo');

        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => this.applyFilters(), 300));
        }

        if (filterPeriodo) {
            filterPeriodo.addEventListener('change', () => this.applyFilters());
        }

        if (filterVeiculo) {
            filterVeiculo.addEventListener('change', () => this.applyFilters());
        }
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const periodo = document.getElementById('filterPeriodo')?.value || '';
        const veiculoId = document.getElementById('filterVeiculo')?.value || '';

        this.filteredData = this.abastecimentos.filter(abastecimento => {
            const veiculo = this.veiculos.find(v => v.id == abastecimento.veiculoId);
            
            // Filtro de busca
            const matchesSearch = !searchTerm || 
                (veiculo && (veiculo.modelo.toLowerCase().includes(searchTerm) || 
                veiculo.placa.toLowerCase().includes(searchTerm))) ||
                abastecimento.posto.toLowerCase().includes(searchTerm);

            // Filtro de período
            const matchesPeriodo = !periodo || this.matchesPeriodo(abastecimento.data, periodo);

            // Filtro de veículo
            const matchesVeiculo = !veiculoId || abastecimento.veiculoId == veiculoId;

            return matchesSearch && matchesPeriodo && matchesVeiculo;
        });

        this.currentPage = 1;
        this.loadAbastecimentos();
        this.updateStats();
    }

    // Verificar se data corresponde ao período
    matchesPeriodo(data, periodo) {
        const dataAbastecimento = new Date(data);
        const hoje = new Date();
        
        switch (periodo) {
            case 'hoje':
                return dataAbastecimento.toDateString() === hoje.toDateString();
            case 'semana':
                const inicioSemana = new Date(hoje);
                inicioSemana.setDate(hoje.getDate() - hoje.getDay());
                return dataAbastecimento >= inicioSemana;
            case 'mes':
                return dataAbastecimento.getMonth() === hoje.getMonth() && 
                       dataAbastecimento.getFullYear() === hoje.getFullYear();
            case 'trimestre':
                const trimestre = Math.floor(hoje.getMonth() / 3);
                const trimestreAbastecimento = Math.floor(dataAbastecimento.getMonth() / 3);
                return trimestreAbastecimento === trimestre && 
                       dataAbastecimento.getFullYear() === hoje.getFullYear();
            default:
                return true;
        }
    }

    // Atualizar estatísticas
    updateStats() {
        const totalAbastecimentos = this.filteredData.length;
        const totalLitros = this.filteredData.reduce((sum, a) => sum + a.litros, 0);
        const totalGasto = this.filteredData.reduce((sum, a) => sum + a.valorTotal, 0);
        const mediaConsumo = this.filteredData.length > 0 ? 
            this.filteredData.reduce((sum, a) => sum + (a.consumo || 0), 0) / this.filteredData.length : 0;

        document.getElementById('totalAbastecimentos').textContent = totalAbastecimentos;
        document.getElementById('totalLitros').textContent = totalLitros.toFixed(1) + 'L';
        document.getElementById('totalGasto').textContent = Utils.formatCurrency(totalGasto);
        document.getElementById('mediaConsumo').textContent = mediaConsumo.toFixed(1) + ' km/L';
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        
        // Calcular consumo se possível
        const kmAtual = parseInt(data.kmAtual);
        const ultimoAbastecimento = this.abastecimentos
            .filter(a => a.veiculoId == data.veiculo)
            .sort((a, b) => new Date(b.data) - new Date(a.data))[0];
        
        let consumo = null;
        if (ultimoAbastecimento && kmAtual > ultimoAbastecimento.kmAtual) {
            const kmPercorridos = kmAtual - ultimoAbastecimento.kmAtual;
            consumo = kmPercorridos / parseFloat(data.litros);
        }

        const abastecimento = {
            id: Date.now(),
            veiculoId: parseInt(data.veiculo),
            data: data.dataAbastecimento,
            posto: data.posto,
            tipoCombustivel: data.tipoCombustivel,
            litros: parseFloat(data.litros),
            valorLitro: parseFloat(data.valorLitro),
            valorTotal: parseFloat(data.valorTotal),
            kmAtual: kmAtual,
            kmAnterior: ultimoAbastecimento ? ultimoAbastecimento.kmAtual : null,
            consumo: consumo,
            observacoes: data.observacoes || ''
        };

        this.abastecimentos.unshift(abastecimento);
        localStorage.setItem('abastecimentos', JSON.stringify(this.abastecimentos));
        
        this.filteredData = [...this.abastecimentos];
        this.loadAbastecimentos();
        this.updateStats();
        
        // Fechar modal e limpar formulário
        const modal = bootstrap.Modal.getInstance(document.getElementById('abastecimentoModal'));
        modal.hide();
        e.target.reset();
        
        console.log('✅ Abastecimento registrado com sucesso!');
        alert('Abastecimento registrado com sucesso!');
    }

    // Editar abastecimento
    editAbastecimento(id) {
        const abastecimento = this.abastecimentos.find(a => a.id === id);
        if (!abastecimento) return;

        // Preencher formulário com dados existentes
        document.getElementById('veiculo').value = abastecimento.veiculoId;
        document.getElementById('dataAbastecimento').value = abastecimento.data;
        document.getElementById('posto').value = abastecimento.posto;
        document.getElementById('tipoCombustivel').value = abastecimento.tipoCombustivel;
        document.getElementById('litros').value = abastecimento.litros;
        document.getElementById('valorLitro').value = abastecimento.valorLitro;
        document.getElementById('valorTotal').value = abastecimento.valorTotal;
        document.getElementById('kmAtual').value = abastecimento.kmAtual;
        document.getElementById('observacoes').value = abastecimento.observacoes;

        // Abrir modal
        const modal = new bootstrap.Modal(document.getElementById('abastecimentoModal'));
        modal.show();

        // Alterar comportamento do formulário para edição
        const form = document.getElementById('abastecimentoForm');
        form.dataset.editId = id;
    }

    // Deletar abastecimento
    deleteAbastecimento(id) {
        if (confirm('Deseja realmente excluir este abastecimento?')) {
            this.abastecimentos = this.abastecimentos.filter(a => a.id !== id);
            localStorage.setItem('abastecimentos', JSON.stringify(this.abastecimentos));
            
            this.filteredData = [...this.abastecimentos];
            this.loadAbastecimentos();
            this.updateStats();
            
            console.log('✅ Abastecimento excluído com sucesso!');
            alert('Abastecimento excluído com sucesso!');
        }
    }

    // Exportar dados
    exportAbastecimentos() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `abastecimentos_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Gerar CSV
    generateCSV() {
        const headers = ['Data', 'Veículo', 'Placa', 'Posto', 'Tipo Combustível', 'Litros', 'Valor/L', 'Valor Total', 'KM', 'Consumo', 'Observações'];
        const rows = this.filteredData.map(abastecimento => {
            const veiculo = this.veiculos.find(v => v.id == abastecimento.veiculoId);
            return [
                Utils.formatDate(abastecimento.data),
                veiculo ? veiculo.modelo : 'N/A',
                veiculo ? veiculo.placa : 'N/A',
                abastecimento.posto,
                abastecimento.tipoCombustivel,
                abastecimento.litros,
                abastecimento.valorLitro,
                abastecimento.valorTotal,
                abastecimento.kmAtual,
                abastecimento.consumo || 'N/A',
                abastecimento.observacoes
            ];
        });

        return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
    }
}

// Funções globais
function openAbastecimentoModal() {
    // Limpar dados de edição
    const form = document.getElementById('abastecimentoForm');
    form.removeAttribute('data-edit-id');
    form.reset();
    
    // Definir data atual
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    document.getElementById('dataAbastecimento').value = now.toISOString().slice(0, 16);
    
    const modal = new bootstrap.Modal(document.getElementById('abastecimentoModal'));
    modal.show();
}

function exportAbastecimentos() {
    abastecimentoSystem.exportAbastecimentos();
}

// Inicializar sistema quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('abastecimentosTable')) {
        window.abastecimentoSystem = new AbastecimentoSystem();
    }
});
