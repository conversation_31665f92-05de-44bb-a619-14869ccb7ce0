// Sistema de Abastecimento
class AbastecimentoSystem {
    constructor() {
        this.abastecimentos = JSON.parse(localStorage.getItem('abastecimentos')) || [];
        this.veiculos = this.getVeiculosFromStorage();
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filteredData = [...this.abastecimentos];

        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        this.loadVeiculos();
        this.loadAbastecimentos();
        this.setupEventListeners();
        this.updateStats();
    }

    // Carregar veículos do sistema
    getVeiculosFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                return vehicles.map(v => ({
                    id: v.id,
                    modelo: `${v.brand} ${v.model} ${v.year}`,
                    placa: v.plate,
                    tipo: v.fuel || 'gasolina'
                }));
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
                return [];
            }
        }
        return [];
    }



    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        const filterVeiculo = document.getElementById('filterVeiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }

        if (filterVeiculo) {
            filterVeiculo.innerHTML = '<option value="">Todos os veículos</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                filterVeiculo.appendChild(option);
            });
        }
    }

    // Carregar abastecimentos na tabela
    loadAbastecimentos() {
        const tbody = document.getElementById('abastecimentosTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        pageData.forEach(abastecimento => {
            const veiculo = this.veiculos.find(v => v.id == abastecimento.veiculoId);
            const row = document.createElement('tr');
            
            // Calcular status baseado nos campos cupom e nota fiscal
            const statusInfo = this.calculateStatusInfo(abastecimento);

            row.innerHTML = `
                <td>${Utils.formatDate(abastecimento.data)}</td>
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td>${abastecimento.motorista || '-'}</td>
                <td>${abastecimento.posto}</td>
                <td>${abastecimento.litros}L</td>
                <td>${Utils.formatCurrency(abastecimento.valorTotal)}</td>
                <td>${Utils.formatKm(abastecimento.kmAtual)}</td>
                <td>${abastecimento.consumo ? abastecimento.consumo.toFixed(1) + ' km/L' : 'N/A'}</td>
                <td>${this.formatCupomFiscal(abastecimento.cupomFiscal)}</td>
                <td>${this.formatNotaFiscal(abastecimento.notaFiscal)}</td>
                <td>${statusInfo.html}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="abastecimentoSystem.editAbastecimento(${abastecimento.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="abastecimentoSystem.deleteAbastecimento(${abastecimento.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });

        this.updatePagination();
    }

    // Atualizar paginação
    updatePagination() {
        const pagination = document.getElementById('pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        pagination.innerHTML = '';

        // Botão anterior
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${this.currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="abastecimentoSystem.changePage(${this.currentPage - 1})">Anterior</a>`;
        pagination.appendChild(prevLi);

        // Números das páginas
        for (let i = 1; i <= totalPages; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === this.currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="abastecimentoSystem.changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
        }

        // Botão próximo
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${this.currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="abastecimentoSystem.changePage(${this.currentPage + 1})">Próximo</a>`;
        pagination.appendChild(nextLi);
    }

    // Mudar página
    changePage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.loadAbastecimentos();
        }
    }

    // Calcular informações de status baseado nos campos cupom e nota fiscal
    calculateStatusInfo(abastecimento) {
        const hasCupom = abastecimento.cupomFiscal && abastecimento.cupomFiscal.trim() !== '';
        const hasNota = abastecimento.notaFiscal && abastecimento.notaFiscal.trim() !== '';

        let status = '';
        let statusClass = '';
        let statusIcon = '';
        let dataStatus = '';

        if (hasNota && hasCupom) {
            status = 'Completo';
            statusClass = 'text-success';
            statusIcon = 'fas fa-check-circle';
            dataStatus = Utils.formatDate(abastecimento.data);
        } else if (hasCupom) {
            status = 'Cupom OK';
            statusClass = 'text-warning';
            statusIcon = 'fas fa-receipt';
            dataStatus = Utils.formatDate(abastecimento.data);
        } else if (hasNota) {
            status = 'Nota OK';
            statusClass = 'text-info';
            statusIcon = 'fas fa-file-invoice';
            dataStatus = Utils.formatDate(abastecimento.data);
        } else {
            status = 'Pendente';
            statusClass = 'text-muted';
            statusIcon = 'fas fa-clock';
            dataStatus = '-';
        }

        const html = `
            <div class="${statusClass}">
                <i class="${statusIcon}"></i>
                <small class="d-block">${status}</small>
                <small class="text-muted">${dataStatus}</small>
            </div>
        `;

        return { status, statusClass, statusIcon, dataStatus, html };
    }

    // Formatar cupom fiscal
    formatCupomFiscal(cupomFiscal) {
        if (!cupomFiscal || cupomFiscal.trim() === '') {
            return '<span class="text-muted">-</span>';
        }
        return `<span class="text-success"><i class="fas fa-receipt"></i> ${cupomFiscal}</span>`;
    }

    // Formatar nota fiscal
    formatNotaFiscal(notaFiscal) {
        if (!notaFiscal || notaFiscal.trim() === '') {
            return '<span class="text-muted">-</span>';
        }
        return `<span class="text-info"><i class="fas fa-file-invoice"></i> ${notaFiscal}</span>`;
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de abastecimento
        const form = document.getElementById('abastecimentoForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Cálculo automático do valor total
        const litros = document.getElementById('litros');
        const valorLitro = document.getElementById('valorLitro');
        const valorTotal = document.getElementById('valorTotal');

        if (litros && valorLitro && valorTotal) {
            [litros, valorLitro].forEach(input => {
                input.addEventListener('input', () => {
                    const l = parseFloat(litros.value) || 0;
                    const vl = parseFloat(valorLitro.value) || 0;
                    valorTotal.value = (l * vl).toFixed(2);
                });
            });
        }

        // Event listeners para cupom e nota fiscal (para atualização de status)
        const cupomFiscal = document.getElementById('cupomFiscal');
        const notaFiscal = document.getElementById('notaFiscal');

        if (cupomFiscal) {
            cupomFiscal.addEventListener('input', () => {
                console.log('📄 Cupom fiscal alterado:', cupomFiscal.value);
            });
        }

        if (notaFiscal) {
            notaFiscal.addEventListener('input', () => {
                console.log('📋 Nota fiscal alterada:', notaFiscal.value);
            });
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        const filterPeriodo = document.getElementById('filterPeriodo');
        const filterVeiculo = document.getElementById('filterVeiculo');

        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => this.applyFilters(), 300));
        }

        if (filterPeriodo) {
            filterPeriodo.addEventListener('change', () => this.applyFilters());
        }

        if (filterVeiculo) {
            filterVeiculo.addEventListener('change', () => this.applyFilters());
        }
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const periodo = document.getElementById('filterPeriodo')?.value || '';
        const veiculoId = document.getElementById('filterVeiculo')?.value || '';

        this.filteredData = this.abastecimentos.filter(abastecimento => {
            const veiculo = this.veiculos.find(v => v.id == abastecimento.veiculoId);
            
            // Filtro de busca
            const matchesSearch = !searchTerm || 
                (veiculo && (veiculo.modelo.toLowerCase().includes(searchTerm) || 
                veiculo.placa.toLowerCase().includes(searchTerm))) ||
                abastecimento.posto.toLowerCase().includes(searchTerm);

            // Filtro de período
            const matchesPeriodo = !periodo || this.matchesPeriodo(abastecimento.data, periodo);

            // Filtro de veículo
            const matchesVeiculo = !veiculoId || abastecimento.veiculoId == veiculoId;

            return matchesSearch && matchesPeriodo && matchesVeiculo;
        });

        this.currentPage = 1;
        this.loadAbastecimentos();
        this.updateStats();
    }

    // Verificar se data corresponde ao período
    matchesPeriodo(data, periodo) {
        const dataAbastecimento = new Date(data);
        const hoje = new Date();
        
        switch (periodo) {
            case 'hoje':
                return dataAbastecimento.toDateString() === hoje.toDateString();
            case 'semana':
                const inicioSemana = new Date(hoje);
                inicioSemana.setDate(hoje.getDate() - hoje.getDay());
                return dataAbastecimento >= inicioSemana;
            case 'mes':
                return dataAbastecimento.getMonth() === hoje.getMonth() && 
                       dataAbastecimento.getFullYear() === hoje.getFullYear();
            case 'trimestre':
                const trimestre = Math.floor(hoje.getMonth() / 3);
                const trimestreAbastecimento = Math.floor(dataAbastecimento.getMonth() / 3);
                return trimestreAbastecimento === trimestre && 
                       dataAbastecimento.getFullYear() === hoje.getFullYear();
            default:
                return true;
        }
    }

    // Atualizar estatísticas
    updateStats() {
        const totalAbastecimentos = this.filteredData.length;
        const totalLitros = this.filteredData.reduce((sum, a) => sum + a.litros, 0);
        const totalGasto = this.filteredData.reduce((sum, a) => sum + a.valorTotal, 0);

        // Calcular média de consumo apenas dos abastecimentos que têm consumo calculado
        const abastecimentosComConsumo = this.filteredData.filter(a => a.consumo && a.consumo > 0);
        const mediaConsumo = abastecimentosComConsumo.length > 0 ?
            abastecimentosComConsumo.reduce((sum, a) => sum + a.consumo, 0) / abastecimentosComConsumo.length : 0;

        console.log('📊 Estatísticas de Abastecimento:', {
            totalAbastecimentos,
            totalLitros: totalLitros.toFixed(1),
            totalGasto: totalGasto.toFixed(2),
            abastecimentosComConsumo: abastecimentosComConsumo.length,
            mediaConsumo: mediaConsumo.toFixed(2),
            consumos: abastecimentosComConsumo.map(a => a.consumo.toFixed(2))
        });

        // Atualizar elementos da interface
        const totalAbastecimentosEl = document.getElementById('totalAbastecimentos');
        const totalLitrosEl = document.getElementById('totalLitros');
        const totalGastoEl = document.getElementById('totalGasto');
        const mediaConsumoEl = document.getElementById('mediaConsumo');

        if (totalAbastecimentosEl) totalAbastecimentosEl.textContent = totalAbastecimentos;
        if (totalLitrosEl) totalLitrosEl.textContent = totalLitros.toFixed(1) + 'L';
        if (totalGastoEl) totalGastoEl.textContent = Utils.formatCurrency(totalGasto);
        if (mediaConsumoEl) {
            mediaConsumoEl.textContent = mediaConsumo > 0 ?
                mediaConsumo.toFixed(1) + ' km/L' :
                'Aguardando dados';
        }
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        const editId = e.target.dataset.editId;

        // Calcular consumo se possível
        const kmAtual = parseInt(data.kmAtual);
        const litros = parseFloat(data.litros);

        // Buscar último abastecimento do mesmo veículo (excluindo o atual se estiver editando)
        // Ordenar por data e depois por ID para garantir ordem cronológica
        const abastecimentosDoVeiculo = this.abastecimentos
            .filter(a => a.veiculoId == data.veiculo && (!editId || a.id != editId))
            .sort((a, b) => {
                const dateA = new Date(a.data);
                const dateB = new Date(b.data);
                if (dateA.getTime() === dateB.getTime()) {
                    return b.id - a.id; // Se mesma data, ordenar por ID (mais recente primeiro)
                }
                return dateB - dateA; // Ordenar por data (mais recente primeiro)
            });

        const ultimoAbastecimento = abastecimentosDoVeiculo[0];

        let consumo = null;
        let kmAnterior = null;

        if (ultimoAbastecimento) {
            kmAnterior = ultimoAbastecimento.kmAtual;

            // Verificar se os dados são válidos para calcular consumo
            if (kmAtual > kmAnterior && litros > 0) {
                const kmPercorridos = kmAtual - kmAnterior;
                consumo = kmPercorridos / litros;

                console.log(`📊 Calculando consumo:`, {
                    kmAtual,
                    kmAnterior,
                    kmPercorridos,
                    litros,
                    consumo: consumo.toFixed(2) + ' km/L'
                });
            } else {
                console.log(`⚠️ Não foi possível calcular consumo:`, {
                    kmAtual,
                    kmAnterior,
                    litros,
                    motivo: kmAtual <= kmAnterior ? 'KM atual menor ou igual ao anterior' : 'Litros inválidos'
                });
            }
        } else {
            console.log(`ℹ️ Primeiro abastecimento do veículo - consumo não calculado`);
        }

        const abastecimento = {
            id: editId ? parseInt(editId) : Date.now(),
            veiculoId: parseInt(data.veiculo),
            data: data.dataAbastecimento,
            motorista: data.motorista || '',
            posto: data.posto,
            tipoCombustivel: data.tipoCombustivel,
            litros: litros,
            valorLitro: parseFloat(data.valorLitro),
            valorTotal: parseFloat(data.valorTotal),
            kmAtual: kmAtual,
            kmAnterior: kmAnterior,
            consumo: consumo,
            cupomFiscal: data.cupomFiscal || '',
            notaFiscal: data.notaFiscal || '',
            observacoes: data.observacoes || ''
        };

        if (editId) {
            // Atualizar abastecimento existente
            const index = this.abastecimentos.findIndex(a => a.id == editId);
            if (index !== -1) {
                this.abastecimentos[index] = abastecimento;
                console.log('✅ Abastecimento atualizado com sucesso!');
                alert('Abastecimento atualizado com sucesso!');
            }
        } else {
            // Criar novo abastecimento
            this.abastecimentos.unshift(abastecimento);
            console.log('✅ Abastecimento registrado com sucesso!');
            alert('Abastecimento registrado com sucesso!');
        }

        localStorage.setItem('abastecimentos', JSON.stringify(this.abastecimentos));

        this.filteredData = [...this.abastecimentos];
        this.loadAbastecimentos();
        this.updateStats();

        // Fechar modal e limpar formulário
        const modal = bootstrap.Modal.getInstance(document.getElementById('abastecimentoModal'));
        modal.hide();
        e.target.reset();
        e.target.removeAttribute('data-edit-id');
    }

    // Recalcular consumo de todos os abastecimentos
    recalcularConsumos() {
        console.log('🔄 Recalculando consumos de todos os abastecimentos...');

        // Agrupar abastecimentos por veículo
        const abastecimentosPorVeiculo = {};
        this.abastecimentos.forEach(abast => {
            if (!abastecimentosPorVeiculo[abast.veiculoId]) {
                abastecimentosPorVeiculo[abast.veiculoId] = [];
            }
            abastecimentosPorVeiculo[abast.veiculoId].push(abast);
        });

        // Recalcular consumo para cada veículo
        Object.keys(abastecimentosPorVeiculo).forEach(veiculoId => {
            const abastecimentos = abastecimentosPorVeiculo[veiculoId]
                .sort((a, b) => {
                    const dateA = new Date(a.data);
                    const dateB = new Date(b.data);
                    if (dateA.getTime() === dateB.getTime()) {
                        return a.id - b.id; // Se mesma data, ordenar por ID (mais antigo primeiro)
                    }
                    return dateA - dateB; // Ordenar por data (mais antigo primeiro)
                });

            // Recalcular consumo para cada abastecimento (exceto o primeiro)
            for (let i = 1; i < abastecimentos.length; i++) {
                const atual = abastecimentos[i];
                const anterior = abastecimentos[i - 1];

                if (atual.kmAtual > anterior.kmAtual && atual.litros > 0) {
                    const kmPercorridos = atual.kmAtual - anterior.kmAtual;
                    atual.consumo = kmPercorridos / atual.litros;
                    atual.kmAnterior = anterior.kmAtual;

                    console.log(`✅ Consumo recalculado para abastecimento ${atual.id}: ${atual.consumo.toFixed(2)} km/L`);
                } else {
                    atual.consumo = null;
                    atual.kmAnterior = anterior.kmAtual;
                    console.log(`⚠️ Não foi possível calcular consumo para abastecimento ${atual.id}`);
                }
            }

            // Primeiro abastecimento não tem consumo
            if (abastecimentos.length > 0) {
                abastecimentos[0].consumo = null;
                abastecimentos[0].kmAnterior = null;
            }
        });

        // Salvar alterações
        localStorage.setItem('abastecimentos', JSON.stringify(this.abastecimentos));
        this.filteredData = [...this.abastecimentos];
        this.loadAbastecimentos();
        this.updateStats();

        console.log('✅ Recálculo de consumos concluído!');
    }

    // Editar abastecimento
    editAbastecimento(id) {
        const abastecimento = this.abastecimentos.find(a => a.id === id);
        if (!abastecimento) return;

        // Preencher formulário com dados existentes
        document.getElementById('veiculo').value = abastecimento.veiculoId;
        document.getElementById('dataAbastecimento').value = abastecimento.data;
        document.getElementById('motorista').value = abastecimento.motorista || '';
        document.getElementById('posto').value = abastecimento.posto;
        document.getElementById('tipoCombustivel').value = abastecimento.tipoCombustivel;
        document.getElementById('cupomFiscal').value = abastecimento.cupomFiscal || '';
        document.getElementById('litros').value = abastecimento.litros;
        document.getElementById('valorLitro').value = abastecimento.valorLitro;
        document.getElementById('valorTotal').value = abastecimento.valorTotal;
        document.getElementById('notaFiscal').value = abastecimento.notaFiscal || '';
        document.getElementById('kmAtual').value = abastecimento.kmAtual;
        document.getElementById('observacoes').value = abastecimento.observacoes;

        // Abrir modal
        const modal = new bootstrap.Modal(document.getElementById('abastecimentoModal'));
        modal.show();

        // Alterar comportamento do formulário para edição
        const form = document.getElementById('abastecimentoForm');
        form.dataset.editId = id;
    }

    // Deletar abastecimento
    deleteAbastecimento(id) {
        if (confirm('Deseja realmente excluir este abastecimento?')) {
            this.abastecimentos = this.abastecimentos.filter(a => a.id !== id);
            localStorage.setItem('abastecimentos', JSON.stringify(this.abastecimentos));
            
            this.filteredData = [...this.abastecimentos];
            this.loadAbastecimentos();
            this.updateStats();
            
            console.log('✅ Abastecimento excluído com sucesso!');
            alert('Abastecimento excluído com sucesso!');
        }
    }

    // Exportar dados
    exportAbastecimentos() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `abastecimentos_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Gerar CSV
    generateCSV() {
        const headers = ['Data', 'Veículo', 'Placa', 'Motorista', 'Posto', 'Tipo Combustível', 'Litros', 'Valor/L', 'Valor Total', 'KM', 'Consumo', 'Cupom Fiscal', 'Nota Fiscal', 'Status', 'Observações'];
        const rows = this.filteredData.map(abastecimento => {
            const veiculo = this.veiculos.find(v => v.id == abastecimento.veiculoId);
            const statusInfo = this.calculateStatusInfo(abastecimento);
            return [
                Utils.formatDate(abastecimento.data),
                veiculo ? veiculo.modelo : 'N/A',
                veiculo ? veiculo.placa : 'N/A',
                abastecimento.motorista || '-',
                abastecimento.posto,
                abastecimento.tipoCombustivel,
                abastecimento.litros,
                abastecimento.valorLitro,
                abastecimento.valorTotal,
                abastecimento.kmAtual,
                abastecimento.consumo || 'N/A',
                abastecimento.cupomFiscal || '-',
                abastecimento.notaFiscal || '-',
                statusInfo.status,
                abastecimento.observacoes
            ];
        });

        return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
    }
}

// Funções globais
function openAbastecimentoModal() {
    // Limpar dados de edição
    const form = document.getElementById('abastecimentoForm');
    form.removeAttribute('data-edit-id');
    form.reset();
    
    // Definir data atual
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    document.getElementById('dataAbastecimento').value = now.toISOString().slice(0, 16);
    
    const modal = new bootstrap.Modal(document.getElementById('abastecimentoModal'));
    modal.show();
}

function exportAbastecimentos() {
    abastecimentoSystem.exportAbastecimentos();
}

function recalcularConsumos() {
    if (confirm('Deseja recalcular o consumo de todos os abastecimentos? Esta ação irá sobrescrever os valores atuais.')) {
        abastecimentoSystem.recalcularConsumos();
        alert('Consumos recalculados com sucesso!');
    }
}

// Inicializar sistema quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('abastecimentosTable')) {
        window.abastecimentoSystem = new AbastecimentoSystem();
    }
});
