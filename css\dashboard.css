/* Dashboard Layout */
body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    overflow-x: hidden;
}

/* Sidebar */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #3498db;
}

.sidebar-header h4 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar.collapsed .sidebar-header h4 {
    display: none;
}

/* Sidebar Navigation */
.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.sidebar-nav li {
    margin: 5px 0;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #3498db;
}

.sidebar-nav a i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.sidebar.collapsed .sidebar-nav a span {
    display: none;
}

/* Submenu */
.has-submenu {
    position: relative;
}

.has-submenu > a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.has-submenu > a::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-left: auto;
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.has-submenu.open > a::after {
    transform: rotate(180deg);
}

.has-submenu:hover > a {
    background: rgba(255, 255, 255, 0.1);
}

.submenu {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 8px 8px;
    margin-top: 2px;
}

.submenu.open {
    max-height: 300px;
    padding: 5px 0;
}

.submenu li {
    list-style: none;
}

.submenu a {
    padding: 12px 20px 12px 60px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    border-radius: 0;
}

.submenu a:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding-left: 65px;
}

.submenu a i {
    font-size: 0.8rem;
    width: 16px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: 260px;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-left: 70px;
}

/* Top Bar */
.topbar {
    background: white;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f0f0f0;
    color: #333;
}

.topbar h1 {
    margin: 0;
    font-size: 1.8rem;
    color: #2c3e50;
    font-weight: 600;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Notifications */
.notifications {
    position: relative;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.notifications:hover {
    background: #f0f0f0;
}

.notifications i {
    font-size: 1.2rem;
    color: #666;
}

.notification-count {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 25px;
    transition: background 0.3s ease;
    position: relative;
}

.user-menu:hover {
    background: #f0f0f0;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: #333;
}

.dropdown {
    position: relative;
}

.user-menu {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.user-menu:hover {
    background: rgba(0, 0, 0, 0.05);
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: calc(100% + 5px);
    background: white;
    min-width: 180px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    padding: 10px 0;
    z-index: 1000;
    border: 1px solid rgba(0, 0, 0, 0.1);
    animation: dropdownFadeIn 0.2s ease;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-content::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
}

.dropdown-content a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.dropdown-content a:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

.dropdown-content a i {
    width: 16px;
    text-align: center;
    color: #666;
}

.dropdown-content a:hover i {
    color: #2c3e50;
}

/* Configuration Pages Styles */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.content-title h2 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1.8rem;
    font-weight: 600;
}

.content-title p {
    color: #6c757d;
    margin: 0;
    font-size: 1rem;
}

.content-actions .btn {
    padding: 10px 20px;
    font-weight: 500;
}

.filters-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.filters-container label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.table {
    margin: 0;
}

.table th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
    border-bottom: 2px solid #e9ecef;
}

.table td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-buttons .btn {
    padding: 5px 8px;
    font-size: 0.8rem;
}

.badge {
    padding: 6px 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 20px;
}

.badge-primary { background: #007bff; color: white; }
.badge-success { background: #28a745; color: white; }
.badge-warning { background: #ffc107; color: #212529; }
.badge-danger { background: #dc3545; color: white; }
.badge-info { background: #17a2b8; color: white; }
.badge-secondary { background: #6c757d; color: white; }

.user-info, .vehicle-info, .supplier-info {
    display: flex;
    flex-direction: column;
}

.user-info strong, .vehicle-info strong, .supplier-info strong {
    color: #2c3e50;
    font-size: 0.95rem;
}

.user-info small, .vehicle-info small, .supplier-info small {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 2px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    color: #2c3e50;
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    color: #495057;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.sidebar-nav .active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
}

/* Charts Page Styles */
.charts-container {
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chart-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.chart-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-header p {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.chart-body {
    padding: 20px;
    height: 300px;
    position: relative;
}

.chart-body canvas {
    max-height: 100%;
}

.statistics-summary {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid #e9ecef;
}

.statistics-summary .stat-card {
    margin-bottom: 20px;
}

.statistics-summary .stat-content small {
    display: block;
    margin-top: 5px;
    font-size: 0.75rem;
    color: #6c757d;
}

/* Enhanced Dashboard Styles */
.enhanced-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
}

.header-title {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.title-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
}

.title-content h1 {
    margin: 0 0 8px 0;
    font-size: 2.2rem;
    font-weight: 700;
}

.title-content p {
    margin: 0 0 15px 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.header-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.header-badges .badge {
    padding: 8px 12px;
    font-size: 0.9rem;
    border-radius: 20px;
}

.header-actions {
    display: flex;
    align-items: center;
}

.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-buttons .btn {
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 500;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.action-buttons .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.metrics-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.metric-content {
    display: flex;
    flex-direction: column;
}

.metric-value {
    font-size: 1.4rem;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.metric-label {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 2px;
}

/* Advanced Filters */
.advanced-filters {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.filters-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.filters-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filters-content {
    padding: 25px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-label i {
    color: #6c757d;
}

.form-select, .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.quick-filters {
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.quick-filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.quick-filter-buttons .btn {
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 0.85rem;
}

/* Enhanced Charts Grid */
.charts-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.charts-row {
    display: grid;
    gap: 25px;
}

.primary-charts {
    grid-template-columns: 2fr 1fr;
}

.secondary-charts {
    grid-template-columns: 2fr 1fr;
}

.tertiary-charts {
    grid-template-columns: 1fr 1fr;
}

.quaternary-charts {
    grid-template-columns: 1fr;
}

.chart-container.large {
    grid-column: span 1;
}

.chart-container.medium {
    grid-column: span 1;
}

.chart-container.wide {
    grid-column: span 1;
}

.chart-container.small {
    grid-column: span 1;
}

.chart-container.full {
    grid-column: span 1;
}

.enhanced-chart-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.enhanced-chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.chart-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.chart-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.chart-title h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.chart-title p {
    margin: 0;
    color: #6c757d;
    font-size: 0.85rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-controls .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
    position: relative;
}

.status-indicator.active::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #28a745;
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.2); opacity: 0.1; }
    100% { transform: scale(1); opacity: 0.3; }
}

.chart-body {
    padding: 25px;
    position: relative;
    height: 350px;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    display: none;
}

.chart-insights {
    position: absolute;
    bottom: 15px;
    left: 25px;
    right: 25px;
    display: flex;
    justify-content: space-between;
    background: rgba(248, 249, 250, 0.95);
    padding: 10px 15px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.insight-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.insight-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
}

.insight-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.trend-up {
    color: #28a745 !important;
}

.trend-down {
    color: #dc3545 !important;
}

/* Enhanced Statistics */
.enhanced-statistics {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
    overflow: hidden;
}

.statistics-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.statistics-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.statistics-actions {
    display: flex;
    gap: 10px;
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    padding: 25px;
}

.enhanced-stat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid;
}

.enhanced-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-stat-card.primary {
    border-left-color: #007bff;
}

.enhanced-stat-card.success {
    border-left-color: #28a745;
}

.enhanced-stat-card.warning {
    border-left-color: #ffc107;
}

.enhanced-stat-card.info {
    border-left-color: #17a2b8;
}

.enhanced-stat-card.secondary {
    border-left-color: #6c757d;
}

.enhanced-stat-card.danger {
    border-left-color: #dc3545;
}

.stat-card-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(248, 249, 250, 0.5);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
}

.enhanced-stat-card.primary .stat-icon {
    background: #007bff;
}

.enhanced-stat-card.success .stat-icon {
    background: #28a745;
}

.enhanced-stat-card.warning .stat-icon {
    background: #ffc107;
}

.enhanced-stat-card.info .stat-icon {
    background: #17a2b8;
}

.enhanced-stat-card.secondary .stat-icon {
    background: #6c757d;
}

.enhanced-stat-card.danger .stat-icon {
    background: #dc3545;
}

.stat-trend {
    display: flex;
    align-items: center;
}

.trend-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.trend-indicator.up {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.trend-indicator.down {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-card-body {
    padding: 20px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 5px;
}

.stat-period {
    font-size: 0.8rem;
    color: #adb5bd;
}

.stat-card-footer {
    padding: 12px 20px;
    background: rgba(248, 249, 250, 0.7);
    border-top: 1px solid #f1f3f4;
}

.stat-comparison {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.comparison-label {
    color: #6c757d;
}

.comparison-value {
    font-weight: 600;
}

.comparison-value.positive {
    color: #28a745;
}

.comparison-value.negative {
    color: #dc3545;
}

/* Chart responsive adjustments */
@media (max-width: 768px) {
    .enhanced-header {
        padding: 20px;
    }

    .header-main {
        flex-direction: column;
        gap: 20px;
    }

    .header-title {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .title-content h1 {
        font-size: 1.8rem;
    }

    .metrics-bar {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .primary-charts {
        grid-template-columns: 1fr;
    }

    .chart-body {
        height: 250px;
        padding: 15px;
    }

    .chart-card-header {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .header-right {
        align-self: stretch;
        justify-content: space-between;
    }

    .filters-content {
        padding: 15px;
    }

    .quick-filter-buttons {
        justify-content: center;
    }

    .secondary-charts,
    .tertiary-charts,
    .quaternary-charts {
        grid-template-columns: 1fr;
    }

    .statistics-grid {
        grid-template-columns: 1fr;
        padding: 15px;
    }

    .statistics-header {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .statistics-actions {
        align-self: stretch;
        justify-content: center;
    }

    .enhanced-stat-card {
        margin-bottom: 15px;
    }

    .stat-value {
        font-size: 1.6rem;
    }

    .chart-insights {
        position: static;
        margin-top: 15px;
        flex-direction: column;
        gap: 10px;
    }

    .insight-item {
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .enhanced-header {
        padding: 15px;
    }

    .title-content h1 {
        font-size: 1.5rem;
    }

    .metrics-bar {
        padding: 15px;
    }

    .metric-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .action-buttons {
        flex-direction: column;
        width: 100%;
    }

    .action-buttons .btn {
        width: 100%;
    }

    .chart-body {
        height: 200px;
        padding: 10px;
    }

    .stat-value {
        font-size: 1.4rem;
    }
}

/* Chart loading state */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6c757d;
    font-size: 1.1rem;
}

.chart-loading i {
    margin-right: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart filters */
.filters-container .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filters-container .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button groups */
.btn-group .btn {
    border-radius: 6px;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-group .btn:hover:not(.active) {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Reports Page Styles */
.quick-reports-section {
    margin-bottom: 40px;
}

.quick-reports-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.report-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    height: 100%;
    border: 1px solid #e9ecef;
}

.report-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.report-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.report-content {
    flex: 1;
}

.report-content h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.report-content p {
    margin: 0 0 5px 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.report-content small {
    color: #6c757d;
    font-size: 0.8rem;
}

.report-action {
    color: #007bff;
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: transform 0.2s ease;
}

.report-card:hover .report-action {
    transform: translateX(3px);
}

.scheduled-reports-section,
.reports-history-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.section-actions .form-control {
    width: 250px;
}

.report-info strong {
    color: #2c3e50;
    font-size: 0.95rem;
}

.report-info small {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 2px;
}

/* Modal customizations for reports */
.modal-lg {
    max-width: 800px;
}

.form-check {
    margin-bottom: 8px;
}

.form-check-label {
    font-size: 0.9rem;
    color: #495057;
}

/* Responsive adjustments for reports */
@media (max-width: 768px) {
    .report-card {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .report-action {
        margin-top: 10px;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .section-actions .form-control {
        width: 100%;
    }

    .quick-reports-section .col-md-4 {
        margin-bottom: 15px;
    }
}

/* Loading states */
.report-generating {
    opacity: 0.6;
    pointer-events: none;
}

.report-generating::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Dashboard Content */
.dashboard-content {
    padding: 30px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-icon.bg-primary { background: linear-gradient(135deg, #3498db, #2980b9); }
.stat-icon.bg-success { background: linear-gradient(135deg, #27ae60, #229954); }
.stat-icon.bg-warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
.stat-icon.bg-danger { background: linear-gradient(135deg, #e74c3c, #c0392b); }

.stat-info h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-info p {
    margin: 5px 0 0 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Charts Row */
.charts-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ecf0f1;
}

.chart-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

/* Tables Row */
.tables-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.table-header {
    padding: 20px 25px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ecf0f1;
}

.table-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    margin: 0;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 15px 25px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* Alerts List */
.alerts-list {
    padding: 20px 25px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.alert-icon.warning { background: #f39c12; }
.alert-icon.danger { background: #e74c3c; }
.alert-icon.info { background: #3498db; }

.alert-content h5 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    color: #2c3e50;
}

.alert-content p {
    margin: 0;
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .topbar {
        padding: 15px 20px;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-row,
    .tables-row {
        grid-template-columns: 1fr;
    }
}

/* ===== ENHANCED DASHBOARD STYLES ===== */

/* Quick Actions Bar */
.quick-actions-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    overflow-x: auto;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    min-width: 120px;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    color: white;
}

.quick-action-btn i {
    font-size: 20px;
}

.quick-action-btn span {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

/* Enhanced Stats Cards */
.stat-card.enhanced {
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.stat-card.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card.enhanced:hover::before {
    opacity: 1;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
}

.stat-trend.critical {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.stat-trend i {
    font-size: 10px;
}

.stat-subtitle {
    color: #7f8c8d;
    font-size: 12px;
    font-weight: 400;
    margin-top: 5px;
}

.stat-progress {
    margin-top: 15px;
    height: 4px;
    background: #ecf0f1;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #3498db;
    border-radius: 2px;
    transition: width 0.8s ease;
}

.progress-bar.bg-success { background: #2ecc71; }
.progress-bar.bg-warning { background: #f39c12; }
.progress-bar.bg-danger { background: #e74c3c; }
.progress-bar.bg-info { background: #3498db; }
.progress-bar.bg-secondary { background: #95a5a6; }

/* Enhanced Charts Section */
.charts-section {
    margin-bottom: 30px;
}

.chart-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

/* View buttons (desagrupados) */
.view-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.view-buttons .btn {
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 600;
    padding: 12px 24px;
    font-size: 1.1rem;
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: 2px solid #dee2e6;
}

.view-buttons .btn i {
    font-size: 1.2rem;
}

.view-buttons .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
    transform: translateY(-2px);
}

.view-buttons .btn:hover:not(.active) {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.view-buttons .btn:active {
    transform: translateY(0);
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* Garantir que os gráficos sejam sempre visíveis quando a grid estiver ativa */
.charts-grid[style*="display: grid"] {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.charts-grid[style*="display: grid"] .chart-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.charts-grid[style*="display: grid"] canvas {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.chart-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
}

.chart-container.main-chart {
    grid-row: span 2;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.chart-title h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.chart-title p {
    margin: 5px 0 0 0;
    color: #7f8c8d;
    font-size: 14px;
}

.chart-actions {
    display: flex;
    gap: 5px;
}

.chart-body {
    padding: 20px;
    height: 300px;
    position: relative;
}

.main-chart .chart-body {
    height: 400px;
}

.chart-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.chart-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    font-size: 14px;
    color: #6c757d;
}

.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.chart-legend {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6c757d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* Live Status Panel */
.status-panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.panel-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    font-size: 12px;
    font-weight: 600;
}

.live-dot {
    width: 8px;
    height: 8px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.status-item:hover {
    background: #e9ecef;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.status-info h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
}

.status-info p {
    margin: 2px 0 0 0;
    font-size: 12px;
    color: #6c757d;
}

/* Enhanced Tables */
.table-container.enhanced {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.table-title h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.table-count {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 10px;
}

/* Notifications Center */
.notifications-center {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.notifications-header {
    padding: 20px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notifications-title h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.notifications-count {
    background: #fff3cd;
    color: #856404;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.notifications-actions {
    display: flex;
    gap: 10px;
}

.notification-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 12px 15px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    color: #495057;
    background: white;
    border-bottom-color: #007bff;
}

.tab-btn:hover {
    background: #e9ecef;
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

/* Metrics Cards */
.metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.metric-header h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.metric-body {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.metric-value {
    display: flex;
    align-items: center;
    gap: 15px;
}

.metric-value .value {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.progress-ring {
    position: relative;
    width: 60px;
    height: 60px;
}

.progress-ring-svg {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: #e9ecef;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 157;
    stroke-dashoffset: 0;
    transition: stroke-dashoffset 0.8s ease;
}

.financial-summary {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.financial-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.financial-item:last-child {
    border-bottom: none;
}

.financial-item .label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.financial-item .value {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.financial-item .trend {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.financial-item .trend.up {
    background: #d4edda;
    color: #155724;
}

.financial-item .trend.down {
    background: #f8d7da;
    color: #721c24;
}

/* Notification Styles */
.notification-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item.unread {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.notification-icon.critical {
    background: #dc3545;
}

.notification-icon.warning {
    background: #ffc107;
}

.notification-icon.info {
    background: #17a2b8;
}

.notification-icon.success {
    background: #28a745;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.notification-time {
    font-size: 12px;
    color: #6c757d;
}

.notification-content p {
    margin: 0 0 10px 0;
    font-size: 13px;
    color: #495057;
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    gap: 8px;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast-notification {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border-left: 4px solid #007bff;
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.toast-notification.success {
    border-left-color: #28a745;
}

.toast-notification.warning {
    border-left-color: #ffc107;
}

.toast-notification.error {
    border-left-color: #dc3545;
}

.toast-notification.info {
    border-left-color: #17a2b8;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.toast-content i {
    font-size: 16px;
    color: #007bff;
}

.toast-notification.success .toast-content i {
    color: #28a745;
}

.toast-notification.warning .toast-content i {
    color: #ffc107;
}

.toast-notification.error .toast-content i {
    color: #dc3545;
}

.toast-notification.info .toast-content i {
    color: #17a2b8;
}

.toast-content span {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: #495057;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .quick-actions-bar {
        padding: 15px;
        gap: 10px;
    }

    .quick-action-btn {
        min-width: 100px;
        padding: 12px 15px;
    }

    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .metrics-row {
        grid-template-columns: 1fr;
    }

    .notification-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        font-size: 11px;
        padding: 10px 12px;
    }

    .toast-container {
        left: 10px;
        right: 10px;
        top: 10px;
    }

    .toast-notification {
        min-width: auto;
    }
}

/* Visualizações alternativas (Tabela e Grade) */
.table-view {
    display: none;
    margin-top: 20px;
}

.table-view .table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.table-view .table-header {
    padding: 20px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-view .table-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.table-view .table-header i {
    margin-right: 8px;
}

.table-view .table-responsive {
    padding: 0;
}

.table-view .table {
    margin: 0;
    font-size: 0.9rem;
}

.table-view .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #2c3e50;
    padding: 15px 12px;
}

.table-view .table td {
    padding: 12px;
    vertical-align: middle;
}

.table-view .table tbody tr:hover {
    background-color: #f8f9fa;
}

.table-view .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Visualização em Grade */
.grid-view {
    display: none;
    margin-top: 20px;
}

.grid-view .metric-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.grid-view .metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.grid-view .metric-header {
    padding: 20px;
    text-align: center;
    color: white;
    font-size: 2rem;
}

.grid-view .metric-body {
    padding: 20px;
    text-align: center;
}

.grid-view .metric-body h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.grid-view .metric-body p {
    margin: 0;
    color: #7f8c8d;
    font-weight: 500;
}

/* Animações para transições de visualização */
.charts-grid,
.table-view,
.grid-view {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.charts-grid.fade-out,
.table-view.fade-out,
.grid-view.fade-out {
    opacity: 0;
    transform: translateY(10px);
}

/* Responsividade para visualizações alternativas */
@media (max-width: 768px) {
    .table-view .table-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .table-view .table-responsive {
        font-size: 0.8rem;
    }

    .grid-view {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    }

    .grid-view .metric-header {
        padding: 15px;
        font-size: 1.5rem;
    }

    .grid-view .metric-body {
        padding: 15px;
    }

    .grid-view .metric-body h3 {
        font-size: 1.4rem;
    }
}

/* Notificações Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.toast-notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideInRight 0.3s ease;
    border-left: 4px solid #007bff;
}

.toast-notification.toast-success {
    border-left-color: #28a745;
}

.toast-notification.toast-error {
    border-left-color: #dc3545;
}

.toast-notification.toast-warning {
    border-left-color: #ffc107;
}

.toast-notification.toast-info {
    border-left-color: #17a2b8;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.toast-content i {
    font-size: 1.1rem;
}

.toast-notification.toast-success .toast-content i {
    color: #28a745;
}

.toast-notification.toast-error .toast-content i {
    color: #dc3545;
}

.toast-notification.toast-warning .toast-content i {
    color: #ffc107;
}

.toast-notification.toast-info .toast-content i {
    color: #17a2b8;
}

.toast-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.toast-close:hover {
    background-color: #f8f9fa;
    color: #495057;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
