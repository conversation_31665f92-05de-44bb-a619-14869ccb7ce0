// Sistema de Gerenciamento de Fornecedores
class SupplierManager {
    constructor() {
        this.suppliers = this.loadSuppliers();
        this.currentSupplier = null;
        this.init();
    }

    init() {
        this.loadSuppliersTable();
        this.setupEventListeners();
        this.setupFilters();
        this.updateStatistics();
    }

    // Carregar fornecedores do localStorage ou dados padrão
    loadSuppliers() {
        const savedSuppliers = localStorage.getItem('frotas_suppliers');
        if (savedSuppliers) {
            return JSON.parse(savedSuppliers);
        }

        // Fornecedores padrão do sistema
        return [
            {
                id: 1,
                name: 'Posto Ipiranga Centro',
                cnpj: '12.345.678/0001-90',
                category: 'combustivel',
                phone: '(11) 3456-7890',
                email: '<EMAIL>',
                contact: '<PERSON>',
                contactPhone: '(11) 99999-1234',
                address: 'Rua Principal, 123 - Centro - São Paulo/SP',
                status: 'ativo',
                notes: 'Posto com desconto para frota',
                createdAt: '2024-01-01 00:00:00'
            },
            {
                id: 2,
                name: 'Oficina Mecânica Central',
                cnpj: '23.456.789/0001-01',
                category: 'manutencao',
                phone: '(11) 3567-8901',
                email: '<EMAIL>',
                contact: 'Maria Santos',
                contactPhone: '(11) 99999-2345',
                address: 'Av. Industrial, 456 - Vila Industrial - São Paulo/SP',
                status: 'ativo',
                notes: 'Especializada em veículos pesados',
                createdAt: '2024-01-02 00:00:00'
            },
            {
                id: 3,
                name: 'Auto Peças Rápidas',
                cnpj: '34.567.890/0001-12',
                category: 'pecas',
                phone: '(11) 3678-9012',
                email: '<EMAIL>',
                contact: 'Carlos Oliveira',
                contactPhone: '(11) 99999-3456',
                address: 'Rua das Peças, 789 - Bom Retiro - São Paulo/SP',
                status: 'ativo',
                notes: 'Entrega rápida, bom preço',
                createdAt: '2024-01-03 00:00:00'
            },
            {
                id: 4,
                name: 'Lava Car Premium',
                cnpj: '45.678.901/0001-23',
                category: 'lavagem',
                phone: '(11) 3789-0123',
                email: '<EMAIL>',
                contact: 'Ana Costa',
                contactPhone: '(11) 99999-4567',
                address: 'Av. Lavagem, 321 - Jardins - São Paulo/SP',
                status: 'ativo',
                notes: 'Serviço completo de lavagem',
                createdAt: '2024-01-04 00:00:00'
            },
            {
                id: 5,
                name: 'Shell Select',
                cnpj: '56.789.012/0001-34',
                category: 'combustivel',
                phone: '(11) 3890-1234',
                email: '<EMAIL>',
                contact: 'Roberto Lima',
                contactPhone: '(11) 99999-5678',
                address: 'Rodovia Anhanguera, Km 15 - São Paulo/SP',
                status: 'ativo',
                notes: 'Combustível de qualidade premium',
                createdAt: '2024-01-05 00:00:00'
            },
            {
                id: 6,
                name: 'Mecânica do Zé',
                cnpj: '67.890.123/0001-45',
                category: 'manutencao',
                phone: '(11) 3901-2345',
                email: '<EMAIL>',
                contact: 'José Ferreira',
                contactPhone: '(11) 99999-6789',
                address: 'Rua da Oficina, 654 - Vila Madalena - São Paulo/SP',
                status: 'inativo',
                notes: 'Temporariamente fechado',
                createdAt: '2024-01-06 00:00:00'
            }
        ];
    }

    // Salvar fornecedores no localStorage
    saveSuppliers() {
        localStorage.setItem('frotas_suppliers', JSON.stringify(this.suppliers));
    }

    // Configurar event listeners
    setupEventListeners() {
        // Busca em tempo real
        document.getElementById('searchSupplier').addEventListener('input', () => {
            this.filterSuppliers();
        });

        // Filtros
        document.getElementById('filterCategory').addEventListener('change', () => {
            this.filterSuppliers();
        });

        document.getElementById('filterStatus').addEventListener('change', () => {
            this.filterSuppliers();
        });

        // Formatação de CNPJ e telefones
        document.getElementById('supplierCnpj').addEventListener('input', (e) => {
            this.formatCNPJ(e.target);
        });

        document.getElementById('supplierPhone').addEventListener('input', (e) => {
            this.formatPhone(e.target);
        });

        document.getElementById('supplierContactPhone').addEventListener('input', (e) => {
            this.formatPhone(e.target);
        });
    }

    // Configurar filtros
    setupFilters() {
        this.filterSuppliers();
    }

    // Atualizar estatísticas
    updateStatistics() {
        const total = this.suppliers.length;
        const fuel = this.suppliers.filter(s => s.category === 'combustivel').length;
        const service = this.suppliers.filter(s => s.category === 'manutencao').length;
        const parts = this.suppliers.filter(s => s.category === 'pecas').length;

        document.getElementById('totalSuppliers').textContent = total;
        document.getElementById('fuelSuppliers').textContent = fuel;
        document.getElementById('serviceSuppliers').textContent = service;
        document.getElementById('partsSuppliers').textContent = parts;
    }

    // Carregar tabela de fornecedores
    loadSuppliersTable(suppliersToShow = null) {
        const tbody = document.getElementById('suppliersTableBody');
        const suppliers = suppliersToShow || this.suppliers;

        tbody.innerHTML = '';

        suppliers.forEach(supplier => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="supplier-checkbox" value="${supplier.id}">
                </td>
                <td>
                    <div class="supplier-info">
                        <strong>${supplier.name}</strong>
                        <small class="text-muted d-block">${supplier.contact || 'Sem contato'}</small>
                    </div>
                </td>
                <td>${supplier.cnpj}</td>
                <td>
                    <span class="badge badge-${this.getCategoryBadgeClass(supplier.category)}">
                        ${this.getCategoryLabel(supplier.category)}
                    </span>
                </td>
                <td>${supplier.phone || '-'}</td>
                <td>
                    <span class="badge badge-${supplier.status === 'ativo' ? 'success' : 'secondary'}">
                        ${supplier.status === 'ativo' ? 'Ativo' : 'Inativo'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="supplierManager.editSupplier(${supplier.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="supplierManager.deleteSupplier(${supplier.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="supplierManager.viewDetails(${supplier.id})" title="Detalhes">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Filtrar fornecedores
    filterSuppliers() {
        const search = document.getElementById('searchSupplier').value.toLowerCase();
        const categoryFilter = document.getElementById('filterCategory').value;
        const statusFilter = document.getElementById('filterStatus').value;

        const filteredSuppliers = this.suppliers.filter(supplier => {
            const matchesSearch = supplier.name.toLowerCase().includes(search) || 
                                supplier.cnpj.includes(search) ||
                                (supplier.contact && supplier.contact.toLowerCase().includes(search));
            const matchesCategory = !categoryFilter || supplier.category === categoryFilter;
            const matchesStatus = !statusFilter || supplier.status === statusFilter;

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.loadSuppliersTable(filteredSuppliers);
    }

    // Limpar filtros
    clearFilters() {
        document.getElementById('searchSupplier').value = '';
        document.getElementById('filterCategory').value = '';
        document.getElementById('filterStatus').value = '';
        this.loadSuppliersTable();
    }

    // Mostrar modal de adicionar fornecedor
    showAddSupplierModal() {
        this.currentSupplier = null;
        document.getElementById('supplierModalTitle').textContent = 'Novo Fornecedor';
        document.getElementById('supplierForm').reset();
        document.getElementById('supplierId').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
        modal.show();
    }

    // Editar fornecedor
    editSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        this.currentSupplier = supplier;
        document.getElementById('supplierModalTitle').textContent = 'Editar Fornecedor';
        document.getElementById('supplierId').value = supplier.id;
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('supplierCnpj').value = supplier.cnpj;
        document.getElementById('supplierCategory').value = supplier.category;
        document.getElementById('supplierStatus').value = supplier.status;
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('supplierEmail').value = supplier.email || '';
        document.getElementById('supplierContact').value = supplier.contact || '';
        document.getElementById('supplierContactPhone').value = supplier.contactPhone || '';
        document.getElementById('supplierAddress').value = supplier.address || '';
        document.getElementById('supplierNotes').value = supplier.notes || '';
        
        const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
        modal.show();
    }

    // Salvar fornecedor
    saveSupplier() {
        const form = document.getElementById('supplierForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const supplierId = document.getElementById('supplierId').value;
        const name = document.getElementById('supplierName').value;
        const cnpj = document.getElementById('supplierCnpj').value;
        const category = document.getElementById('supplierCategory').value;
        const status = document.getElementById('supplierStatus').value;
        const phone = document.getElementById('supplierPhone').value;
        const email = document.getElementById('supplierEmail').value;
        const contact = document.getElementById('supplierContact').value;
        const contactPhone = document.getElementById('supplierContactPhone').value;
        const address = document.getElementById('supplierAddress').value;
        const notes = document.getElementById('supplierNotes').value;

        // Verificar CNPJ duplicado
        const existingSupplier = this.suppliers.find(s => s.cnpj === cnpj && s.id != supplierId);
        if (existingSupplier) {
            alert('Este CNPJ já está sendo usado por outro fornecedor!');
            return;
        }

        if (supplierId) {
            // Editar fornecedor existente
            const supplierIndex = this.suppliers.findIndex(s => s.id == supplierId);
            if (supplierIndex !== -1) {
                this.suppliers[supplierIndex] = {
                    ...this.suppliers[supplierIndex],
                    name, cnpj, category, status, phone, email, contact, contactPhone, address, notes,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // Adicionar novo fornecedor
            const newSupplier = {
                id: Math.max(...this.suppliers.map(s => s.id)) + 1,
                name, cnpj, category, status, phone, email, contact, contactPhone, address, notes,
                createdAt: new Date().toISOString()
            };
            this.suppliers.push(newSupplier);
        }

        this.saveSuppliers();
        this.loadSuppliersTable();
        this.updateStatistics();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('supplierModal'));
        modal.hide();

        // Mostrar notificação
        const message = supplierId ? 'Fornecedor atualizado com sucesso!' : 'Fornecedor criado com sucesso!';
        console.log('✅', message);
        alert(message);
    }

    // Excluir fornecedor
    deleteSupplier(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        if (confirm(`Deseja realmente excluir o fornecedor "${supplier.name}"?`)) {
            this.suppliers = this.suppliers.filter(s => s.id !== supplierId);
            this.saveSuppliers();
            this.loadSuppliersTable();
            this.updateStatistics();

            console.log('✅ Fornecedor excluído com sucesso!');
            alert('Fornecedor excluído com sucesso!');
        }
    }

    // Ver detalhes do fornecedor
    viewDetails(supplierId) {
        const supplier = this.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const details = `
Fornecedor: ${supplier.name}
CNPJ: ${supplier.cnpj}
Categoria: ${this.getCategoryLabel(supplier.category)}
Status: ${supplier.status === 'ativo' ? 'Ativo' : 'Inativo'}

Contato: ${supplier.contact || 'Não informado'}
Telefone: ${supplier.phone || 'Não informado'}
Email: ${supplier.email || 'Não informado'}

Endereço: ${supplier.address || 'Não informado'}

Observações: ${supplier.notes || 'Nenhuma observação'}
        `;

        alert(details);
    }

    // Exportar fornecedores
    exportSuppliers() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `fornecedores_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Gerar CSV
    generateCSV() {
        const headers = ['ID', 'Nome', 'CNPJ', 'Categoria', 'Telefone', 'Email', 'Contato', 'Status'];
        const rows = this.suppliers.map(supplier => [
            supplier.id,
            supplier.name,
            supplier.cnpj,
            this.getCategoryLabel(supplier.category),
            supplier.phone || '',
            supplier.email || '',
            supplier.contact || '',
            supplier.status === 'ativo' ? 'Ativo' : 'Inativo'
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    // Formatar CNPJ
    formatCNPJ(input) {
        let value = input.value.replace(/\D/g, '');
        
        if (value.length <= 14) {
            value = value.replace(/^(\d{2})(\d)/, '$1.$2');
            value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
            value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
            value = value.replace(/(\d{4})(\d)/, '$1-$2');
        }
        
        input.value = value;
    }

    // Formatar telefone
    formatPhone(input) {
        let value = input.value.replace(/\D/g, '');
        
        if (value.length <= 11) {
            if (value.length <= 10) {
                value = value.replace(/^(\d{2})(\d)/, '($1) $2');
                value = value.replace(/(\d{4})(\d)/, '$1-$2');
            } else {
                value = value.replace(/^(\d{2})(\d)/, '($1) $2');
                value = value.replace(/(\d{5})(\d)/, '$1-$2');
            }
        }
        
        input.value = value;
    }

    // Utilitários
    getCategoryBadgeClass(category) {
        switch (category) {
            case 'combustivel': return 'primary';
            case 'manutencao': return 'warning';
            case 'pecas': return 'info';
            case 'lavagem': return 'success';
            case 'outros': return 'secondary';
            default: return 'secondary';
        }
    }

    getCategoryLabel(category) {
        switch (category) {
            case 'combustivel': return 'Combustível';
            case 'manutencao': return 'Manutenção';
            case 'pecas': return 'Peças';
            case 'lavagem': return 'Lavagem';
            case 'outros': return 'Outros';
            default: return 'Desconhecido';
        }
    }
}

// Funções globais
function showAddSupplierModal() {
    supplierManager.showAddSupplierModal();
}

function saveSupplier() {
    supplierManager.saveSupplier();
}

function clearFilters() {
    supplierManager.clearFilters();
}

function exportSuppliers() {
    supplierManager.exportSuppliers();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.supplier-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.supplierManager = new SupplierManager();
});
