{"name": "fleet-management-v2", "version": "1.0.0", "description": "Sistema de Gestão de Frotas com Supabase, Node.js e React", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "setup": "npm run install:all && npm run db:setup", "db:setup": "cd server && npm run db:setup", "test": "cd server && npm test && cd ../client && npm test", "lint": "cd server && npm run lint && cd ../client && npm run lint"}, "keywords": ["fleet-management", "supabase", "nodejs", "react", "postgresql"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}