# 🚗 Sistema de Gestão de Frotas v2.0

Sistema completo para gestão de frotas de veículos com **Supabase PostgreSQL**, **Node.js** e **React**.

## 🌟 Características

- ✅ **Backend Node.js** com Express e API REST
- ✅ **Frontend React** moderno com hooks e context
- ✅ **Banco PostgreSQL** via Supabase
- ✅ **Autenticação segura** com Supabase Auth
- ✅ **Interface responsiva** e moderna
- ✅ **Controle de acesso** por roles (admin/user)
- ✅ **Validação de dados** completa
- ✅ **Tratamento de erros** robusto
- ✅ **Testes automatizados**

## 📋 Funcionalidades

### 🚙 Gestão de Veículos
- Cadastro completo de veículos
- Controle de status (ativo, inativo, manutenção)
- Histórico de quilometragem

### ⛽ Controle de Abastecimento
- Registro de abastecimentos
- Cálculo automático de consumo
- Controle de cupons fiscais e notas

### 🔧 Manutenção
- Agendamento de manutenções
- Histórico completo de serviços
- Controle de fornecedores

### 🧽 Lavagem
- Registro de lavagens internas/externas
- Controle de custos
- Histórico por veículo

### 📋 Revisões
- Controle de revisões periódicas
- Calendário de revisões
- Itens verificados

### 💰 Controle Financeiro
- Fluxo de caixa completo
- Relatórios mensais
- Categorização de gastos

### 📊 Relatórios
- Relatórios detalhados
- Exportação em PDF
- Gráficos e estatísticas

## 🚀 Instalação Rápida

### 1. Pré-requisitos
```bash
# Node.js 18+ e npm
node --version
npm --version

# Git
git --version
```

### 2. Clonar e Instalar
```bash
# Clonar repositório
git clone <repository-url>
cd fleet-management-v2

# Instalar todas as dependências
npm run install:all
```

### 3. Configurar Supabase

#### 3.1 Criar Projeto no Supabase
1. Acesse [supabase.com](https://supabase.com)
2. Faça login com: **<EMAIL>**
3. Crie novo projeto: **gestao-frotas**
4. Região: **South America (São Paulo)**
5. Senha: **Ra5izen2kim#**

#### 3.2 Executar Script SQL
1. Vá para **SQL Editor** no Supabase
2. Execute o script em `SUPABASE-SETUP-GUIDE.md`

#### 3.3 Configurar Variáveis de Ambiente

**Backend (.env):**
```bash
cd server
cp .env.example .env
# Edite o arquivo .env com suas credenciais do Supabase
```

**Frontend (.env):**
```bash
cd client
cp .env.example .env
# Edite o arquivo .env com suas credenciais do Supabase
```

### 4. Executar Aplicação
```bash
# Desenvolvimento (backend + frontend)
npm run dev

# Ou executar separadamente:
npm run server:dev  # Backend na porta 5000
npm run client:dev  # Frontend na porta 3000
```

### 5. Acessar Sistema
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000
- **Health Check:** http://localhost:5000/health

## 📁 Estrutura do Projeto

```
fleet-management-v2/
├── server/                 # Backend Node.js
│   ├── config/            # Configurações
│   ├── middleware/        # Middlewares
│   ├── routes/           # Rotas da API
│   ├── services/         # Serviços
│   ├── utils/            # Utilitários
│   └── index.js          # Entrada do servidor
├── client/               # Frontend React
│   ├── public/          # Arquivos públicos
│   ├── src/
│   │   ├── components/  # Componentes React
│   │   ├── contexts/    # Contexts (Auth, Theme)
│   │   ├── hooks/       # Custom hooks
│   │   ├── pages/       # Páginas
│   │   ├── services/    # Serviços API
│   │   ├── styles/      # Estilos
│   │   └── utils/       # Utilitários
│   └── package.json
├── docs/                # Documentação
├── package.json         # Scripts principais
└── README.md
```

## 🔧 Scripts Disponíveis

```bash
# Desenvolvimento
npm run dev              # Backend + Frontend
npm run server:dev       # Apenas backend
npm run client:dev       # Apenas frontend

# Produção
npm run build           # Build do frontend
npm start              # Executar em produção

# Instalação
npm run install:all     # Instalar todas as dependências
npm run setup          # Setup completo

# Testes
npm test               # Executar todos os testes
npm run test:server    # Testes do backend
npm run test:client    # Testes do frontend

# Linting
npm run lint           # Lint em todo o projeto
npm run lint:fix       # Corrigir problemas de lint
```

## 🔐 Autenticação

### Primeiro Acesso
1. Acesse `/register`
2. Crie a conta de administrador
3. Após o primeiro usuário, novos registros serão usuários comuns

### Roles
- **Admin:** Acesso completo ao sistema
- **User:** Acesso limitado aos próprios dados

## 🌐 API Endpoints

### Autenticação
- `POST /api/auth/register` - Registrar usuário
- `POST /api/auth/login` - Login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/profile` - Perfil do usuário

### Veículos
- `GET /api/vehicles` - Listar veículos
- `POST /api/vehicles` - Criar veículo
- `PUT /api/vehicles/:id` - Atualizar veículo
- `DELETE /api/vehicles/:id` - Deletar veículo

### Abastecimento
- `GET /api/fuel` - Listar abastecimentos
- `POST /api/fuel` - Registrar abastecimento
- `PUT /api/fuel/:id` - Atualizar abastecimento

*[Documentação completa da API em desenvolvimento]*

## 🧪 Testes

```bash
# Executar todos os testes
npm test

# Testes com coverage
npm run test:coverage

# Testes em modo watch
npm run test:watch
```

## 📦 Deploy

### Supabase + Vercel (Recomendado)
1. **Backend:** Deploy no Vercel/Railway/Heroku
2. **Frontend:** Deploy no Vercel/Netlify
3. **Banco:** Já está no Supabase

### Variáveis de Ambiente (Produção)
```bash
# Backend
SUPABASE_URL=sua_url_supabase
SUPABASE_SERVICE_KEY=sua_service_key
NODE_ENV=production

# Frontend
REACT_APP_SUPABASE_URL=sua_url_supabase
REACT_APP_SUPABASE_ANON_KEY=sua_anon_key
REACT_APP_API_URL=sua_url_backend
```

## 🐛 Solução de Problemas

### Erro de Conexão com Supabase
1. Verifique as variáveis de ambiente
2. Confirme se o projeto está ativo no Supabase
3. Verifique as políticas RLS

### Erro de CORS
1. Configure `CORS_ORIGIN` no backend
2. Verifique se as URLs estão corretas

### Erro de Autenticação
1. Verifique se as tabelas foram criadas
2. Confirme as políticas RLS
3. Verifique os tokens JWT

## 📞 Suporte

- **Email:** <EMAIL>
- **Documentação:** [Em desenvolvimento]
- **Issues:** [GitHub Issues]

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.

---

**Desenvolvido com ❤️ para gestão eficiente de frotas**
