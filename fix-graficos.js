// Fix para Menu Gráficos - Sistema de Gestão de Frotas - VERSÃO FINAL
console.log('🔧 Carregando correções para menu gráficos - VERSÃO FINAL...');

// Verificar se Chart.js está disponível
function ensureChartJs() {
    if (typeof Chart === 'undefined') {
        console.error('❌ Chart.js não está carregado');
        return false;
    }
    console.log('✅ Chart.js disponível, versão:', Chart.version);
    return true;
}

// Verificar se ChartsManager está disponível
function ensureChartsManager() {
    if (typeof ChartsManager === 'undefined') {
        console.error('❌ ChartsManager não está disponível');
        return false;
    }
    console.log('✅ ChartsManager disponível');
    return true;
}

// Função principal de inicialização
function initializeGraphics() {
    console.log('🎯 Iniciando sistema de gráficos...');

    // Verificar dependências
    if (!ensureChartJs()) {
        console.log('⏳ Chart.js não disponível');
        setTimeout(initializeGraphics, 1000); // Tentar novamente em 1 segundo
        return;
    }

    // Tentar usar ChartsManager principal
    if (ensureChartsManager()) {
        console.log('📊 Usando ChartsManager principal...');
        try {
            if (!window.chartsManager) {
                window.chartsManager = new ChartsManager();
                console.log('✅ ChartsManager principal inicializado');
                showSuccessNotification('Sistema de gráficos carregado com sucesso!');
                return;
            } else {
                console.log('ℹ️ ChartsManager já existe');
                return;
            }
        } catch (error) {
            console.error('❌ Erro no ChartsManager principal:', error);
            showErrorNotification('Erro no sistema principal. Usando fallback...');
        }
    }

    // Fallback para sistema simplificado
    console.log('🔄 Usando sistema de gráficos simplificado...');
    initializeSimpleCharts();
}

// Sistema de gráficos simplificado
function initializeSimpleCharts() {
    console.log('🚀 Iniciando sistema simplificado...');

    const chartElements = [
        { id: 'fuelConsumptionChart', title: 'Consumo de Combustível', type: 'line' },
        { id: 'costsByCategoryChart', title: 'Custos por Categoria', type: 'doughnut' },
        { id: 'costsEvolutionChart', title: 'Evolução de Custos', type: 'line' },
        { id: 'fleetStatusChart', title: 'Status da Frota', type: 'pie' },
        { id: 'maintenanceChart', title: 'Manutenções', type: 'bar' },
        { id: 'mileageChart', title: 'Quilometragem', type: 'bar' },
        { id: 'comparativeChart', title: 'Comparativo', type: 'line' }
    ];

    let chartsCreated = 0;

    chartElements.forEach(element => {
        const canvas = document.getElementById(element.id);
        if (canvas) {
            try {
                createChart(canvas, element);
                chartsCreated++;
                console.log(`✅ ${element.title} criado`);
            } catch (error) {
                console.error(`❌ Erro ao criar ${element.title}:`, error);
            }
        } else {
            console.warn(`⚠️ Canvas ${element.id} não encontrado`);
        }
    });

    if (chartsCreated > 0) {
        console.log(`📊 ${chartsCreated} gráficos criados com sucesso`);
        showSuccessNotification(`${chartsCreated} gráficos carregados com sucesso!`);
    } else {
        console.error('❌ Nenhum gráfico foi criado');
        showErrorNotification('Erro: Nenhum gráfico pôde ser carregado');
    }
}
// Função para criar gráficos individuais
function createChart(canvas, config) {
    const ctx = canvas.getContext('2d');

    let chartConfig;

    switch (config.type) {
        case 'line':
            chartConfig = createLineChartConfig(config.title);
            break;
        case 'doughnut':
            chartConfig = createDoughnutChartConfig();
            break;
        case 'pie':
            chartConfig = createPieChartConfig();
            break;
        case 'bar':
            chartConfig = createBarChartConfig(config.title);
            break;
        default:
            console.warn(`Tipo de gráfico não suportado: ${config.type}`);
            return;
    }

    new Chart(ctx, chartConfig);
}

// Configurações dos gráficos
function createLineChartConfig(title) {
    const data = generateRandomData(6, 20, 100);
    return {
        type: 'line',
        data: {
            labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
            datasets: [{
                label: title,
                data: data,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: { color: 'rgba(0, 0, 0, 0.1)' }
                },
                x: {
                    grid: { color: 'rgba(0, 0, 0, 0.1)' }
                }
            }
        }
    };
}
function createBarChartConfig(title) {
    const data = generateRandomData(6, 5, 25);
    return {
        type: 'bar',
        data: {
            labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
            datasets: [{
                label: title,
                data: data,
                backgroundColor: '#ffc107',
                borderColor: '#e0a800',
                borderWidth: 1,
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 },
                    grid: { color: 'rgba(0, 0, 0, 0.1)' }
                },
                x: {
                    grid: { color: 'rgba(0, 0, 0, 0.1)' }
                }
            }
        }
    };
}
// Funções auxiliares
function generateRandomData(count, min, max) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
}

// Funções de notificação
function showSuccessNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    notification.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'alert alert-danger alert-dismissible fade show';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 8000);
}

// Função global para reinicializar gráficos
window.reinitializeCharts = function() {
    console.log('🔄 Reinicializando gráficos...');

    // Destruir gráficos existentes
    if (window.chartsManager && window.chartsManager.charts) {
        Object.values(window.chartsManager.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
    }

    // Reinicializar
    window.chartsManager = null;
    initializeGraphics();
};

// Funções globais para compatibilidade
window.refreshCharts = function() {
    console.log('🔄 Função refreshCharts chamada');
    if (window.chartsManager && typeof window.chartsManager.updateCharts === 'function') {
        window.chartsManager.updateCharts();
    } else {
        console.log('⚠️ ChartsManager não disponível, tentando recriar...');
        initializeGraphics();
    }
};

window.exportCharts = function() {
    console.log('📥 Função exportCharts chamada');
    if (window.chartsManager && typeof window.chartsManager.exportCharts === 'function') {
        window.chartsManager.exportCharts();
    } else {
        console.log('⚠️ ChartsManager não disponível para exportar');
        showErrorNotification('Sistema de gráficos não está disponível para exportar');
    }
};
// Função para verificar se estamos na página de gráficos
function isGraphicsPage() {
    const chartElements = [
        'fuelConsumptionChart', 'costsByCategoryChart', 'maintenanceChart',
        'fleetStatusChart', 'mileageChart', 'comparativeChart', 'costsEvolutionChart'
    ];

    return chartElements.some(id => document.getElementById(id) !== null);
}

// Inicialização automática mais robusta
function autoInitialize() {
    console.log('🔄 Verificando se deve inicializar gráficos...');

    if (!isGraphicsPage()) {
        console.log('ℹ️ Não é uma página de gráficos, ignorando inicialização');
        return;
    }

    console.log('📊 Página de gráficos detectada, inicializando...');
    initializeGraphics();
}

// Múltiplas tentativas de inicialização
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(autoInitialize, 500);
    });
} else {
    setTimeout(autoInitialize, 100);
}

// Verificação adicional após 2 segundos
setTimeout(() => {
    if (isGraphicsPage() && !window.chartsManager) {
        console.log('🔄 Tentativa adicional de inicialização...');
        autoInitialize();
    }
}, 2000);

console.log('✅ Fix para gráficos carregado - Sistema corrigido e otimizado');
