<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Siste<PERSON> de <PERSON>vagem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-section {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-spray-can text-primary"></i>
                    Debug do Sistema de Lavagem
                </h1>
                
                <div class="debug-section">
                    <h5><i class="fas fa-info-circle"></i> Status do Sistema</h5>
                    <div id="systemStatus"></div>
                </div>

                <!-- Botões de Teste -->
                <div class="mb-4">
                    <button class="btn btn-primary me-2" onclick="testVehicleData()">
                        <i class="fas fa-car"></i> Testar Dados de Veículos
                    </button>
                    <button class="btn btn-success me-2" onclick="testLavagemSystem()">
                        <i class="fas fa-cogs"></i> Testar Sistema de Lavagem
                    </button>
                    <button class="btn btn-warning me-2" onclick="createTestVehicles()">
                        <i class="fas fa-plus"></i> Criar Veículos de Teste
                    </button>
                    <button class="btn btn-info me-2" onclick="testFormSubmission()">
                        <i class="fas fa-paper-plane"></i> Testar Envio de Formulário
                    </button>
                    <button class="btn btn-danger me-2" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> Limpar Dados
                    </button>
                </div>

                <!-- Área de Status -->
                <div id="statusArea"></div>

                <!-- Formulário de Teste -->
                <div class="debug-section">
                    <h5>Formulário de Teste</h5>
                    <form id="testForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="testVeiculo">Veículo</label>
                                    <select id="testVeiculo" name="veiculo" class="form-select">
                                        <option value="">Selecione o veículo</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="testData">Data/Hora</label>
                                    <input type="datetime-local" id="testData" name="dataLavagem" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="testTipo">Tipo</label>
                                    <select id="testTipo" name="tipoLavagem" class="form-select">
                                        <option value="">Selecione</option>
                                        <option value="simples">Simples</option>
                                        <option value="completa">Completa</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="testLocal">Local</label>
                                    <select id="testLocal" name="localLavagem" class="form-select">
                                        <option value="">Selecione</option>
                                        <option value="interno">Interno</option>
                                        <option value="externo">Externo</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="testValor">Valor</label>
                                    <input type="number" id="testValor" name="valor" class="form-control" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="testResponsavel">Responsável</label>
                            <input type="text" id="testResponsavel" name="responsavel" class="form-control">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="testFormData()">
                            <i class="fas fa-test-tube"></i> Testar Dados do Formulário
                        </button>
                    </form>
                </div>

                <!-- Dados Atuais -->
                <div class="debug-section">
                    <h5>Dados Atuais</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Veículos</h6>
                            <div id="vehiclesList"></div>
                        </div>
                        <div class="col-md-6">
                            <h6>Lavagens</h6>
                            <div id="lavagensList"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/lavagem.js"></script>
    
    <script>
        function addStatus(message, type = 'info') {
            const statusArea = document.getElementById('statusArea');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i> ${message}`;
            statusArea.appendChild(statusDiv);
            
            setTimeout(() => {
                if (statusDiv.parentElement) {
                    statusDiv.remove();
                }
            }, 10000);
        }

        function testVehicleData() {
            const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            addStatus(`Veículos encontrados: ${vehicles.length}`, vehicles.length > 0 ? 'success' : 'warning');
            
            if (vehicles.length > 0) {
                console.log('🚗 Veículos:', vehicles);
                updateVehiclesList();
                populateTestVehicleSelect();
            }
        }

        function testLavagemSystem() {
            if (typeof LavagemSystem !== 'undefined') {
                addStatus('LavagemSystem está disponível!', 'success');
                
                if (window.lavagemSystem) {
                    addStatus('Instância do LavagemSystem existe!', 'success');
                    console.log('🧽 Sistema de lavagem:', window.lavagemSystem);
                } else {
                    addStatus('Criando instância do LavagemSystem...', 'warning');
                    try {
                        window.lavagemSystem = new LavagemSystem();
                        addStatus('LavagemSystem criado com sucesso!', 'success');
                    } catch (error) {
                        addStatus(`Erro ao criar LavagemSystem: ${error.message}`, 'error');
                    }
                }
            } else {
                addStatus('LavagemSystem NÃO está disponível!', 'error');
            }
        }

        function createTestVehicles() {
            const testVehicles = [
                { id: 1, brand: 'Toyota', model: 'Corolla', year: 2020, plate: 'ABC-1234', status: 'ativo' },
                { id: 2, brand: 'Honda', model: 'Civic', year: 2019, plate: 'DEF-5678', status: 'ativo' },
                { id: 3, brand: 'Ford', model: 'Focus', year: 2021, plate: 'GHI-9012', status: 'ativo' }
            ];
            
            localStorage.setItem('frotas_vehicles', JSON.stringify(testVehicles));
            addStatus('Veículos de teste criados!', 'success');
            
            // Recarregar sistema se existir
            if (window.lavagemSystem) {
                window.lavagemSystem.veiculos = window.lavagemSystem.getVeiculosFromStorage();
                window.lavagemSystem.loadVeiculos();
            }
            
            updateVehiclesList();
            populateTestVehicleSelect();
        }

        function testFormSubmission() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            console.log('📋 Dados do formulário de teste:');
            for (let [key, value] of formData.entries()) {
                console.log(`  ${key}: ${value}`);
            }
            
            addStatus('Dados do formulário logados no console', 'info');
        }

        function testFormData() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            const data = {
                veiculo: formData.get('veiculo'),
                dataLavagem: formData.get('dataLavagem'),
                tipoLavagem: formData.get('tipoLavagem'),
                localLavagem: formData.get('localLavagem'),
                valor: formData.get('valor'),
                responsavel: formData.get('responsavel')
            };
            
            console.log('🧪 Teste de dados:', data);
            
            let errors = [];
            if (!data.veiculo) errors.push('Veículo não selecionado');
            if (!data.dataLavagem) errors.push('Data não informada');
            if (!data.tipoLavagem) errors.push('Tipo não selecionado');
            if (!data.localLavagem) errors.push('Local não selecionado');
            if (!data.valor) errors.push('Valor não informado');
            if (!data.responsavel) errors.push('Responsável não informado');
            
            if (errors.length > 0) {
                addStatus(`Erros encontrados: ${errors.join(', ')}`, 'error');
            } else {
                addStatus('Todos os campos estão preenchidos!', 'success');
            }
        }

        function clearAllData() {
            if (confirm('Tem certeza que deseja limpar todos os dados?')) {
                localStorage.removeItem('frotas_vehicles');
                localStorage.removeItem('lavagens');
                addStatus('Dados limpos!', 'warning');
                updateVehiclesList();
                updateLavagensList();
            }
        }

        function updateVehiclesList() {
            const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            const list = document.getElementById('vehiclesList');
            
            if (vehicles.length === 0) {
                list.innerHTML = '<p class="text-muted">Nenhum veículo encontrado</p>';
            } else {
                list.innerHTML = vehicles.map(v => 
                    `<div class="badge bg-primary me-1 mb-1">${v.plate} - ${v.brand} ${v.model}</div>`
                ).join('');
            }
        }

        function updateLavagensList() {
            const lavagens = JSON.parse(localStorage.getItem('lavagens') || '[]');
            const list = document.getElementById('lavagensList');
            
            if (lavagens.length === 0) {
                list.innerHTML = '<p class="text-muted">Nenhuma lavagem encontrada</p>';
            } else {
                list.innerHTML = lavagens.map(l => 
                    `<div class="badge bg-success me-1 mb-1">${l.veiculo} - R$ ${l.valor}</div>`
                ).join('');
            }
        }

        function populateTestVehicleSelect() {
            const vehicles = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            const select = document.getElementById('testVeiculo');
            
            select.innerHTML = '<option value="">Selecione o veículo</option>';
            vehicles.forEach(vehicle => {
                const option = document.createElement('option');
                option.value = vehicle.id;
                option.textContent = `${vehicle.brand} ${vehicle.model} ${vehicle.year} - ${vehicle.plate}`;
                select.appendChild(option);
            });
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            addStatus('Página de debug carregada!', 'success');
            
            // Definir data/hora atual
            const now = new Date();
            now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
            document.getElementById('testData').value = now.toISOString().slice(0, 16);
            
            // Preencher campos de teste
            document.getElementById('testTipo').value = 'completa';
            document.getElementById('testLocal').value = 'interno';
            document.getElementById('testValor').value = '25.00';
            document.getElementById('testResponsavel').value = 'João Silva';
            
            setTimeout(() => {
                testVehicleData();
                testLavagemSystem();
                updateLavagensList();
            }, 1000);
        });
    </script>
</body>
</html>
