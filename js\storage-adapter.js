/**
 * Adaptador de Armazenamento
 * Fornece uma interface compatível com localStorage mas usando FileStorage
 */
class StorageAdapter {
    constructor() {
        this.fileStorage = new FileStorage();
        this.ready = false;
        this.init();
    }

    async init() {
        // Aguardar inicialização do FileStorage
        while (!this.fileStorage.initialized) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        this.ready = true;
        console.log('✅ StorageAdapter pronto para uso');
    }

    // Aguardar que o adapter esteja pronto
    async waitForReady() {
        while (!this.ready) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // Salvar item (compatível com localStorage.setItem)
    async setItem(key, value) {
        await this.waitForReady();
        
        try {
            // Se value já é um objeto, usar diretamente
            // Se é string, tentar fazer parse para manter compatibilidade
            let data = value;
            if (typeof value === 'string') {
                try {
                    data = JSON.parse(value);
                } catch (e) {
                    // Se não conseguir fazer parse, manter como string
                    data = value;
                }
            }
            
            const success = await this.fileStorage.setItem(key, data);
            if (!success) {
                throw new Error(`Falha ao salvar ${key}`);
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Erro no StorageAdapter.setItem(${key}):`, error);
            throw error;
        }
    }

    // Carregar item (compatível com localStorage.getItem)
    async getItem(key) {
        await this.waitForReady();
        
        try {
            const data = await this.fileStorage.getItem(key);
            
            if (data === null) {
                return null;
            }
            
            // Para manter compatibilidade com localStorage, retornar como string JSON
            return typeof data === 'string' ? data : JSON.stringify(data);
        } catch (error) {
            console.error(`❌ Erro no StorageAdapter.getItem(${key}):`, error);
            return null;
        }
    }

    // Carregar item como objeto (método adicional)
    async getItemAsObject(key) {
        await this.waitForReady();
        
        try {
            return await this.fileStorage.getItem(key);
        } catch (error) {
            console.error(`❌ Erro no StorageAdapter.getItemAsObject(${key}):`, error);
            return null;
        }
    }

    // Remover item (compatível com localStorage.removeItem)
    async removeItem(key) {
        await this.waitForReady();
        
        try {
            const success = await this.fileStorage.removeItem(key);
            if (!success) {
                throw new Error(`Falha ao remover ${key}`);
            }
            return true;
        } catch (error) {
            console.error(`❌ Erro no StorageAdapter.removeItem(${key}):`, error);
            throw error;
        }
    }

    // Limpar todos os dados (compatível com localStorage.clear)
    async clear() {
        await this.waitForReady();
        
        try {
            const success = await this.fileStorage.clear();
            if (!success) {
                throw new Error('Falha ao limpar dados');
            }
            return true;
        } catch (error) {
            console.error('❌ Erro no StorageAdapter.clear():', error);
            throw error;
        }
    }

    // Obter chave por índice (compatível com localStorage.key)
    async key(index) {
        await this.waitForReady();
        
        try {
            const keys = await this.fileStorage.getAllKeys();
            return keys[index] || null;
        } catch (error) {
            console.error(`❌ Erro no StorageAdapter.key(${index}):`, error);
            return null;
        }
    }

    // Obter número de itens (compatível com localStorage.length)
    async length() {
        await this.waitForReady();
        
        try {
            const keys = await this.fileStorage.getAllKeys();
            return keys.length;
        } catch (error) {
            console.error('❌ Erro no StorageAdapter.length():', error);
            return 0;
        }
    }

    // Obter todas as chaves
    async getAllKeys() {
        await this.waitForReady();
        
        try {
            return await this.fileStorage.getAllKeys();
        } catch (error) {
            console.error('❌ Erro no StorageAdapter.getAllKeys():', error);
            return [];
        }
    }

    // Obter estatísticas de armazenamento
    async getStats() {
        await this.waitForReady();
        
        try {
            return await this.fileStorage.getStorageStats();
        } catch (error) {
            console.error('❌ Erro no StorageAdapter.getStats():', error);
            return null;
        }
    }

    // Método para migrar dados do localStorage para FileStorage
    async migrateFromLocalStorage() {
        await this.waitForReady();
        
        try {
            console.log('🔄 Iniciando migração do localStorage para FileStorage...');
            
            const migratedKeys = [];
            const errors = [];
            
            // Iterar sobre todos os itens do localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key) {
                    try {
                        const value = localStorage.getItem(key);
                        if (value) {
                            // Tentar fazer parse do JSON
                            let data;
                            try {
                                data = JSON.parse(value);
                            } catch (e) {
                                data = value; // Manter como string se não for JSON
                            }
                            
                            // Salvar no FileStorage
                            const success = await this.fileStorage.setItem(key, data);
                            if (success) {
                                migratedKeys.push(key);
                                console.log(`✅ Migrado: ${key}`);
                            } else {
                                errors.push(`Falha ao migrar ${key}`);
                            }
                        }
                    } catch (error) {
                        errors.push(`Erro ao migrar ${key}: ${error.message}`);
                        console.error(`❌ Erro ao migrar ${key}:`, error);
                    }
                }
            }
            
            console.log(`🎉 Migração concluída: ${migratedKeys.length} itens migrados`);
            
            if (errors.length > 0) {
                console.warn(`⚠️ Erros durante migração:`, errors);
            }
            
            return {
                success: true,
                migratedKeys,
                errors,
                totalMigrated: migratedKeys.length
            };
        } catch (error) {
            console.error('❌ Erro durante migração:', error);
            return {
                success: false,
                error: error.message,
                migratedKeys: [],
                errors: [error.message],
                totalMigrated: 0
            };
        }
    }

    // Método para limpar localStorage após migração bem-sucedida
    async clearLocalStorageAfterMigration() {
        try {
            const keys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key) keys.push(key);
            }
            
            localStorage.clear();
            console.log(`🧹 localStorage limpo: ${keys.length} itens removidos`);
            
            return {
                success: true,
                clearedKeys: keys,
                totalCleared: keys.length
            };
        } catch (error) {
            console.error('❌ Erro ao limpar localStorage:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Verificar se há dados no localStorage que precisam ser migrados
    hasLocalStorageData() {
        return localStorage.length > 0;
    }

    // Obter informações sobre dados no localStorage
    getLocalStorageInfo() {
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                const value = localStorage.getItem(key);
                keys.push({
                    key,
                    size: value ? value.length : 0,
                    hasData: !!value
                });
            }
        }
        
        return {
            totalKeys: keys.length,
            keys,
            totalSize: keys.reduce((sum, item) => sum + item.size, 0)
        };
    }
}

// Criar instância global
const storageAdapter = new StorageAdapter();

// Exportar para uso em outros módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageAdapter;
}
