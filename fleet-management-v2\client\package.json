{"name": "fleet-management-client", "version": "1.0.0", "description": "Frontend React para Sistema de Gestão de Frotas", "private": true, "dependencies": {"@supabase/supabase-js": "^2.38.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-datepicker": "^4.24.0", "react-select": "^5.8.0", "react-table": "^7.8.0", "styled-components": "^6.1.6", "framer-motion": "^10.16.16", "react-modal": "^3.16.1", "react-loading-skeleton": "^3.3.1", "react-helmet-async": "^2.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "devDependencies": {"react-scripts": "5.0.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}