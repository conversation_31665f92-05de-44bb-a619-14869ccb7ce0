{"name": "fleet-management-frontend", "version": "1.0.0", "description": "Frontend React para Sistema de Gestão de Frotas", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "@supabase/supabase-js": "^2.38.0", "axios": "^1.6.0", "react-query": "^3.39.3", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-table": "^7.8.0", "react-modal": "^3.16.1", "react-select": "^5.8.0", "react-datepicker": "^4.21.0", "react-icons": "^4.11.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.4", "react-pdf": "^7.5.1", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-table": "^7.7.17", "@types/react-modal": "^3.16.3", "@types/react-datepicker": "^4.19.4", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}, "engines": {"node": ">=18.0.0"}}