// Sistema de Revisão
class RevisaoSystem {
    constructor() {
        this.revisoes = JSON.parse(localStorage.getItem('revisoes')) || [];
        this.veiculos = this.getVeiculosFromStorage();
        this.filteredData = [...this.revisoes];

        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        this.loadVeiculos();
        this.loadRevisoes();
        this.setupEventListeners();
        this.updateStats();

        // Inicializar calendário após um pequeno delay para garantir que o DOM esteja pronto
        setTimeout(() => {
            this.initializeCalendar();
        }, 100);
    }

    // Carregar veículos do sistema
    getVeiculosFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                console.log('🚗 Veículos carregados do localStorage:', vehicles);

                return vehicles.map(v => {
                    // Verificar diferentes estruturas possíveis dos dados
                    const veiculo = {
                        id: v.id,
                        modelo: v.modelo || `${v.brand || v.marca || ''} ${v.model || v.modelo || ''} ${v.year || v.ano || ''}`.trim(),
                        placa: v.placa || v.plate || '',
                        kmAtual: this.getKmAtualFromAbastecimento(v.id) // Buscar KM do abastecimento
                    };

                    console.log(`📊 Veículo processado: ${veiculo.placa} - KM: ${veiculo.kmAtual}`);
                    return veiculo;
                });
            } catch (error) {
                console.error('❌ Erro ao carregar veículos:', error);
                return [];
            }
        }

        console.warn('⚠️ Nenhum veículo encontrado no localStorage');
        return [];
    }

    // Buscar KM atual do último abastecimento do veículo
    getKmAtualFromAbastecimento(veiculoId) {
        try {
            const savedAbastecimentos = localStorage.getItem('abastecimentos');
            if (!savedAbastecimentos) {
                console.log(`ℹ️ Nenhum abastecimento encontrado para veículo ${veiculoId}`);
                return 0;
            }

            const abastecimentos = JSON.parse(savedAbastecimentos);

            // Filtrar abastecimentos do veículo e ordenar por data (mais recente primeiro)
            const abastecimentosVeiculo = abastecimentos
                .filter(a => a.veiculoId == veiculoId)
                .sort((a, b) => {
                    const dateA = new Date(a.data);
                    const dateB = new Date(b.data);
                    if (dateA.getTime() === dateB.getTime()) {
                        return b.id - a.id; // Se mesma data, ordenar por ID (mais recente primeiro)
                    }
                    return dateB - dateA; // Ordenar por data (mais recente primeiro)
                });

            if (abastecimentosVeiculo.length > 0) {
                const ultimoAbastecimento = abastecimentosVeiculo[0];
                const kmAtual = parseInt(ultimoAbastecimento.kmAtual) || 0;
                console.log(`📊 KM atual do veículo ${veiculoId} obtido do abastecimento: ${kmAtual} km`);
                return kmAtual;
            }

            console.log(`ℹ️ Nenhum abastecimento encontrado para veículo ${veiculoId}`);
            return 0;

        } catch (error) {
            console.error('❌ Erro ao buscar KM do abastecimento:', error);
            return 0;
        }
    }



    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }
    }

    // Carregar revisões nas tabelas
    loadRevisoes() {
        this.loadProximasRevisoes();
        this.loadHistoricoRevisoes();
    }

    // Carregar próximas revisões
    loadProximasRevisoes() {
        const tbody = document.getElementById('proximasRevisoesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const proximasRevisoes = this.filteredData.filter(r => 
            r.status === 'agendada' || r.status === 'em_andamento'
        ).sort((a, b) => new Date(a.dataAgendamento) - new Date(b.dataAgendamento));

        proximasRevisoes.forEach(revisao => {
            const veiculo = this.veiculos.find(v => v.id == revisao.veiculoId);
            const row = document.createElement('tr');
            
            const isVencida = new Date(revisao.dataAgendamento) < new Date();
            const statusClass = this.getStatusClass(revisao.status, isVencida);
            const statusText = isVencida && revisao.status === 'agendada' ? 'Vencida' : this.getStatusText(revisao.status);
            
            row.innerHTML = `
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td><span class="badge bg-info">${this.getTipoText(revisao.tipo)}</span></td>
                <td>${Utils.formatDate(revisao.dataAgendamento)}</td>
                <td>${veiculo ? Utils.formatKm(veiculo.kmAtual) : 'N/A'}</td>
                <td>${revisao.kmRevisao ? Utils.formatKm(revisao.kmRevisao) : 'N/A'}</td>
                <td><span class="badge ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="revisaoSystem.editRevisao(${revisao.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="revisaoSystem.concluirRevisao(${revisao.id})">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="revisaoSystem.deleteRevisao(${revisao.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Carregar histórico de revisões
    loadHistoricoRevisoes() {
        const tbody = document.getElementById('historicoRevisoesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const historicoRevisoes = this.filteredData
            .sort((a, b) => new Date(b.dataAgendamento) - new Date(a.dataAgendamento));

        historicoRevisoes.forEach(revisao => {
            const veiculo = this.veiculos.find(v => v.id == revisao.veiculoId);
            const row = document.createElement('tr');
            
            const isVencida = new Date(revisao.dataAgendamento) < new Date();
            const statusClass = this.getStatusClass(revisao.status, isVencida);
            const statusText = isVencida && revisao.status === 'agendada' ? 'Vencida' : this.getStatusText(revisao.status);
            
            row.innerHTML = `
                <td>${Utils.formatDate(revisao.dataAgendamento)}</td>
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td><span class="badge bg-info">${this.getTipoText(revisao.tipo)}</span></td>
                <td>${revisao.oficina}</td>
                <td>${revisao.valorReal ? Utils.formatCurrency(revisao.valorReal) : (revisao.valorEstimado ? Utils.formatCurrency(revisao.valorEstimado) : 'N/A')}</td>
                <td><span class="badge ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="revisaoSystem.viewRevisao(${revisao.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="revisaoSystem.editRevisao(${revisao.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="revisaoSystem.deleteRevisao(${revisao.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Obter classe CSS do status
    getStatusClass(status, isVencida = false) {
        if (isVencida && status === 'agendada') return 'bg-danger';
        
        switch (status) {
            case 'agendada': return 'bg-warning';
            case 'em_andamento': return 'bg-info';
            case 'concluida': return 'bg-success';
            default: return 'bg-secondary';
        }
    }

    // Obter texto do status
    getStatusText(status) {
        switch (status) {
            case 'agendada': return 'Agendada';
            case 'em_andamento': return 'Em Andamento';
            case 'concluida': return 'Concluída';
            default: return status;
        }
    }

    // Obter texto do tipo
    getTipoText(tipo) {
        switch (tipo) {
            case 'preventiva': return 'Preventiva';
            case 'periodica': return 'Periódica';
            case 'obrigatoria': return 'Obrigatória';
            default: return tipo;
        }
    }

    // Atualizar estatísticas
    updateStats() {
        const total = this.filteredData.length;
        const agendadas = this.filteredData.filter(r => r.status === 'agendada').length;
        const concluidas = this.filteredData.filter(r => r.status === 'concluida').length;
        const vencidas = this.filteredData.filter(r => 
            r.status === 'agendada' && new Date(r.dataAgendamento) < new Date()
        ).length;

        document.getElementById('totalRevisoes').textContent = total;
        document.getElementById('revisoesAgendadas').textContent = agendadas;
        document.getElementById('revisoesConcluidas').textContent = concluidas;
        document.getElementById('revisoesVencidas').textContent = vencidas;
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de revisão
        const form = document.getElementById('revisaoForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        const filterStatus = document.getElementById('filterStatus');
        const filterTipo = document.getElementById('filterTipo');

        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => this.applyFilters(), 300));
        }

        if (filterStatus) {
            filterStatus.addEventListener('change', () => this.applyFilters());
        }

        if (filterTipo) {
            filterTipo.addEventListener('change', () => this.applyFilters());
        }

        // Listener para atualizar KM atual quando veículo for selecionado
        const veiculoSelect = document.getElementById('veiculo');
        if (veiculoSelect) {
            veiculoSelect.addEventListener('change', (e) => this.updateKmAtual(e.target.value));
        }
    }

    // Atualizar KM atual baseado no veículo selecionado
    updateKmAtual(veiculoId) {
        if (!veiculoId) {
            const kmAtualDisplay = document.getElementById('kmAtualDisplay');
            if (kmAtualDisplay) {
                kmAtualDisplay.style.display = 'none';
            }
            return;
        }

        const veiculo = this.veiculos.find(v => v.id == veiculoId);
        const kmAtualDisplay = document.getElementById('kmAtualDisplay');

        if (veiculo && kmAtualDisplay) {
            // Buscar KM mais atualizado do abastecimento
            const kmAtualizado = this.getKmAtualFromAbastecimento(veiculoId);
            const kmParaExibir = kmAtualizado > 0 ? kmAtualizado : veiculo.kmAtual;

            kmAtualDisplay.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-tachometer-alt text-primary me-2"></i>
                    <strong>KM Atual:</strong>
                    <span class="text-primary ms-1">${this.formatKm(kmParaExibir)}</span>
                    <small class="text-muted ms-2">(último abastecimento)</small>
                </div>
            `;
            kmAtualDisplay.style.display = 'block';
            kmAtualDisplay.className = 'alert alert-info mt-2';
            console.log(`📊 KM atual atualizado: ${veiculo.placa} - ${kmParaExibir} km (do abastecimento)`);
        } else if (kmAtualDisplay) {
            kmAtualDisplay.style.display = 'none';
        }
    }

    // Formatar quilometragem
    formatKm(km) {
        if (!km || km === 0) return '0 km';
        return new Intl.NumberFormat('pt-BR').format(km) + ' km';
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const status = document.getElementById('filterStatus')?.value || '';
        const tipo = document.getElementById('filterTipo')?.value || '';

        this.filteredData = this.revisoes.filter(revisao => {
            const veiculo = this.veiculos.find(v => v.id == revisao.veiculoId);
            
            // Filtro de busca
            const matchesSearch = !searchTerm || 
                (veiculo && (veiculo.modelo.toLowerCase().includes(searchTerm) || 
                veiculo.placa.toLowerCase().includes(searchTerm))) ||
                revisao.oficina.toLowerCase().includes(searchTerm);

            // Filtro de status (incluindo vencidas)
            let matchesStatus = !status;
            if (status === 'vencida') {
                matchesStatus = revisao.status === 'agendada' && new Date(revisao.dataAgendamento) < new Date();
            } else if (status) {
                matchesStatus = revisao.status === status;
            }

            // Filtro de tipo
            const matchesTipo = !tipo || revisao.tipo === tipo;

            return matchesSearch && matchesStatus && matchesTipo;
        });

        this.loadRevisoes();
        this.updateStats();
        this.loadCalendar(); // Atualizar calendário
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        const form = e.target;
        const editId = form.dataset.editId;

        const revisaoData = {
            veiculoId: parseInt(data.veiculo),
            tipo: data.tipoRevisao,
            dataAgendamento: data.dataAgendamento,
            kmRevisao: data.kmRevisao ? parseInt(data.kmRevisao) : null,
            oficina: data.oficina,
            contato: data.contato || '',
            valorEstimado: data.valorEstimado ? parseFloat(data.valorEstimado) : null,
            status: data.status || 'agendada',
            itensRevisao: data.itensRevisao || '',
            observacoes: data.observacoes || ''
        };

        if (editId) {
            // Modo edição
            const revisaoIndex = this.revisoes.findIndex(r => r.id === parseInt(editId));
            if (revisaoIndex !== -1) {
                // Manter dados originais que não devem ser alterados
                const revisaoOriginal = this.revisoes[revisaoIndex];
                this.revisoes[revisaoIndex] = {
                    ...revisaoOriginal,
                    ...revisaoData
                };

                console.log('✅ Revisão atualizada com sucesso!');
                alert('Revisão atualizada com sucesso!');
            }
        } else {
            // Modo criação
            const novaRevisao = {
                id: Date.now(),
                ...revisaoData,
                valorReal: null,
                dataConclusao: null
            };

            this.revisoes.unshift(novaRevisao);
            console.log('✅ Revisão agendada com sucesso!');
            alert('Revisão agendada com sucesso!');
        }

        // Salvar no localStorage
        localStorage.setItem('revisoes', JSON.stringify(this.revisoes));

        this.filteredData = [...this.revisoes];
        this.loadRevisoes();
        this.updateStats();
        this.loadCalendar(); // Atualizar calendário

        // Fechar modal e limpar formulário
        const modal = bootstrap.Modal.getInstance(document.getElementById('revisaoModal'));
        modal.hide();
        form.reset();
        form.removeAttribute('data-edit-id');
    }

    // Editar revisão
    editRevisao(id) {
        const revisao = this.revisoes.find(r => r.id === id);
        if (!revisao) return;

        // Preencher formulário com dados existentes
        document.getElementById('veiculo').value = revisao.veiculoId;
        document.getElementById('tipoRevisao').value = revisao.tipo;
        document.getElementById('dataAgendamento').value = revisao.dataAgendamento;
        document.getElementById('kmRevisao').value = revisao.kmRevisao || '';
        document.getElementById('oficina').value = revisao.oficina;
        document.getElementById('contato').value = revisao.contato;
        document.getElementById('valorEstimado').value = revisao.valorEstimado || '';
        document.getElementById('status').value = revisao.status;
        document.getElementById('itensRevisao').value = revisao.itensRevisao;
        document.getElementById('observacoes').value = revisao.observacoes;

        // Atualizar KM atual para o veículo selecionado
        this.updateKmAtual(revisao.veiculoId);

        // Abrir modal
        const modal = new bootstrap.Modal(document.getElementById('revisaoModal'));
        modal.show();

        // Alterar comportamento do formulário para edição
        const form = document.getElementById('revisaoForm');
        form.dataset.editId = id;
    }

    // Concluir revisão
    concluirRevisao(id) {
        const valorReal = prompt('Informe o valor real da revisão (opcional):');
        
        const revisaoIndex = this.revisoes.findIndex(r => r.id === id);
        if (revisaoIndex !== -1) {
            this.revisoes[revisaoIndex].status = 'concluida';
            this.revisoes[revisaoIndex].dataConclusao = new Date().toISOString();
            if (valorReal && !isNaN(parseFloat(valorReal))) {
                this.revisoes[revisaoIndex].valorReal = parseFloat(valorReal);
            }
            
            localStorage.setItem('revisoes', JSON.stringify(this.revisoes));
            this.filteredData = [...this.revisoes];
            this.loadRevisoes();
            this.updateStats();
            this.loadCalendar(); // Atualizar calendário

            console.log('✅ Revisão concluída com sucesso!');
            alert('Revisão concluída com sucesso!');
        }
    }

    // Visualizar revisão
    viewRevisao(id) {
        const revisao = this.revisoes.find(r => r.id === id);
        const veiculo = this.veiculos.find(v => v.id == revisao.veiculoId);
        
        if (!revisao) return;

        const detalhes = `
            <strong>Veículo:</strong> ${veiculo ? veiculo.modelo + ' - ' + veiculo.placa : 'N/A'}<br>
            <strong>Tipo:</strong> ${this.getTipoText(revisao.tipo)}<br>
            <strong>Data:</strong> ${Utils.formatDate(revisao.dataAgendamento)}<br>
            <strong>Oficina:</strong> ${revisao.oficina}<br>
            <strong>Contato:</strong> ${revisao.contato}<br>
            <strong>Valor:</strong> ${revisao.valorReal ? Utils.formatCurrency(revisao.valorReal) : (revisao.valorEstimado ? Utils.formatCurrency(revisao.valorEstimado) : 'N/A')}<br>
            <strong>Status:</strong> ${this.getStatusText(revisao.status)}<br>
            <strong>Itens:</strong> ${revisao.itensRevisao}<br>
            <strong>Observações:</strong> ${revisao.observacoes}
        `;

        // Criar modal simples para exibir detalhes
        const modalHtml = `
            <div class="modal fade" id="viewRevisaoModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Detalhes da Revisão</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${detalhes}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal anterior se existir
        const existingModal = document.getElementById('viewRevisaoModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Adicionar novo modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('viewRevisaoModal'));
        modal.show();
    }

    // Deletar revisão
    deleteRevisao(id) {
        if (confirm('Deseja realmente excluir esta revisão?')) {
            this.revisoes = this.revisoes.filter(r => r.id !== id);
            localStorage.setItem('revisoes', JSON.stringify(this.revisoes));

            this.filteredData = [...this.revisoes];
            this.loadRevisoes();
            this.updateStats();
            this.loadCalendar(); // Atualizar calendário

            console.log('✅ Revisão excluída com sucesso!');
            alert('Revisão excluída com sucesso!');
        }
    }

    // Inicializar calendário
    initializeCalendar() {
        console.log('📅 Inicializando calendário de revisões...');

        // Verificar se FullCalendar está disponível
        if (typeof FullCalendar === 'undefined') {
            console.error('❌ FullCalendar não está carregado');
            return;
        }

        const calendarEl = document.getElementById('calendar');
        if (!calendarEl) {
            console.error('❌ Elemento calendar não encontrado');
            return;
        }

        // Configurar calendário
        this.calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'pt-br',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,listWeek'
            },
            buttonText: {
                today: 'Hoje',
                month: 'Mês',
                week: 'Semana',
                list: 'Lista'
            },
            height: 'auto',
            events: [],
            eventClick: (info) => {
                this.handleCalendarEventClick(info);
            },
            dateClick: (info) => {
                this.handleCalendarDateClick(info);
            },
            eventDidMount: (info) => {
                // Adicionar classes CSS baseadas no status
                const event = info.event;
                const status = event.extendedProps.status;
                const isOverdue = event.extendedProps.isOverdue;

                if (isOverdue) {
                    info.el.classList.add('revision-overdue');
                } else if (status === 'agendada') {
                    info.el.classList.add('revision-upcoming');
                } else if (status === 'concluida') {
                    info.el.classList.add('revision-completed');
                } else {
                    info.el.classList.add('revision-scheduled');
                }
            }
        });

        // Renderizar calendário
        this.calendar.render();
        console.log('✅ Calendário inicializado com sucesso');

        // Carregar eventos
        this.loadCalendar();
    }

    // Carregar eventos no calendário
    loadCalendar() {
        if (!this.calendar) {
            console.warn('⚠️ Calendário não inicializado');
            return;
        }

        console.log('📅 Carregando eventos no calendário...');

        // Limpar eventos existentes
        this.calendar.removeAllEvents();

        // Converter revisões em eventos do calendário
        const events = this.filteredData.map(revisao => {
            const veiculo = this.veiculos.find(v => v.id == revisao.veiculoId);
            const isOverdue = new Date(revisao.dataAgendamento) < new Date() && revisao.status === 'agendada';

            let backgroundColor, borderColor, textColor;

            // Definir cores baseadas no status
            if (isOverdue) {
                backgroundColor = '#dc3545';
                borderColor = '#dc3545';
                textColor = '#ffffff';
            } else if (revisao.status === 'agendada') {
                backgroundColor = '#ffc107';
                borderColor = '#ffc107';
                textColor = '#212529';
            } else if (revisao.status === 'concluida') {
                backgroundColor = '#28a745';
                borderColor = '#28a745';
                textColor = '#ffffff';
            } else {
                backgroundColor = '#17a2b8';
                borderColor = '#17a2b8';
                textColor = '#ffffff';
            }

            return {
                id: revisao.id,
                title: `${veiculo ? veiculo.placa : 'N/A'} - ${this.getTipoText(revisao.tipo)}`,
                start: revisao.dataAgendamento,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                textColor: textColor,
                extendedProps: {
                    revisaoId: revisao.id,
                    status: revisao.status,
                    isOverdue: isOverdue,
                    veiculo: veiculo,
                    oficina: revisao.oficina,
                    tipo: revisao.tipo,
                    valorEstimado: revisao.valorEstimado,
                    valorReal: revisao.valorReal
                }
            };
        });

        // Adicionar eventos ao calendário
        this.calendar.addEventSource(events);
        console.log(`✅ ${events.length} eventos carregados no calendário`);
    }

    // Manipular clique em evento do calendário
    handleCalendarEventClick(info) {
        const revisaoId = info.event.extendedProps.revisaoId;
        console.log('📅 Evento clicado:', revisaoId);

        // Mostrar detalhes da revisão
        this.viewRevisao(revisaoId);
    }

    // Manipular clique em data do calendário
    handleCalendarDateClick(info) {
        console.log('📅 Data clicada:', info.dateStr);

        // Abrir modal para nova revisão com data pré-preenchida
        const form = document.getElementById('revisaoForm');
        form.removeAttribute('data-edit-id');
        form.reset();

        // Definir data clicada
        const clickedDate = new Date(info.date);
        clickedDate.setHours(9, 0, 0, 0); // Definir horário padrão 9:00
        const localDateTime = new Date(clickedDate.getTime() - clickedDate.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);

        document.getElementById('dataAgendamento').value = localDateTime;

        const modal = new bootstrap.Modal(document.getElementById('revisaoModal'));
        modal.show();
    }
}

// Funções globais
function openRevisaoModal() {
    // Limpar dados de edição
    const form = document.getElementById('revisaoForm');
    form.removeAttribute('data-edit-id');
    form.reset();
    
    // Definir data atual + 1 dia
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setMinutes(tomorrow.getMinutes() - tomorrow.getTimezoneOffset());
    document.getElementById('dataAgendamento').value = tomorrow.toISOString().slice(0, 16);
    
    const modal = new bootstrap.Modal(document.getElementById('revisaoModal'));
    modal.show();
}

// Inicializar sistema quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('proximasRevisoesTable')) {
        window.revisaoSystem = new RevisaoSystem();
    }
});
