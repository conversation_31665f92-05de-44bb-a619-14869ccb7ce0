-- ========================================
-- POLÍTICAS RLS (ROW LEVEL SECURITY)
-- Sistema de Gestão de Frotas v2.0
-- ========================================

-- <PERSON><PERSON><PERSON>r RLS em todas as tabelas
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fuel_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.washing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revision_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cash_flow ENABLE ROW LEVEL SECURITY;

-- ========================================
-- POLÍTICAS PARA PROFILES
-- ========================================

CREATE POLICY "Users can view own profile" ON public.profiles 
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- ========================================
-- POLÍTICAS PARA VEHICLES
-- ========================================

CREATE POLICY "Users can view own vehicles" ON public.vehicles 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own vehicles" ON public.vehicles 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own vehicles" ON public.vehicles 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own vehicles" ON public.vehicles 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- POLÍTICAS PARA SUPPLIERS
-- ========================================

CREATE POLICY "Users can view own suppliers" ON public.suppliers 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own suppliers" ON public.suppliers 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own suppliers" ON public.suppliers 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own suppliers" ON public.suppliers 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- POLÍTICAS PARA FUEL_RECORDS
-- ========================================

CREATE POLICY "Users can view own fuel records" ON public.fuel_records 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own fuel records" ON public.fuel_records 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own fuel records" ON public.fuel_records 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own fuel records" ON public.fuel_records 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- POLÍTICAS PARA MAINTENANCE_RECORDS
-- ========================================

CREATE POLICY "Users can view own maintenance records" ON public.maintenance_records 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own maintenance records" ON public.maintenance_records 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own maintenance records" ON public.maintenance_records 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own maintenance records" ON public.maintenance_records 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- POLÍTICAS PARA WASHING_RECORDS
-- ========================================

CREATE POLICY "Users can view own washing records" ON public.washing_records 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own washing records" ON public.washing_records 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own washing records" ON public.washing_records 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own washing records" ON public.washing_records 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- POLÍTICAS PARA REVISION_RECORDS
-- ========================================

CREATE POLICY "Users can view own revision records" ON public.revision_records 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own revision records" ON public.revision_records 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own revision records" ON public.revision_records 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own revision records" ON public.revision_records 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- POLÍTICAS PARA CASH_FLOW
-- ========================================

CREATE POLICY "Users can view own cash flow records" ON public.cash_flow 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own cash flow records" ON public.cash_flow 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own cash flow records" ON public.cash_flow 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own cash flow records" ON public.cash_flow 
    FOR DELETE USING (auth.uid() = user_id);

-- ========================================
-- FUNÇÃO PARA CRIAR PERFIL AUTOMATICAMENTE
-- ========================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    CASE 
      WHEN (SELECT COUNT(*) FROM public.profiles WHERE role = 'admin') = 0 
      THEN 'admin' 
      ELSE 'user' 
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para criar perfil automaticamente quando usuário se registra
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ========================================
-- CONFIGURAÇÕES DE STORAGE (OPCIONAL)
-- ========================================

-- Criar buckets para arquivos
INSERT INTO storage.buckets (id, name, public) VALUES ('documents', 'documents', false);
INSERT INTO storage.buckets (id, name, public) VALUES ('images', 'images', true);

-- Políticas para storage
CREATE POLICY "Users can upload own documents" ON storage.objects 
    FOR INSERT WITH CHECK (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view own documents" ON storage.objects 
    FOR SELECT USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can upload images" ON storage.objects 
    FOR INSERT WITH CHECK (bucket_id = 'images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Anyone can view images" ON storage.objects 
    FOR SELECT USING (bucket_id = 'images');

-- ========================================
-- VIEWS ÚTEIS (OPCIONAL)
-- ========================================

-- View para estatísticas de veículos
CREATE VIEW vehicle_stats AS
SELECT 
    v.user_id,
    COUNT(*) as total_vehicles,
    COUNT(CASE WHEN v.status = 'ativo' THEN 1 END) as active_vehicles,
    COUNT(CASE WHEN v.status = 'manutencao' THEN 1 END) as maintenance_vehicles,
    COUNT(CASE WHEN v.status = 'inativo' THEN 1 END) as inactive_vehicles
FROM vehicles v
GROUP BY v.user_id;

-- View para resumo financeiro mensal
CREATE VIEW monthly_expenses AS
SELECT 
    cf.user_id,
    DATE_TRUNC('month', cf.data_transacao) as month,
    cf.categoria,
    SUM(CASE WHEN cf.tipo = 'saida' THEN cf.valor ELSE 0 END) as total_expenses,
    SUM(CASE WHEN cf.tipo = 'entrada' THEN cf.valor ELSE 0 END) as total_income,
    COUNT(*) as transaction_count
FROM cash_flow cf
GROUP BY cf.user_id, DATE_TRUNC('month', cf.data_transacao), cf.categoria;

-- ========================================
-- SCRIPT CONCLUÍDO
-- ========================================

-- Verificar se tudo foi criado corretamente
SELECT 'Setup completed successfully!' as status;
