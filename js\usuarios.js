// Sistema de Gerenciamento de Usuários
class UserManager {
    constructor() {
        this.users = this.loadUsers();
        this.currentUser = null;
        this.init();
    }

    init() {
        this.loadUsersTable();
        this.setupEventListeners();
        this.setupFilters();
    }

    // Carregar usuários do localStorage ou dados padrão
    loadUsers() {
        const savedUsers = localStorage.getItem('frotas_users');
        if (savedUsers) {
            return JSON.parse(savedUsers);
        }

        // Usuários padrão do sistema
        return [
            {
                id: 1,
                name: 'Administrador do Sistema',
                email: '<EMAIL>',
                role: 'admin',
                status: 'ativo',
                lastAccess: '2024-01-15 09:30:00',
                createdAt: '2024-01-01 00:00:00'
            },
            {
                id: 2,
                name: 'Supervisor de <PERSON>',
                email: '<EMAIL>',
                role: 'supervisor',
                status: 'ativo',
                lastAccess: '2024-01-15 08:45:00',
                createdAt: '2024-01-02 00:00:00'
            },
            {
                id: 3,
                name: 'Operador de <PERSON>',
                email: '<EMAIL>',
                role: 'operador',
                status: 'ativo',
                lastAccess: '2024-01-15 07:15:00',
                createdAt: '2024-01-03 00:00:00'
            },
            {
                id: 4,
                name: 'João Silva',
                email: '<EMAIL>',
                role: 'operador',
                status: 'inativo',
                lastAccess: '2024-01-10 16:20:00',
                createdAt: '2024-01-05 00:00:00'
            },
            {
                id: 5,
                name: 'Maria Santos',
                email: '<EMAIL>',
                role: 'supervisor',
                status: 'ativo',
                lastAccess: '2024-01-14 18:30:00',
                createdAt: '2024-01-08 00:00:00'
            }
        ];
    }

    // Salvar usuários no localStorage
    saveUsers() {
        localStorage.setItem('frotas_users', JSON.stringify(this.users));
    }

    // Configurar event listeners
    setupEventListeners() {
        // Busca em tempo real
        document.getElementById('searchUser').addEventListener('input', () => {
            this.filterUsers();
        });

        // Filtros
        document.getElementById('filterRole').addEventListener('change', () => {
            this.filterUsers();
        });

        document.getElementById('filterStatus').addEventListener('change', () => {
            this.filterUsers();
        });
    }

    // Configurar filtros
    setupFilters() {
        this.filterUsers();
    }

    // Carregar tabela de usuários
    loadUsersTable(usersToShow = null) {
        const tbody = document.getElementById('usersTableBody');
        const users = usersToShow || this.users;

        tbody.innerHTML = '';

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="user-checkbox" value="${user.id}">
                </td>
                <td>
                    <div class="user-info">
                        <strong>${user.name}</strong>
                    </div>
                </td>
                <td>${user.email}</td>
                <td>
                    <span class="badge badge-${this.getRoleBadgeClass(user.role)}">
                        ${this.getRoleLabel(user.role)}
                    </span>
                </td>
                <td>
                    <span class="badge badge-${user.status === 'ativo' ? 'success' : 'secondary'}">
                        ${user.status === 'ativo' ? 'Ativo' : 'Inativo'}
                    </span>
                </td>
                <td>${this.formatDateTime(user.lastAccess)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="userManager.editUser(${user.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="userManager.deleteUser(${user.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="userManager.resetPassword(${user.id})" title="Resetar Senha">
                            <i class="fas fa-key"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Filtrar usuários
    filterUsers() {
        const search = document.getElementById('searchUser').value.toLowerCase();
        const roleFilter = document.getElementById('filterRole').value;
        const statusFilter = document.getElementById('filterStatus').value;

        const filteredUsers = this.users.filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(search) || 
                                user.email.toLowerCase().includes(search);
            const matchesRole = !roleFilter || user.role === roleFilter;
            const matchesStatus = !statusFilter || user.status === statusFilter;

            return matchesSearch && matchesRole && matchesStatus;
        });

        this.loadUsersTable(filteredUsers);
    }

    // Limpar filtros
    clearFilters() {
        document.getElementById('searchUser').value = '';
        document.getElementById('filterRole').value = '';
        document.getElementById('filterStatus').value = '';
        this.loadUsersTable();
    }

    // Mostrar modal de adicionar usuário
    showAddUserModal() {
        this.currentUser = null;
        document.getElementById('userModalTitle').textContent = 'Novo Usuário';
        document.getElementById('userForm').reset();
        document.getElementById('userId').value = '';
        document.getElementById('userPassword').required = true;
        document.getElementById('userConfirmPassword').required = true;
        
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
    }

    // Editar usuário
    editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        this.currentUser = user;
        document.getElementById('userModalTitle').textContent = 'Editar Usuário';
        document.getElementById('userId').value = user.id;
        document.getElementById('userName').value = user.name;
        document.getElementById('userEmail').value = user.email;
        document.getElementById('userRole').value = user.role;
        document.getElementById('userStatus').value = user.status;
        document.getElementById('userPassword').required = false;
        document.getElementById('userConfirmPassword').required = false;
        
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
    }

    // Salvar usuário
    saveUser() {
        const form = document.getElementById('userForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const userId = document.getElementById('userId').value;
        const name = document.getElementById('userName').value;
        const email = document.getElementById('userEmail').value;
        const role = document.getElementById('userRole').value;
        const status = document.getElementById('userStatus').value;
        const password = document.getElementById('userPassword').value;
        const confirmPassword = document.getElementById('userConfirmPassword').value;

        // Validar senhas se fornecidas
        if (password && password !== confirmPassword) {
            alert('As senhas não coincidem!');
            return;
        }

        // Verificar email duplicado
        const existingUser = this.users.find(u => u.email === email && u.id != userId);
        if (existingUser) {
            alert('Este email já está sendo usado por outro usuário!');
            return;
        }

        if (userId) {
            // Editar usuário existente
            const userIndex = this.users.findIndex(u => u.id == userId);
            if (userIndex !== -1) {
                this.users[userIndex] = {
                    ...this.users[userIndex],
                    name,
                    email,
                    role,
                    status,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // Adicionar novo usuário
            const newUser = {
                id: Math.max(...this.users.map(u => u.id)) + 1,
                name,
                email,
                role,
                status,
                lastAccess: 'Nunca',
                createdAt: new Date().toISOString()
            };
            this.users.push(newUser);
        }

        this.saveUsers();
        this.loadUsersTable();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
        modal.hide();

        // Mostrar notificação
        const message = userId ? 'Usuário atualizado com sucesso!' : 'Usuário criado com sucesso!';
        console.log('✅', message);
        alert(message);
    }

    // Excluir usuário
    deleteUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        if (confirm(`Deseja realmente excluir o usuário "${user.name}"?`)) {
            this.users = this.users.filter(u => u.id !== userId);
            this.saveUsers();
            this.loadUsersTable();

            console.log('✅ Usuário excluído com sucesso!');
            alert('Usuário excluído com sucesso!');
        }
    }

    // Resetar senha
    resetPassword(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        if (confirm(`Deseja resetar a senha do usuário "${user.name}"?\nA nova senha será: 123456`)) {
            // Aqui você implementaria a lógica real de reset de senha
            const message = `Senha do usuário "${user.name}" foi resetada para: 123456`;
            console.log('ℹ️', message);
            alert(message);
        }
    }

    // Exportar usuários
    exportUsers() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `usuarios_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Gerar CSV
    generateCSV() {
        const headers = ['ID', 'Nome', 'Email', 'Nível de Acesso', 'Status', 'Último Acesso', 'Criado em'];
        const rows = this.users.map(user => [
            user.id,
            user.name,
            user.email,
            this.getRoleLabel(user.role),
            user.status === 'ativo' ? 'Ativo' : 'Inativo',
            user.lastAccess,
            user.createdAt
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    // Utilitários
    getRoleBadgeClass(role) {
        switch (role) {
            case 'admin': return 'danger';
            case 'supervisor': return 'warning';
            case 'operador': return 'info';
            default: return 'secondary';
        }
    }

    getRoleLabel(role) {
        switch (role) {
            case 'admin': return 'Administrador';
            case 'supervisor': return 'Supervisor';
            case 'operador': return 'Operador';
            default: return 'Desconhecido';
        }
    }

    formatDateTime(dateString) {
        if (dateString === 'Nunca') return 'Nunca';
        const date = new Date(dateString);
        return date.toLocaleString('pt-BR');
    }
}

// Funções globais
function showAddUserModal() {
    userManager.showAddUserModal();
}

function saveUser() {
    userManager.saveUser();
}

function clearFilters() {
    userManager.clearFilters();
}

function exportUsers() {
    userManager.exportUsers();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.userManager = new UserManager();
});
