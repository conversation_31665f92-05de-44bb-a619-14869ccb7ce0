# 🚀 EXECUTAR SETUP DO SUPABASE - PASSO A PASSO

## ✅ PASSO 1: CRIAR PROJETO NO SUPABASE

### 1.1 Acessar Supabase
- ✅ **J<PERSON> aberto no navegador:** https://supabase.com
- **Login:** <EMAIL>
- **<PERSON>ha:** Ra5izen2kim#

### 1.2 Criar Novo Projeto
1. Clique em **"New Project"**
2. Preencha os dados:
   ```
   Nome: gestao-frotas
   Organização: (sua organização padrão)
   Região: South America (São Paulo)
   Senha do Banco: Ra5izen2kim#
   ```
3. Clique em **"Create new project"**
4. **Aguarde 2-3 minutos** para o projeto ser criado

---

## ✅ PASSO 2: EXECUTAR SCRIPT SQL

### 2.1 Acessar SQL Editor
1. No painel do Supabase, clique em **"SQL Editor"** (menu lateral)
2. Clique em **"New query"**

### 2.2 Executar Primeiro Script (Tabelas)
1. **Copie TODO o conteúdo** do arquivo: `supabase-complete-setup.sql`
2. **Cole no SQL Editor**
3. Clique em **"Run"** (ou Ctrl+Enter)
4. ✅ **Aguarde confirmação:** "Success. No rows returned"

### 2.3 Executar Segundo Script (Políticas RLS)
1. **Limpe o editor** (Ctrl+A, Delete)
2. **Copie TODO o conteúdo** do arquivo: `supabase-rls-policies.sql`
3. **Cole no SQL Editor**
4. Clique em **"Run"** (ou Ctrl+Enter)
5. ✅ **Aguarde confirmação:** "Setup completed successfully!"

---

## ✅ PASSO 3: OBTER CREDENCIAIS

### 3.1 Acessar Configurações
1. No painel do Supabase, clique em **"Settings"** (menu lateral)
2. Clique em **"API"**

### 3.2 Copiar Credenciais
**Anote estas informações:**
```
URL do Projeto: https://[seu-projeto].supabase.co
Chave Anônima (anon key): eyJ... (chave longa)
Chave de Serviço (service_role key): eyJ... (chave longa)
```

---

## ✅ PASSO 4: CONFIGURAR VARIÁVEIS DE AMBIENTE

### 4.1 Backend (.env)
1. Vá para a pasta: `fleet-management-v2/server/`
2. Copie o arquivo: `cp .env.example .env`
3. Edite o arquivo `.env` com suas credenciais:
```env
SUPABASE_URL=https://[seu-projeto].supabase.co
SUPABASE_ANON_KEY=sua_chave_anonima
SUPABASE_SERVICE_KEY=sua_chave_de_servico
NODE_ENV=development
PORT=5000
CORS_ORIGIN=http://localhost:3000
```

### 4.2 Frontend (.env)
1. Vá para a pasta: `fleet-management-v2/client/`
2. Copie o arquivo: `cp .env.example .env`
3. Edite o arquivo `.env` com suas credenciais:
```env
REACT_APP_SUPABASE_URL=https://[seu-projeto].supabase.co
REACT_APP_SUPABASE_ANON_KEY=sua_chave_anonima
REACT_APP_API_URL=http://localhost:5000/api
```

---

## ✅ PASSO 5: INSTALAR E EXECUTAR

### 5.1 Instalar Dependências
```bash
cd fleet-management-v2
npm run install:all
```

### 5.2 Executar Aplicação
```bash
npm run dev
```

**Resultado esperado:**
```
🚀 Servidor rodando na porta 5000
🌍 Ambiente: development
📊 Health check: http://localhost:5000/health

Local:   http://localhost:3000
Network: http://192.168.x.x:3000
```

---

## ✅ PASSO 6: TESTAR SISTEMA

### 6.1 Acessar Frontend
- **URL:** http://localhost:3000
- **Primeira tela:** Página de registro

### 6.2 Criar Primeira Conta (Admin)
1. Clique em **"Registrar"**
2. Preencha os dados:
   ```
   Nome: Seu Nome
   Email: <EMAIL>
   Senha: suasenha123
   ```
3. ✅ **Primeira conta será ADMIN automaticamente**

### 6.3 Verificar Backend
- **Health Check:** http://localhost:5000/health
- **Deve retornar:** `{"status": "OK", ...}`

---

## 🔧 SOLUÇÃO DE PROBLEMAS

### ❌ Erro: "Variáveis de ambiente não configuradas"
- ✅ **Solução:** Verifique se os arquivos `.env` foram criados e preenchidos

### ❌ Erro: "Conexão com Supabase falhou"
- ✅ **Solução:** Verifique se as credenciais estão corretas
- ✅ **Solução:** Confirme se o projeto Supabase está ativo

### ❌ Erro: "Tabelas não encontradas"
- ✅ **Solução:** Execute novamente os scripts SQL no Supabase

### ❌ Erro de CORS
- ✅ **Solução:** Verifique se `CORS_ORIGIN=http://localhost:3000` no backend

---

## 📞 PRÓXIMOS PASSOS

Após completar este setup:
1. ✅ **Sistema estará funcionando** com autenticação
2. ✅ **Banco PostgreSQL** configurado no Supabase
3. ✅ **API REST** funcionando no backend
4. ✅ **Interface React** funcionando no frontend

**Pronto para continuar o desenvolvimento!** 🎉
