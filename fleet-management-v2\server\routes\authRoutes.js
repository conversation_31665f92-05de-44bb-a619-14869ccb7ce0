const express = require('express');
const { supabase, supabaseAdmin } = require('../config/supabase');
const { authenticate, optionalAuth } = require('../middleware/authMiddleware');
const { asyncHandler, validateRequest } = require('../middleware/errorMiddleware');
const Joi = require('joi');

const router = express.Router();

// Schemas de validação
const registerSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Email deve ter um formato válido',
    'any.required': 'Email é obrigatório'
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Senha deve ter pelo menos 6 caracteres',
    'any.required': 'Senha é obrigatória'
  }),
  fullName: Joi.string().min(2).required().messages({
    'string.min': 'Nome deve ter pelo menos 2 caracteres',
    'any.required': 'Nome completo é obrigatório'
  }),
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required(),
});

const resetPasswordSchema = Joi.object({
  email: Joi.string().email().required(),
});

const updatePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(6).required(),
});

// @desc    Registrar novo usuário
// @route   POST /api/auth/register
// @access  Public
router.post('/register', validateRequest(registerSchema), asyncHandler(async (req, res) => {
  const { email, password, fullName } = req.body;

  // Verificar se já existe um admin
  const { data: adminExists } = await supabaseAdmin
    .from('profiles')
    .select('id')
    .eq('role', 'admin')
    .limit(1);

  const role = adminExists && adminExists.length > 0 ? 'user' : 'admin';

  // Criar usuário no Supabase Auth
  const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
    email,
    password,
    email_confirm: true,
  });

  if (authError) {
    return res.status(400).json({
      error: authError.message,
      code: 'REGISTRATION_FAILED'
    });
  }

  // Criar perfil do usuário
  const { data: profile, error: profileError } = await supabaseAdmin
    .from('profiles')
    .insert([
      {
        id: authData.user.id,
        email,
        full_name: fullName,
        role,
      }
    ])
    .select()
    .single();

  if (profileError) {
    // Se falhar ao criar perfil, remover usuário criado
    await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
    
    return res.status(500).json({
      error: 'Erro ao criar perfil do usuário',
      code: 'PROFILE_CREATION_FAILED'
    });
  }

  res.status(201).json({
    message: 'Usuário registrado com sucesso',
    user: {
      id: authData.user.id,
      email: authData.user.email,
      fullName: profile.full_name,
      role: profile.role,
    },
  });
}));

// @desc    Login do usuário
// @route   POST /api/auth/login
// @access  Public
router.post('/login', validateRequest(loginSchema), asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return res.status(401).json({
      error: 'Credenciais inválidas',
      code: 'INVALID_CREDENTIALS'
    });
  }

  // Buscar perfil do usuário
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', data.user.id)
    .single();

  if (profileError) {
    return res.status(500).json({
      error: 'Erro ao buscar perfil do usuário',
      code: 'PROFILE_FETCH_ERROR'
    });
  }

  res.json({
    message: 'Login realizado com sucesso',
    user: {
      id: data.user.id,
      email: data.user.email,
      fullName: profile.full_name,
      role: profile.role,
    },
    session: {
      access_token: data.session.access_token,
      refresh_token: data.session.refresh_token,
      expires_at: data.session.expires_at,
    },
  });
}));

// @desc    Logout do usuário
// @route   POST /api/auth/logout
// @access  Private
router.post('/logout', authenticate, asyncHandler(async (req, res) => {
  const { error } = await supabase.auth.signOut();

  if (error) {
    return res.status(500).json({
      error: 'Erro ao fazer logout',
      code: 'LOGOUT_ERROR'
    });
  }

  res.json({
    message: 'Logout realizado com sucesso'
  });
}));

// @desc    Obter perfil do usuário atual
// @route   GET /api/auth/profile
// @access  Private
router.get('/profile', authenticate, asyncHandler(async (req, res) => {
  res.json({
    user: {
      id: req.user.id,
      email: req.user.email,
      fullName: req.profile.full_name,
      role: req.profile.role,
      createdAt: req.profile.created_at,
      updatedAt: req.profile.updated_at,
    }
  });
}));

// @desc    Atualizar perfil do usuário
// @route   PUT /api/auth/profile
// @access  Private
router.put('/profile', authenticate, asyncHandler(async (req, res) => {
  const { fullName } = req.body;

  const { data, error } = await supabase
    .from('profiles')
    .update({ full_name: fullName })
    .eq('id', req.user.id)
    .select()
    .single();

  if (error) {
    return res.status(500).json({
      error: 'Erro ao atualizar perfil',
      code: 'PROFILE_UPDATE_ERROR'
    });
  }

  res.json({
    message: 'Perfil atualizado com sucesso',
    user: {
      id: data.id,
      email: data.email,
      fullName: data.full_name,
      role: data.role,
    }
  });
}));

// @desc    Solicitar reset de senha
// @route   POST /api/auth/reset-password
// @access  Public
router.post('/reset-password', validateRequest(resetPasswordSchema), asyncHandler(async (req, res) => {
  const { email } = req.body;

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.CORS_ORIGIN}/reset-password`,
  });

  if (error) {
    return res.status(500).json({
      error: 'Erro ao enviar email de reset',
      code: 'RESET_EMAIL_ERROR'
    });
  }

  res.json({
    message: 'Email de reset enviado com sucesso'
  });
}));

// @desc    Verificar se usuário está autenticado
// @route   GET /api/auth/verify
// @access  Private
router.get('/verify', authenticate, asyncHandler(async (req, res) => {
  res.json({
    authenticated: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      fullName: req.profile.full_name,
      role: req.profile.role,
    }
  });
}));

module.exports = router;
