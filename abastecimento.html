<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Abastecimento - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html" class="active"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="caixa.html"><i class="fas fa-wallet"></i> Controle de Caixa</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Abastecimento</h1>
            </div>
            
            <div class="topbar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Abastecimento Content -->
        <div class="dashboard-content">
            <!-- Action Bar -->
            <div class="action-bar">
                <button class="btn btn-primary" onclick="openAbastecimentoModal()">
                    <i class="fas fa-plus"></i> Novo Abastecimento
                </button>
                
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Buscar por veículo, placa..." id="searchInput">
                    </div>
                    
                    <select id="filterPeriodo" class="form-select">
                        <option value="">Todos os períodos</option>
                        <option value="hoje">Hoje</option>
                        <option value="semana">Esta semana</option>
                        <option value="mes">Este mês</option>
                        <option value="trimestre">Este trimestre</option>
                    </select>
                    
                    <select id="filterVeiculo" class="form-select">
                        <option value="">Todos os veículos</option>
                    </select>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-gas-pump"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalAbastecimentos">156</h3>
                        <p>Total de Abastecimentos</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalLitros">2.450L</h3>
                        <p>Litros Consumidos</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalGasto">R$ 12.850</h3>
                        <p>Gasto Total</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="mediaConsumo">8.5 km/L</h3>
                        <p>Consumo Médio</p>
                    </div>
                </div>
            </div>

            <!-- Abastecimentos Table -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Histórico de Abastecimentos</h3>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-outline-success" onclick="recalcularConsumos()" title="Recalcular todos os consumos">
                            <i class="fas fa-calculator"></i> Recalcular Consumos
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="exportAbastecimentos()">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table" id="abastecimentosTable">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Veículo</th>
                                <th>Placa</th>
                                <th>Motorista</th>
                                <th>Posto</th>
                                <th>Litros</th>
                                <th>Valor</th>
                                <th>Km</th>
                                <th>Consumo</th>
                                <th>Cupom Fiscal</th>
                                <th>Nota Fiscal</th>
                                <th>Status/Data</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="abastecimentosTableBody">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-container">
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination" id="pagination">
                            <!-- Paginação será gerada via JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Novo Abastecimento -->
    <div class="modal fade" id="abastecimentoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Novo Abastecimento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <form id="abastecimentoForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="veiculo">Veículo *</label>
                                    <select id="veiculo" name="veiculo" class="form-select" required>
                                        <option value="">Selecione o veículo</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dataAbastecimento">Data do Abastecimento *</label>
                                    <input type="datetime-local" id="dataAbastecimento" name="dataAbastecimento" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="motorista">Motorista</label>
                                    <input type="text" id="motorista" name="motorista" class="form-control" placeholder="Nome do motorista">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="posto">Posto de Combustível *</label>
                                    <input type="text" id="posto" name="posto" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tipoCombustivel">Tipo de Combustível *</label>
                                    <select id="tipoCombustivel" name="tipoCombustivel" class="form-select" required>
                                        <option value="">Selecione</option>
                                        <option value="gasolina">Gasolina</option>
                                        <option value="etanol">Etanol</option>
                                        <option value="diesel">Diesel</option>
                                        <option value="gnv">GNV</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="cupomFiscal">Cupom Fiscal</label>
                                    <input type="text" id="cupomFiscal" name="cupomFiscal" class="form-control" placeholder="Número do cupom fiscal">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="litros">Litros *</label>
                                    <input type="number" id="litros" name="litros" class="form-control" step="0.01" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="valorLitro">Valor por Litro *</label>
                                    <input type="number" id="valorLitro" name="valorLitro" class="form-control" step="0.001" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="valorTotal">Valor Total</label>
                                    <input type="number" id="valorTotal" name="valorTotal" class="form-control" step="0.01" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notaFiscal">Nota Fiscal</label>
                                    <input type="text" id="notaFiscal" name="notaFiscal" class="form-control" placeholder="Número da nota fiscal">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="kmAtual">Quilometragem Atual *</label>
                                    <input type="number" id="kmAtual" name="kmAtual" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="consumo">Consumo (km/L)</label>
                                    <input type="number" id="consumo" name="consumo" class="form-control" step="0.1" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="observacoes">Observações</label>
                            <textarea id="observacoes" name="observacoes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar Abastecimento</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/abastecimento.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
