<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Veículos - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li class="has-submenu open">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu open">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html" class="active"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                        <li><a href="configuracoes.html"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Gerenciamento de Veículos</h1>
            </div>
            
            <div class="top-bar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            <div class="content-header">
                <div class="content-title">
                    <h2><i class="fas fa-car"></i> Frota de Veículos</h2>
                    <p>Gerencie informações dos veículos da frota</p>
                </div>
                <div class="content-actions">
                    <button class="btn btn-primary" onclick="showAddVehicleModal()">
                        <i class="fas fa-plus"></i> Novo Veículo
                    </button>
                </div>
            </div>

            <!-- Estatísticas Rápidas -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalVehicles">0</h3>
                            <p>Total de Veículos</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeVehicles">0</h3>
                            <p>Ativos</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="maintenanceVehicles">0</h3>
                            <p>Em Manutenção</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-danger">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="inactiveVehicles">0</h3>
                            <p>Inativos</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters-container">
                <div class="row">
                    <div class="col-md-3">
                        <label>Buscar:</label>
                        <input type="text" class="form-control" id="searchVehicle" placeholder="Placa, modelo...">
                    </div>
                    <div class="col-md-2">
                        <label>Tipo:</label>
                        <select class="form-control" id="filterType">
                            <option value="">Todos</option>
                            <option value="carro">Carro</option>
                            <option value="caminhao">Caminhão</option>
                            <option value="van">Van</option>
                            <option value="moto">Moto</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label>Status:</label>
                        <select class="form-control" id="filterStatus">
                            <option value="">Todos</option>
                            <option value="ativo">Ativo</option>
                            <option value="manutencao">Manutenção</option>
                            <option value="inativo">Inativo</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label>Ano:</label>
                        <select class="form-control" id="filterYear">
                            <option value="">Todos</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>&nbsp;</label>
                        <button class="btn btn-secondary d-block" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Limpar Filtros
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tabela de Veículos -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Lista de Veículos</h3>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="exportVehicles()">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>Placa</th>
                                <th>Modelo</th>
                                <th>Tipo</th>
                                <th>Ano</th>
                                <th>Quilometragem</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="vehiclesTableBody">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Adicionar/Editar Veículo -->
    <div class="modal fade" id="vehicleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="vehicleModalTitle">Novo Veículo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="vehicleForm">
                        <input type="hidden" id="vehicleId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehiclePlate" class="form-label">Placa *</label>
                                    <input type="text" class="form-control" id="vehiclePlate" required maxlength="8">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicleModel" class="form-label">Modelo *</label>
                                    <input type="text" class="form-control" id="vehicleModel" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="vehicleBrand" class="form-label">Marca *</label>
                                    <input type="text" class="form-control" id="vehicleBrand" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="vehicleType" class="form-label">Tipo *</label>
                                    <select class="form-control" id="vehicleType" required>
                                        <option value="">Selecione...</option>
                                        <option value="carro">Carro</option>
                                        <option value="caminhao">Caminhão</option>
                                        <option value="van">Van</option>
                                        <option value="moto">Moto</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="vehicleYear" class="form-label">Ano *</label>
                                    <input type="number" class="form-control" id="vehicleYear" required min="1990" max="2030">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicleKm" class="form-label">Quilometragem Atual</label>
                                    <input type="number" class="form-control" id="vehicleKm" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicleStatus" class="form-label">Status</label>
                                    <select class="form-control" id="vehicleStatus">
                                        <option value="ativo">Ativo</option>
                                        <option value="manutencao">Em Manutenção</option>
                                        <option value="inativo">Inativo</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicleFuel" class="form-label">Tipo de Combustível</label>
                                    <select class="form-control" id="vehicleFuel">
                                        <option value="gasolina">Gasolina</option>
                                        <option value="etanol">Etanol</option>
                                        <option value="diesel">Diesel</option>
                                        <option value="flex">Flex</option>
                                        <option value="gnv">GNV</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicleColor" class="form-label">Cor</label>
                                    <input type="text" class="form-control" id="vehicleColor">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="vehicleNotes" class="form-label">Observações</label>
                            <textarea class="form-control" id="vehicleNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="saveVehicle()">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/veiculos.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
