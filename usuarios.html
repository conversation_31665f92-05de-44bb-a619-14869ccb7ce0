<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Usuários - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <link href="css/font-size-adjustments.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="listview.html"><i class="fas fa-list"></i> Listview</a></li>
                <li class="has-submenu open">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu open">
                        <li><a href="usuarios.html" class="active"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                        <li><a href="configuracoes.html"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Gerenciamento de Usuários</h1>
            </div>
            
            <div class="top-bar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            <div class="content-header">
                <div class="content-title">
                    <h2><i class="fas fa-users"></i> Usuários do Sistema</h2>
                    <p>Gerencie usuários, permissões e níveis de acesso</p>
                </div>
                <div class="content-actions">
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class="fas fa-plus"></i> Novo Usuário
                    </button>
                    <button class="btn btn-info ms-2" onclick="testSystem()">
                        <i class="fas fa-bug"></i> Teste Sistema
                    </button>
                    <button class="btn btn-warning ms-2" onclick="forceOpenModal()">
                        <i class="fas fa-external-link-alt"></i> Forçar Modal
                    </button>
                </div>
            </div>

            <!-- Filtros -->
            <div class="filters-container">
                <div class="row">
                    <div class="col-md-3">
                        <label>Buscar:</label>
                        <input type="text" class="form-control" id="searchUser" placeholder="Nome ou email...">
                    </div>
                    <div class="col-md-3">
                        <label>Nível de Acesso:</label>
                        <select class="form-control" id="filterRole">
                            <option value="">Todos</option>
                            <option value="admin">Administrador</option>
                            <option value="supervisor">Supervisor</option>
                            <option value="operador">Operador</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>Status:</label>
                        <select class="form-control" id="filterStatus">
                            <option value="">Todos</option>
                            <option value="ativo">Ativo</option>
                            <option value="inativo">Inativo</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>&nbsp;</label>
                        <button class="btn btn-secondary d-block" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Limpar Filtros
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tabela de Usuários -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Lista de Usuários</h3>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="exportUsers()">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>Nível de Acesso</th>
                                <th>Status</th>
                                <th>Último Acesso</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Adicionar/Editar Usuário -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">Novo Usuário</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userName" class="form-label">Nome Completo *</label>
                                    <input type="text" class="form-control" id="userName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userEmail" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="userEmail" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userRole" class="form-label">Nível de Acesso *</label>
                                    <select class="form-control" id="userRole" required>
                                        <option value="">Selecione...</option>
                                        <option value="admin">Administrador</option>
                                        <option value="supervisor">Supervisor</option>
                                        <option value="operador">Operador</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userStatus" class="form-label">Status</label>
                                    <select class="form-control" id="userStatus">
                                        <option value="ativo">Ativo</option>
                                        <option value="inativo">Inativo</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userPassword" class="form-label">Senha *</label>
                                    <input type="password" class="form-control" id="userPassword">
                                    <small class="text-muted">Deixe em branco para manter a senha atual</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userConfirmPassword" class="form-label">Confirmar Senha</label>
                                    <input type="password" class="form-control" id="userConfirmPassword">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/usuarios.js"></script>
    <script>
        // Verificar se todos os scripts foram carregados
        console.log('🔄 Verificando dependências...');
        console.log('Bootstrap disponível:', typeof bootstrap !== 'undefined');
        console.log('Auth disponível:', typeof auth !== 'undefined');

        // Aguardar um pouco para garantir que tudo foi carregado
        setTimeout(() => {
            if (typeof userManager !== 'undefined' && userManager) {
                console.log('✅ Sistema de usuários pronto!');
            } else {
                console.warn('⚠️ Sistema de usuários ainda não está pronto');
            }
        }, 100);

        // Função de teste do sistema
        function testSystem() {
            console.log('🧪 === TESTE DO SISTEMA ===');
            console.log('Bootstrap disponível:', typeof bootstrap !== 'undefined');
            console.log('userManager disponível:', typeof userManager !== 'undefined');
            console.log('userManager instância:', userManager);

            if (userManager) {
                console.log('Usuários carregados:', userManager.users.length);
                console.log('Lista de usuários:', userManager.users);
            }

            // Testar elementos DOM
            const tbody = document.getElementById('usersTableBody');
            console.log('Elemento usersTableBody encontrado:', !!tbody);

            const modal = document.getElementById('userModal');
            console.log('Elemento userModal encontrado:', !!modal);

            alert('Teste concluído! Verifique o console para detalhes.');
        }

        // Função para forçar abertura do modal
        function forceOpenModal() {
            console.log('🔧 Forçando abertura do modal...');

            const modal = document.getElementById('userModal');
            if (modal) {
                // Limpar formulário
                document.getElementById('userForm').reset();
                document.getElementById('userModalTitle').textContent = 'Novo Usuário (Forçado)';

                // Tentar diferentes métodos
                console.log('Tentativa 1: Bootstrap 5...');
                try {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                    console.log('✅ Sucesso com Bootstrap 5!');
                    return;
                } catch (e) {
                    console.warn('❌ Bootstrap 5 falhou:', e);
                }

                console.log('Tentativa 2: jQuery...');
                if (typeof $ !== 'undefined') {
                    try {
                        $(modal).modal('show');
                        console.log('✅ Sucesso com jQuery!');
                        return;
                    } catch (e) {
                        console.warn('❌ jQuery falhou:', e);
                    }
                }

                console.log('Tentativa 3: Manual...');
                modal.style.display = 'block';
                modal.classList.add('show');
                modal.setAttribute('aria-hidden', 'false');
                document.body.classList.add('modal-open');

                // Criar backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'manual-backdrop';
                document.body.appendChild(backdrop);

                console.log('✅ Modal aberto manualmente!');
            } else {
                console.error('❌ Modal não encontrado!');
                alert('Modal não encontrado no DOM!');
            }
        }
    </script>
</body>
</html>
