<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpeza Completa de Dados</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(238, 90, 82, 0.4);
        }
        .btn-success {
            background: linear-gradient(45deg, #51cf66, #40c057);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(64, 192, 87, 0.4);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success { background-color: rgba(72, 187, 120, 0.2); }
        .log-warning { background-color: rgba(237, 137, 54, 0.2); }
        .log-error { background-color: rgba(245, 101, 101, 0.2); }
        .log-info { background-color: rgba(66, 153, 225, 0.2); }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-danger text-white text-center">
                        <h3><i class="fas fa-trash-alt me-2"></i>Limpeza Completa de Dados</h3>
                        <p class="mb-0">Remove todos os dados padrão e de teste do sistema</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>ATENÇÃO:</strong> Esta ação irá remover TODOS os dados do sistema, incluindo:
                            <ul class="mt-2 mb-0">
                                <li>Todos os veículos cadastrados</li>
                                <li>Histórico de abastecimentos</li>
                                <li>Registros de manutenção</li>
                                <li>Dados de revisões</li>
                                <li>Relatórios salvos</li>
                                <li>Alertas criados</li>
                                <li>Usuários (exceto admin)</li>
                            </ul>
                        </div>

                        <div class="text-center mb-4">
                            <button id="btnLimpar" class="btn btn-danger btn-lg me-3">
                                <i class="fas fa-trash-alt me-2"></i>Limpar Todos os Dados
                            </button>
                            <button id="btnVerificar" class="btn btn-success btn-lg">
                                <i class="fas fa-search me-2"></i>Verificar Dados
                            </button>
                        </div>

                        <div id="resultado" style="display: none;">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Limpeza concluída com sucesso!</strong>
                                <div id="detalhesResultado" class="mt-2"></div>
                            </div>
                        </div>

                        <div id="dadosExistentes" style="display: none;">
                            <h5><i class="fas fa-database me-2"></i>Dados Encontrados no Sistema:</h5>
                            <div id="listaDados"></div>
                        </div>

                        <div id="logContainer" class="log-container" style="display: none;">
                            <h6><i class="fas fa-terminal me-2"></i>Log de Operações:</h6>
                            <div id="logContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logContent = document.getElementById('logContent');
            
            logContainer.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function limparTodosDados() {
            log('🚀 Iniciando limpeza completa de dados...', 'info');
            
            const dataTypes = [
                'veiculos',
                'abastecimentos', 
                'abastecimento',
                'manutencoes',
                'manutencao',
                'revisoes',
                'revisao',
                'lavagens',
                'lavagem',
                'alertas',
                'usuarios',
                'relatorios',
                'reports'
            ];

            let removedCount = 0;
            let totalItems = 0;

            dataTypes.forEach(type => {
                const data = localStorage.getItem(type);
                if (data) {
                    try {
                        const parsedData = JSON.parse(data);
                        const itemCount = Array.isArray(parsedData) ? parsedData.length : 1;
                        totalItems += itemCount;
                        
                        localStorage.removeItem(type);
                        removedCount++;
                        log(`✅ Removido: ${type} (${itemCount} itens)`, 'success');
                    } catch (error) {
                        log(`❌ Erro ao processar ${type}: ${error.message}`, 'error');
                    }
                } else {
                    log(`ℹ️ ${type}: Nenhum dado encontrado`, 'info');
                }
            });

            // Limpar também dados específicos que podem existir
            const specificKeys = [
                'currentUser',
                'authToken',
                'systemConfig',
                'dashboardData',
                'chartData'
            ];

            specificKeys.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    removedCount++;
                    log(`✅ Removido: ${key}`, 'success');
                }
            });

            log(`🎉 Limpeza concluída! ${removedCount} tipos de dados removidos, ${totalItems} itens totais`, 'success');

            // Mostrar resultado
            document.getElementById('resultado').style.display = 'block';
            document.getElementById('detalhesResultado').innerHTML = `
                <small>
                    • ${removedCount} tipos de dados removidos<br>
                    • ${totalItems} itens totais removidos<br>
                    • Sistema completamente limpo<br>
                    • Agora você pode usar apenas dados inseridos manualmente
                </small>
            `;

            // Verificar se realmente foi limpo
            setTimeout(() => {
                verificarDados();
            }, 1000);
        }

        function verificarDados() {
            log('🔍 Verificando dados existentes no sistema...', 'info');
            
            const dataTypes = [
                'veiculos', 'abastecimentos', 'abastecimento', 'manutencoes', 'manutencao',
                'revisoes', 'revisao', 'lavagens', 'lavagem', 'alertas', 'usuarios', 'relatorios'
            ];

            let dadosHtml = '<div class="row">';
            let totalItems = 0;
            let hasData = false;

            dataTypes.forEach((type, index) => {
                const data = localStorage.getItem(type);
                let itemCount = 0;
                let status = 'empty';

                if (data) {
                    try {
                        const parsedData = JSON.parse(data);
                        itemCount = Array.isArray(parsedData) ? parsedData.length : 1;
                        totalItems += itemCount;
                        status = itemCount > 0 ? 'has-data' : 'empty';
                        if (itemCount > 0) hasData = true;
                    } catch (error) {
                        status = 'error';
                        log(`❌ Erro ao verificar ${type}: ${error.message}`, 'error');
                    }
                }

                const badgeClass = status === 'has-data' ? 'bg-warning' : 
                                 status === 'error' ? 'bg-danger' : 'bg-success';
                const icon = status === 'has-data' ? 'fas fa-exclamation-triangle' : 
                            status === 'error' ? 'fas fa-times' : 'fas fa-check';

                dadosHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                            <span><i class="${icon} me-2"></i>${type}</span>
                            <span class="badge ${badgeClass}">${itemCount} itens</span>
                        </div>
                    </div>
                `;

                log(`📊 ${type}: ${itemCount} itens`, itemCount > 0 ? 'warning' : 'success');
            });

            dadosHtml += '</div>';

            if (!hasData) {
                dadosHtml = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Sistema completamente limpo!</strong><br>
                        Nenhum dado padrão encontrado. O sistema está pronto para receber apenas dados inseridos manualmente.
                    </div>
                `;
                log('🎉 Sistema completamente limpo! Nenhum dado padrão encontrado.', 'success');
            } else {
                log(`⚠️ Ainda existem ${totalItems} itens no sistema`, 'warning');
            }

            document.getElementById('listaDados').innerHTML = dadosHtml;
            document.getElementById('dadosExistentes').style.display = 'block';
        }

        // Event listeners
        document.getElementById('btnLimpar').addEventListener('click', function() {
            if (confirm('ATENÇÃO: Esta ação irá remover TODOS os dados do sistema. Tem certeza que deseja continuar?')) {
                if (confirm('CONFIRMAÇÃO FINAL: Todos os dados serão perdidos permanentemente. Continuar?')) {
                    limparTodosDados();
                }
            }
        });

        document.getElementById('btnVerificar').addEventListener('click', verificarDados);

        // Verificar dados automaticamente quando a página carregar
        window.addEventListener('load', function() {
            log('🚀 Página carregada, verificando estado atual do sistema...', 'info');
            verificarDados();
        });
    </script>
</body>
</html>
