/**
 * Sistema de Gerenciamento de Veículos com FileStorage
 * Versão atualizada que usa o sistema de armazenamento baseado em arquivos
 */
class VehicleManagerFileStorage {
    constructor() {
        this.vehicles = [];
        this.currentVehicle = null;
        this.storageAdapter = null;
        this.initialized = false;
        this.init();
    }

    async init() {
        try {
            // Inicializar StorageAdapter
            this.storageAdapter = new StorageAdapter();
            await this.storageAdapter.waitForReady();
            
            // Carregar dados
            await this.loadVehicles();
            
            // Configurar interface
            this.loadVehiclesTable();
            this.setupEventListeners();
            this.setupFilters();
            this.updateStatistics();
            this.populateYearFilter();
            
            this.initialized = true;
            console.log('✅ VehicleManagerFileStorage inicializado');
        } catch (error) {
            console.error('❌ Erro ao inicializar VehicleManagerFileStorage:', error);
        }
    }

    // Carregar veículos do FileStorage
    async loadVehicles() {
        try {
            const data = await this.storageAdapter.getItemAsObject('frotas_vehicles');
            this.vehicles = data || [];
            console.log(`📖 ${this.vehicles.length} veículos carregados do FileStorage`);
            return this.vehicles;
        } catch (error) {
            console.error('❌ Erro ao carregar veículos:', error);
            this.vehicles = [];
            return [];
        }
    }

    // Salvar veículos no FileStorage
    async saveVehicles() {
        try {
            await this.storageAdapter.setItem('frotas_vehicles', this.vehicles);
            console.log(`💾 ${this.vehicles.length} veículos salvos no FileStorage`);
            return true;
        } catch (error) {
            console.error('❌ Erro ao salvar veículos:', error);
            return false;
        }
    }

    // Adicionar novo veículo
    async addVehicle(vehicleData) {
        try {
            const newVehicle = {
                id: Date.now(),
                ...vehicleData,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            this.vehicles.push(newVehicle);
            const success = await this.saveVehicles();
            
            if (success) {
                this.loadVehiclesTable();
                this.updateStatistics();
                console.log('✅ Veículo adicionado com sucesso');
                alert('Veículo adicionado com sucesso!');
                return newVehicle;
            } else {
                throw new Error('Falha ao salvar veículo');
            }
        } catch (error) {
            console.error('❌ Erro ao adicionar veículo:', error);
            alert('Erro ao adicionar veículo: ' + error.message);
            return null;
        }
    }

    // Atualizar veículo existente
    async updateVehicle(vehicleId, vehicleData) {
        try {
            const index = this.vehicles.findIndex(v => v.id === vehicleId);
            if (index === -1) {
                throw new Error('Veículo não encontrado');
            }

            this.vehicles[index] = {
                ...this.vehicles[index],
                ...vehicleData,
                updatedAt: new Date().toISOString()
            };

            const success = await this.saveVehicles();
            
            if (success) {
                this.loadVehiclesTable();
                this.updateStatistics();
                console.log('✅ Veículo atualizado com sucesso');
                alert('Veículo atualizado com sucesso!');
                return this.vehicles[index];
            } else {
                throw new Error('Falha ao salvar alterações');
            }
        } catch (error) {
            console.error('❌ Erro ao atualizar veículo:', error);
            alert('Erro ao atualizar veículo: ' + error.message);
            return null;
        }
    }

    // Remover veículo
    async removeVehicle(vehicleId) {
        try {
            const vehicle = this.vehicles.find(v => v.id === vehicleId);
            if (!vehicle) {
                throw new Error('Veículo não encontrado');
            }

            if (!confirm(`Tem certeza que deseja excluir o veículo ${vehicle.modelo} - ${vehicle.placa}?`)) {
                return false;
            }

            this.vehicles = this.vehicles.filter(v => v.id !== vehicleId);
            const success = await this.saveVehicles();
            
            if (success) {
                this.loadVehiclesTable();
                this.updateStatistics();
                console.log('✅ Veículo removido com sucesso');
                alert('Veículo removido com sucesso!');
                return true;
            } else {
                throw new Error('Falha ao salvar alterações');
            }
        } catch (error) {
            console.error('❌ Erro ao remover veículo:', error);
            alert('Erro ao remover veículo: ' + error.message);
            return false;
        }
    }

    // Carregar tabela de veículos
    loadVehiclesTable() {
        const tbody = document.getElementById('vehiclesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.vehicles.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="fas fa-car me-2"></i>
                        Nenhum veículo cadastrado. Clique em "Novo Veículo" para começar.
                    </td>
                </tr>
            `;
            return;
        }

        this.vehicles.forEach(vehicle => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <div class="vehicle-avatar me-2">
                            <i class="fas fa-car"></i>
                        </div>
                        <div>
                            <strong>${vehicle.modelo}</strong><br>
                            <small class="text-muted">${vehicle.marca}</small>
                        </div>
                    </div>
                </td>
                <td><span class="badge bg-primary">${vehicle.placa}</span></td>
                <td>${vehicle.ano}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(vehicle.status)}">
                        ${this.getStatusText(vehicle.status)}
                    </span>
                </td>
                <td>${vehicle.combustivel}</td>
                <td>${vehicle.km ? vehicle.km.toLocaleString() : 'N/A'} km</td>
                <td>
                    <small class="text-muted">
                        ${new Date(vehicle.createdAt).toLocaleDateString()}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="vehicleManager.editVehicle(${vehicle.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="vehicleManager.viewVehicle(${vehicle.id})" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="vehicleManager.removeVehicle(${vehicle.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de veículo
        const vehicleForm = document.getElementById('vehicleForm');
        if (vehicleForm) {
            vehicleForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Botão novo veículo
        const newVehicleBtn = document.getElementById('newVehicleBtn');
        if (newVehicleBtn) {
            newVehicleBtn.addEventListener('click', () => this.showVehicleModal());
        }

        // Filtros
        const searchInput = document.getElementById('searchVehicles');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.applyFilters());
        }

        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.applyFilters());
        }
    }

    // Manipular envio do formulário
    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const vehicleData = Object.fromEntries(formData);
        
        // Converter campos numéricos
        if (vehicleData.ano) vehicleData.ano = parseInt(vehicleData.ano);
        if (vehicleData.km) vehicleData.km = parseInt(vehicleData.km);
        
        const vehicleId = vehicleData.id ? parseInt(vehicleData.id) : null;
        
        if (vehicleId) {
            await this.updateVehicle(vehicleId, vehicleData);
        } else {
            await this.addVehicle(vehicleData);
        }
        
        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('vehicleModal'));
        if (modal) {
            modal.hide();
        }
        
        // Limpar formulário
        e.target.reset();
    }

    // Mostrar modal de veículo
    showVehicleModal(vehicle = null) {
        const modal = new bootstrap.Modal(document.getElementById('vehicleModal'));
        const form = document.getElementById('vehicleForm');
        
        if (vehicle) {
            // Editar veículo existente
            Object.keys(vehicle).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    input.value = vehicle[key];
                }
            });
            document.getElementById('vehicleModalLabel').textContent = 'Editar Veículo';
        } else {
            // Novo veículo
            form.reset();
            document.getElementById('vehicleModalLabel').textContent = 'Novo Veículo';
        }
        
        modal.show();
    }

    // Editar veículo
    editVehicle(vehicleId) {
        const vehicle = this.vehicles.find(v => v.id === vehicleId);
        if (vehicle) {
            this.showVehicleModal(vehicle);
        }
    }

    // Visualizar veículo
    viewVehicle(vehicleId) {
        const vehicle = this.vehicles.find(v => v.id === vehicleId);
        if (vehicle) {
            // Implementar modal de visualização
            console.log('Visualizar veículo:', vehicle);
        }
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchVehicles')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        
        let filtered = this.vehicles;
        
        if (searchTerm) {
            filtered = filtered.filter(vehicle => 
                vehicle.modelo.toLowerCase().includes(searchTerm) ||
                vehicle.marca.toLowerCase().includes(searchTerm) ||
                vehicle.placa.toLowerCase().includes(searchTerm)
            );
        }
        
        if (statusFilter) {
            filtered = filtered.filter(vehicle => vehicle.status === statusFilter);
        }
        
        // Atualizar tabela com dados filtrados
        this.displayFilteredVehicles(filtered);
    }

    // Exibir veículos filtrados
    displayFilteredVehicles(vehicles) {
        const tbody = document.getElementById('vehiclesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        vehicles.forEach(vehicle => {
            // Usar o mesmo código da loadVehiclesTable mas com os veículos filtrados
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <div class="vehicle-avatar me-2">
                            <i class="fas fa-car"></i>
                        </div>
                        <div>
                            <strong>${vehicle.modelo}</strong><br>
                            <small class="text-muted">${vehicle.marca}</small>
                        </div>
                    </div>
                </td>
                <td><span class="badge bg-primary">${vehicle.placa}</span></td>
                <td>${vehicle.ano}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(vehicle.status)}">
                        ${this.getStatusText(vehicle.status)}
                    </span>
                </td>
                <td>${vehicle.combustivel}</td>
                <td>${vehicle.km ? vehicle.km.toLocaleString() : 'N/A'} km</td>
                <td>
                    <small class="text-muted">
                        ${new Date(vehicle.createdAt).toLocaleDateString()}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="vehicleManager.editVehicle(${vehicle.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="vehicleManager.viewVehicle(${vehicle.id})" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="vehicleManager.removeVehicle(${vehicle.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Métodos auxiliares
    getStatusColor(status) {
        const colors = {
            'ativo': 'success',
            'manutencao': 'warning',
            'inativo': 'secondary',
            'vendido': 'danger'
        };
        return colors[status] || 'secondary';
    }

    getStatusText(status) {
        const texts = {
            'ativo': 'Ativo',
            'manutencao': 'Manutenção',
            'inativo': 'Inativo',
            'vendido': 'Vendido'
        };
        return texts[status] || status;
    }

    // Atualizar estatísticas
    updateStatistics() {
        const totalVehicles = this.vehicles.length;
        const activeVehicles = this.vehicles.filter(v => v.status === 'ativo').length;
        const maintenanceVehicles = this.vehicles.filter(v => v.status === 'manutencao').length;
        
        // Atualizar elementos da interface se existirem
        const totalElement = document.getElementById('totalVehicles');
        if (totalElement) totalElement.textContent = totalVehicles;
        
        const activeElement = document.getElementById('activeVehicles');
        if (activeElement) activeElement.textContent = activeVehicles;
        
        const maintenanceElement = document.getElementById('maintenanceVehicles');
        if (maintenanceElement) maintenanceElement.textContent = maintenanceVehicles;
    }

    // Configurar filtros
    setupFilters() {
        this.populateYearFilter();
    }

    // Popular filtro de anos
    populateYearFilter() {
        const yearFilter = document.getElementById('yearFilter');
        if (!yearFilter) return;
        
        const years = [...new Set(this.vehicles.map(v => v.ano))].sort((a, b) => b - a);
        
        yearFilter.innerHTML = '<option value="">Todos os anos</option>';
        years.forEach(year => {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            yearFilter.appendChild(option);
        });
    }
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se deve usar a versão com FileStorage
    if (typeof StorageAdapter !== 'undefined') {
        window.vehicleManager = new VehicleManagerFileStorage();
        console.log('✅ Usando VehicleManagerFileStorage');
    } else {
        console.log('⚠️ StorageAdapter não disponível, usando versão padrão');
    }
});
