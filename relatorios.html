<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <link href="css/font-size-adjustments.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="relatorios.html" class="active"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="listview.html"><i class="fas fa-list"></i> Listview</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Relatórios</h1>
            </div>
            
            <div class="top-bar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            <div class="content-header">
                <div class="content-title">
                    <h2><i class="fas fa-chart-bar"></i> Relatórios</h2>
                    <p>Gere relatórios detalhados da sua frota</p>
                </div>
                <div class="content-actions">
                    <button class="btn btn-primary" onclick="showCustomReportModal()">
                        <i class="fas fa-plus"></i> Relatório Personalizado
                    </button>
                </div>
            </div>

            <!-- Relatórios Rápidos -->
            <div class="quick-reports-section">
                <h3><i class="fas fa-bolt"></i> Relatórios Rápidos</h3>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="report-card" onclick="generateQuickReport('fuel')">
                            <div class="report-icon bg-primary">
                                <i class="fas fa-gas-pump"></i>
                            </div>
                            <div class="report-content">
                                <h4>Consumo de Combustível</h4>
                                <p>Relatório detalhado de abastecimentos</p>
                                <small class="text-muted">Últimos 30 dias</small>
                            </div>
                            <div class="report-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="report-card" onclick="generateQuickReport('maintenance')">
                            <div class="report-icon bg-warning">
                                <i class="fas fa-wrench"></i>
                            </div>
                            <div class="report-content">
                                <h4>Manutenções</h4>
                                <p>Histórico de manutenções e custos</p>
                                <small class="text-muted">Últimos 90 dias</small>
                            </div>
                            <div class="report-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="report-card" onclick="generateQuickReport('costs')">
                            <div class="report-icon bg-success">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="report-content">
                                <h4>Custos Operacionais</h4>
                                <p>Análise completa de custos</p>
                                <small class="text-muted">Mensal</small>
                            </div>
                            <div class="report-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="report-card" onclick="generateQuickReport('fleet')">
                            <div class="report-icon bg-info">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="report-content">
                                <h4>Status da Frota</h4>
                                <p>Situação atual dos veículos</p>
                                <small class="text-muted">Tempo real</small>
                            </div>
                            <div class="report-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="report-card" onclick="generateQuickReport('performance')">
                            <div class="report-icon bg-secondary">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="report-content">
                                <h4>Performance</h4>
                                <p>Indicadores de desempenho</p>
                                <small class="text-muted">Comparativo</small>
                            </div>
                            <div class="report-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="report-card" onclick="generateQuickReport('suppliers')">
                            <div class="report-icon bg-dark">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="report-content">
                                <h4>Fornecedores</h4>
                                <p>Análise de fornecedores</p>
                                <small class="text-muted">Ranking</small>
                            </div>
                            <div class="report-action">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Relatórios Agendados -->
            <div class="scheduled-reports-section">
                <div class="section-header">
                    <h3><i class="fas fa-clock"></i> Relatórios Agendados</h3>
                    <button class="btn btn-outline-primary btn-sm" onclick="showScheduleModal()">
                        <i class="fas fa-plus"></i> Agendar Relatório
                    </button>
                </div>
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Tipo</th>
                                    <th>Frequência</th>
                                    <th>Próxima Execução</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="scheduledReportsTable">
                                <tr>
                                    <td>Relatório Mensal de Combustível</td>
                                    <td><span class="badge badge-primary">Combustível</span></td>
                                    <td>Mensal</td>
                                    <td>01/08/2024</td>
                                    <td><span class="badge badge-success">Ativo</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Relatório Semanal de Manutenção</td>
                                    <td><span class="badge badge-warning">Manutenção</span></td>
                                    <td>Semanal</td>
                                    <td>05/07/2024</td>
                                    <td><span class="badge badge-success">Ativo</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="Excluir">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Histórico de Relatórios -->
            <div class="reports-history-section">
                <div class="section-header">
                    <h3><i class="fas fa-history"></i> Histórico de Relatórios</h3>
                    <div class="section-actions">
                        <input type="text" class="form-control form-control-sm" placeholder="Buscar relatórios..." id="searchReports">
                    </div>
                </div>
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Tipo</th>
                                    <th>Data de Geração</th>
                                    <th>Período</th>
                                    <th>Tamanho</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="reportsHistoryTable">
                                <!-- Dados serão carregados via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Relatório Personalizado -->
    <div class="modal fade" id="customReportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Relatório Personalizado</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="customReportForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reportName" class="form-label">Nome do Relatório *</label>
                                    <input type="text" class="form-control" id="reportName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reportType" class="form-label">Tipo *</label>
                                    <select class="form-control" id="reportType" required>
                                        <option value="">Selecione...</option>
                                        <option value="fuel">Combustível</option>
                                        <option value="maintenance">Manutenção</option>
                                        <option value="costs">Custos</option>
                                        <option value="fleet">Frota</option>
                                        <option value="performance">Performance</option>
                                        <option value="custom">Personalizado</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reportPeriodStart" class="form-label">Data Inicial *</label>
                                    <input type="date" class="form-control" id="reportPeriodStart" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reportPeriodEnd" class="form-label">Data Final *</label>
                                    <input type="date" class="form-control" id="reportPeriodEnd" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="reportVehicles" class="form-label">Veículos</label>
                            <select class="form-control" id="reportVehicles" multiple>
                                <option value="all">Todos os veículos</option>
                            </select>
                            <small class="text-muted">Mantenha Ctrl pressionado para selecionar múltiplos</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Campos a Incluir</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeBasicInfo" checked>
                                        <label class="form-check-label" for="includeBasicInfo">Informações Básicas</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeCosts">
                                        <label class="form-check-label" for="includeCosts">Custos Detalhados</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeCharts">
                                        <label class="form-check-label" for="includeCharts">Gráficos</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeComparison">
                                        <label class="form-check-label" for="includeComparison">Comparativos</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeRecommendations">
                                        <label class="form-check-label" for="includeRecommendations">Recomendações</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeDetails">
                                        <label class="form-check-label" for="includeDetails">Detalhes Técnicos</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="reportFormat" class="form-label">Formato de Saída</label>
                            <select class="form-control" id="reportFormat">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="csv">CSV</option>
                                <option value="html">HTML</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="generateCustomReport()">
                        <i class="fas fa-file-alt"></i> Gerar Relatório
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/relatorios.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
