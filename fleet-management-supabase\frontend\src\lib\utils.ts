import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('pt-BR').format(new Date(date))
}

export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat('pt-BR', {
    dateStyle: 'short',
    timeStyle: 'short'
  }).format(new Date(date))
}

export function formatKm(km: number): string {
  return new Intl.NumberFormat('pt-BR').format(km) + ' km'
}

export function formatLiters(liters: number): string {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 1,
    maximumFractionDigits: 3
  }).format(liters) + 'L'
}

export function formatConsumption(consumption: number): string {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 1,
    maximumFractionDigits: 2
  }).format(consumption) + ' km/L'
}

export function formatPlate(plate: string): string {
  // Formato ABC-1234 ou ABC1D23
  if (plate.length === 7) {
    return plate.substring(0, 3) + '-' + plate.substring(3)
  }
  return plate
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    ativo: 'success',
    inativo: 'secondary',
    manutencao: 'warning',
    vendido: 'danger',
    agendada: 'warning',
    em_andamento: 'primary',
    concluida: 'success',
    cancelada: 'danger',
    realizada: 'success'
  }
  
  return statusColors[status] || 'secondary'
}

export function getStatusLabel(status: string): string {
  const statusLabels: Record<string, string> = {
    ativo: 'Ativo',
    inativo: 'Inativo',
    manutencao: 'Em Manutenção',
    vendido: 'Vendido',
    agendada: 'Agendada',
    em_andamento: 'Em Andamento',
    concluida: 'Concluída',
    cancelada: 'Cancelada',
    realizada: 'Realizada'
  }
  
  return statusLabels[status] || status
}

export function getRoleLabel(role: string): string {
  const roleLabels: Record<string, string> = {
    admin: 'Administrador',
    supervisor: 'Supervisor',
    operador: 'Operador'
  }
  
  return roleLabels[role] || role
}

export function getVehicleTypeLabel(type: string): string {
  const typeLabels: Record<string, string> = {
    carro: 'Carro',
    moto: 'Moto',
    caminhao: 'Caminhão',
    van: 'Van'
  }
  
  return typeLabels[type] || type
}

export function getFuelTypeLabel(fuelType: string): string {
  const fuelLabels: Record<string, string> = {
    gasolina: 'Gasolina',
    etanol: 'Etanol',
    diesel: 'Diesel',
    flex: 'Flex',
    gnv: 'GNV'
  }
  
  return fuelLabels[fuelType] || fuelType
}
