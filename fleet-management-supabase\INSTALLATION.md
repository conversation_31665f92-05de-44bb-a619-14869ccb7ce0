# Guia de Instalação - Sistema de Gestão de Frotas

## 📋 Pré-requisitos

- Node.js 18+ instalado
- npm ou yarn
- Conta no Supabase
- Git (opcional)

## 🚀 Configuração do Supabase

### 1. Criar Projeto no Supabase

1. Acesse [supabase.com](https://supabase.com)
2. Faça login com: **<EMAIL>** / **Ra5izen2kim#**
3. Clique em "New Project"
4. Configure:
   - **Name**: Fleet Management System
   - **Database Password**: Crie uma senha segura
   - **Region**: South America (São Paulo)
5. Aguarde a criação do projeto (2-3 minutos)

### 2. Configurar Banco de Dados

1. No painel do Supabase, vá para **SQL Editor**
2. Copie e execute o conteúdo do arquivo `database/schema.sql`
3. Aguarde a execução completa

### 3. Configurar Autenticação

1. Vá para **Authentication > Settings**
2. Configure:
   - **Site URL**: `http://localhost:3000`
   - **Redirect URLs**: `http://localhost:3000/**`
3. Em **Auth Providers**, mantenha apenas **Email** habilitado

### 4. Obter Chaves da API

1. Vá para **Settings > API**
2. Copie:
   - **Project URL**
   - **anon public key**
   - **service_role key** (para o backend)

## 🔧 Instalação do Backend

### 1. Navegar para o diretório do backend

```bash
cd fleet-management-supabase/backend
```

### 2. Instalar dependências

```bash
npm install
```

### 3. Configurar variáveis de ambiente

```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:

```env
PORT=3001
NODE_ENV=development

# Configurações do Supabase
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua-anon-key
SUPABASE_SERVICE_ROLE_KEY=sua-service-role-key

# JWT Secret
JWT_SECRET=sua-chave-secreta-jwt

# CORS Origins
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### 4. Iniciar o servidor

```bash
npm run dev
```

O backend estará rodando em `http://localhost:3001`

## 🎨 Instalação do Frontend

### 1. Navegar para o diretório do frontend

```bash
cd fleet-management-supabase/frontend
```

### 2. Instalar dependências

```bash
npm install
```

### 3. Configurar variáveis de ambiente

```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:

```env
# Configurações do Supabase
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua-anon-key

# API Backend
VITE_API_URL=http://localhost:3001/api

# Configurações da aplicação
VITE_APP_NAME=Sistema de Gestão de Frotas
VITE_APP_VERSION=1.0.0
```

### 4. Iniciar o servidor de desenvolvimento

```bash
npm run dev
```

O frontend estará rodando em `http://localhost:3000`

## 👥 Criar Usuário Administrador

### Opção 1: Via Interface (Recomendado)

1. Acesse `http://localhost:3000`
2. Na tela de login, use as credenciais de demonstração ou crie um novo usuário
3. O primeiro usuário criado com role 'admin' será o administrador

### Opção 2: Via SQL (Supabase)

Execute no SQL Editor do Supabase:

```sql
-- Inserir usuário admin diretamente
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('admin123', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW()
);

-- Inserir dados do usuário na tabela users
INSERT INTO users (
  id,
  email,
  name,
  role,
  status
) VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  '<EMAIL>',
  'Administrador do Sistema',
  'admin',
  'ativo'
);
```

## 🧪 Dados de Teste

Para popular o sistema com dados de exemplo, execute:

```bash
cd database
# Execute os scripts de seed (se disponíveis)
```

## ✅ Verificação da Instalação

1. **Backend**: Acesse `http://localhost:3001/health`
   - Deve retornar status OK

2. **Frontend**: Acesse `http://localhost:3000`
   - Deve exibir a tela de login

3. **Banco de Dados**: No Supabase, verifique se as tabelas foram criadas

## 🔍 Solução de Problemas

### Erro de Conexão com Supabase

- Verifique se as URLs e chaves estão corretas
- Confirme se o projeto Supabase está ativo
- Verifique a configuração de CORS

### Erro de Dependências

```bash
# Limpar cache e reinstalar
rm -rf node_modules package-lock.json
npm install
```

### Erro de Porta em Uso

```bash
# Verificar processos na porta
lsof -i :3000
lsof -i :3001

# Matar processo se necessário
kill -9 PID
```

### Problemas de Autenticação

- Verifique se o usuário foi criado corretamente
- Confirme as configurações de Auth no Supabase
- Verifique os logs do console do navegador

## 📚 Próximos Passos

1. **Migração de Dados**: Se você tem dados do sistema antigo, use os scripts de migração
2. **Configuração de Produção**: Configure variáveis de ambiente para produção
3. **Deploy**: Siga o guia de deploy para colocar em produção
4. **Backup**: Configure backups automáticos no Supabase

## 🆘 Suporte

Se encontrar problemas durante a instalação:

1. Verifique os logs do console
2. Consulte a documentação do Supabase
3. Verifique se todas as dependências estão instaladas
4. Confirme se as versões do Node.js são compatíveis

---

**Instalação concluída com sucesso!** 🎉

Agora você pode acessar o sistema em `http://localhost:3000` e começar a usar o Sistema de Gestão de Frotas.
