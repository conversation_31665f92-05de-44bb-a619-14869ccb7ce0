-- Sistema de Gestão de Frotas - Schema PostgreSQL
-- Criado para Supabase

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON><PERSON> de usuários (integrada com Supabase Auth)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'supervisor', 'operador')),
    status VARCHAR(20) DEFAULT 'ativo' CHECK (status IN ('ativo', 'inativo')),
    last_access TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de veículos
CREATE TABLE vehicles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plate VARCHAR(10) UNIQUE NOT NULL,
    brand VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('carro', 'moto', 'caminhao', 'van')),
    year INTEGER NOT NULL,
    km INTEGER DEFAULT 0,
    fuel_type VARCHAR(50) NOT NULL CHECK (fuel_type IN ('gasolina', 'etanol', 'diesel', 'flex', 'gnv')),
    color VARCHAR(50),
    status VARCHAR(20) DEFAULT 'ativo' CHECK (status IN ('ativo', 'inativo', 'manutencao', 'vendido')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de registros de abastecimento
CREATE TABLE fuel_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vehicle_id UUID NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    driver VARCHAR(255),
    gas_station VARCHAR(255) NOT NULL,
    fuel_type VARCHAR(50) NOT NULL,
    liters DECIMAL(10,3) NOT NULL,
    price_per_liter DECIMAL(10,3) NOT NULL,
    total_value DECIMAL(10,2) NOT NULL,
    current_km INTEGER NOT NULL,
    previous_km INTEGER,
    consumption DECIMAL(10,2), -- km/L
    tax_coupon VARCHAR(255),
    invoice VARCHAR(255),
    tax_coupon_date DATE,
    invoice_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de registros de manutenção
CREATE TABLE maintenance_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vehicle_id UUID NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE,
    type VARCHAR(100) NOT NULL,
    problem TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'media' CHECK (priority IN ('baixa', 'media', 'alta', 'urgente')),
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    workshop VARCHAR(255) NOT NULL,
    contact VARCHAR(255),
    estimated_value DECIMAL(10,2),
    actual_value DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'agendada' CHECK (status IN ('agendada', 'em_andamento', 'concluida', 'cancelada')),
    completion_forecast TIMESTAMP WITH TIME ZONE,
    required_parts TEXT,
    notes TEXT,
    start_date TIMESTAMP WITH TIME ZONE,
    completion_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de registros de lavagem
CREATE TABLE washing_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vehicle_id UUID NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    location VARCHAR(255) NOT NULL,
    car_wash_name VARCHAR(255),
    car_wash_address TEXT,
    responsible VARCHAR(255) NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    current_km INTEGER,
    status VARCHAR(20) DEFAULT 'realizada' CHECK (status IN ('agendada', 'realizada', 'cancelada')),
    services TEXT[], -- Array de serviços realizados
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de registros de revisão
CREATE TABLE revision_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vehicle_id UUID NOT NULL REFERENCES vehicles(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('preventiva', 'periodica', 'obrigatoria')),
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    revision_km INTEGER,
    workshop VARCHAR(255) NOT NULL,
    contact VARCHAR(255),
    estimated_value DECIMAL(10,2),
    actual_value DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'agendada' CHECK (status IN ('agendada', 'em_andamento', 'concluida', 'cancelada')),
    revision_items TEXT,
    notes TEXT,
    completion_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de transações financeiras
CREATE TABLE financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('entrada', 'saida')),
    value DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    date DATE NOT NULL,
    origin VARCHAR(50) NOT NULL CHECK (origin IN ('manual', 'abastecimento', 'manutencao', 'lavagem', 'revisao')),
    reference_id UUID, -- ID do registro de origem (fuel_records, maintenance_records, etc)
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para melhor performance
CREATE INDEX idx_vehicles_plate ON vehicles(plate);
CREATE INDEX idx_vehicles_status ON vehicles(status);
CREATE INDEX idx_fuel_records_vehicle_date ON fuel_records(vehicle_id, date);
CREATE INDEX idx_fuel_records_date ON fuel_records(date);
CREATE INDEX idx_maintenance_records_vehicle_date ON maintenance_records(vehicle_id, scheduled_date);
CREATE INDEX idx_maintenance_records_status ON maintenance_records(status);
CREATE INDEX idx_washing_records_vehicle_date ON washing_records(vehicle_id, date);
CREATE INDEX idx_revision_records_vehicle_date ON revision_records(vehicle_id, scheduled_date);
CREATE INDEX idx_revision_records_status ON revision_records(status);
CREATE INDEX idx_financial_transactions_date ON financial_transactions(date);
CREATE INDEX idx_financial_transactions_origin ON financial_transactions(origin);

-- Triggers para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_fuel_records_updated_at BEFORE UPDATE ON fuel_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_washing_records_updated_at BEFORE UPDATE ON washing_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_revision_records_updated_at BEFORE UPDATE ON revision_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_financial_transactions_updated_at BEFORE UPDATE ON financial_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS (Row Level Security) - Configuração básica
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuel_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE washing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE revision_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_transactions ENABLE ROW LEVEL SECURITY;

-- Políticas básicas de RLS (serão refinadas conforme necessário)
CREATE POLICY "Users can view all data" ON users FOR SELECT USING (true);
CREATE POLICY "Users can view all vehicles" ON vehicles FOR SELECT USING (true);
CREATE POLICY "Users can view all fuel records" ON fuel_records FOR SELECT USING (true);
CREATE POLICY "Users can view all maintenance records" ON maintenance_records FOR SELECT USING (true);
CREATE POLICY "Users can view all washing records" ON washing_records FOR SELECT USING (true);
CREATE POLICY "Users can view all revision records" ON revision_records FOR SELECT USING (true);
CREATE POLICY "Users can view all financial transactions" ON financial_transactions FOR SELECT USING (true);
