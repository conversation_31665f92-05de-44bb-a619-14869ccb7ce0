import { createClient } from '@supabase/supabase-js';

// Configurações do Supabase
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Variáveis de ambiente do Supabase não configuradas. ' +
    'Certifique-se de definir REACT_APP_SUPABASE_URL e REACT_APP_SUPABASE_ANON_KEY'
  );
}

// Cliente Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  global: {
    headers: {
      'X-Client-Info': 'fleet-management-v2',
    },
  },
});

// Função para verificar conexão
export const testConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Erro na conexão com Supabase:', error.message);
      return false;
    }
    
    console.log('✅ Conexão com Supabase estabelecida');
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar conexão:', error.message);
    return false;
  }
};

// Função para obter URL de upload de arquivos
export const getFileUrl = (bucket, path) => {
  const { data } = supabase.storage.from(bucket).getPublicUrl(path);
  return data.publicUrl;
};

// Função para upload de arquivos
export const uploadFile = async (bucket, path, file, options = {}) => {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false,
        ...options,
      });

    if (error) {
      throw error;
    }

    return {
      path: data.path,
      fullPath: data.fullPath,
      url: getFileUrl(bucket, data.path),
    };
  } catch (error) {
    console.error('Erro no upload:', error);
    throw error;
  }
};

// Função para deletar arquivos
export const deleteFile = async (bucket, path) => {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Erro ao deletar arquivo:', error);
    throw error;
  }
};

// Configurações de tabelas
export const TABLES = {
  PROFILES: 'profiles',
  VEHICLES: 'vehicles',
  SUPPLIERS: 'suppliers',
  FUEL_RECORDS: 'fuel_records',
  MAINTENANCE_RECORDS: 'maintenance_records',
  WASHING_RECORDS: 'washing_records',
  REVISION_RECORDS: 'revision_records',
  CASH_FLOW: 'cash_flow',
};

// Configurações de storage
export const STORAGE_BUCKETS = {
  DOCUMENTS: 'documents',
  IMAGES: 'images',
};

// Função para formatar erros do Supabase
export const formatSupabaseError = (error) => {
  if (!error) return 'Erro desconhecido';

  // Erros comuns do PostgreSQL
  const errorMessages = {
    '23505': 'Este registro já existe',
    '23503': 'Referência inválida',
    '23502': 'Campo obrigatório não preenchido',
    'PGRST116': 'Registro não encontrado',
    'invalid_credentials': 'Email ou senha incorretos',
    'email_not_confirmed': 'Email não confirmado',
    'weak_password': 'Senha muito fraca',
    'signup_disabled': 'Cadastro desabilitado',
  };

  return errorMessages[error.code] || error.message || 'Erro desconhecido';
};

// Função para criar query builder com filtros comuns
export const createQuery = (table, userId = null) => {
  let query = supabase.from(table).select('*');
  
  if (userId) {
    query = query.eq('user_id', userId);
  }
  
  return query;
};

// Função para paginação
export const paginateQuery = (query, page = 1, limit = 10) => {
  const offset = (page - 1) * limit;
  return query.range(offset, offset + limit - 1);
};

// Função para ordenação
export const orderQuery = (query, column = 'created_at', ascending = false) => {
  return query.order(column, { ascending });
};

export default supabase;
