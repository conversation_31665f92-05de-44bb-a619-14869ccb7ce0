import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

import { useAuth } from './contexts/AuthContext';
import LoadingSpinner from './components/LoadingSpinner';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';

// Lazy loading dos componentes
const Login = React.lazy(() => import('./pages/Login'));
const Register = React.lazy(() => import('./pages/Register'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Vehicles = React.lazy(() => import('./pages/Vehicles'));
const Fuel = React.lazy(() => import('./pages/Fuel'));
const Maintenance = React.lazy(() => import('./pages/Maintenance'));
const Washing = React.lazy(() => import('./pages/Washing'));
const Revision = React.lazy(() => import('./pages/Revision'));
const Suppliers = React.lazy(() => import('./pages/Suppliers'));
const CashFlow = React.lazy(() => import('./pages/CashFlow'));
const Reports = React.lazy(() => import('./pages/Reports'));
const Profile = React.lazy(() => import('./pages/Profile'));
const Users = React.lazy(() => import('./pages/Users'));
const NotFound = React.lazy(() => import('./pages/NotFound'));

function App() {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <Helmet>
        <title>Sistema de Gestão de Frotas</title>
        <meta name="description" content="Sistema completo para gestão de frotas de veículos" />
      </Helmet>

      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          {/* Rotas públicas */}
          <Route 
            path="/login" 
            element={
              user ? <Navigate to="/dashboard" replace /> : <Login />
            } 
          />
          <Route 
            path="/register" 
            element={
              user ? <Navigate to="/dashboard" replace /> : <Register />
            } 
          />

          {/* Rotas protegidas */}
          <Route 
            path="/" 
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="vehicles" element={<Vehicles />} />
            <Route path="fuel" element={<Fuel />} />
            <Route path="maintenance" element={<Maintenance />} />
            <Route path="washing" element={<Washing />} />
            <Route path="revision" element={<Revision />} />
            <Route path="suppliers" element={<Suppliers />} />
            <Route path="cash-flow" element={<CashFlow />} />
            <Route path="reports" element={<Reports />} />
            <Route path="profile" element={<Profile />} />
            
            {/* Rotas administrativas */}
            <Route 
              path="users" 
              element={
                <ProtectedRoute requireAdmin>
                  <Users />
                </ProtectedRoute>
              } 
            />
          </Route>

          {/* Rota 404 */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </>
  );
}

export default App;
