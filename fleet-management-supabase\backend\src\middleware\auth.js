const { supabase } = require('../config/supabase');

// Middleware para verificar autenticação
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Token de acesso requerido'
      });
    }

    // Verificar token com Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({
        error: 'Token inválido ou expirado'
      });
    }

    // Buscar dados completos do usuário
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return res.status(401).json({
        error: 'Usu<PERSON>rio não encontrado'
      });
    }

    // Verificar se usuário está ativo
    if (userData.status !== 'ativo') {
      return res.status(403).json({
        error: 'Usuário inativo'
      });
    }

    // Adicionar dados do usuário à requisição
    req.user = {
      id: user.id,
      email: user.email,
      name: userData.name,
      role: userData.role,
      status: userData.status
    };

    next();
  } catch (error) {
    console.error('Erro na autenticação:', error);
    return res.status(500).json({
      error: 'Erro interno do servidor'
    });
  }
};

// Middleware para verificar permissões por role
const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Usuário não autenticado'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Permissão insuficiente'
      });
    }

    next();
  };
};

// Middleware para verificar se é admin
const requireAdmin = requireRole(['admin']);

// Middleware para verificar se é admin ou supervisor
const requireAdminOrSupervisor = requireRole(['admin', 'supervisor']);

module.exports = {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireAdminOrSupervisor
};
