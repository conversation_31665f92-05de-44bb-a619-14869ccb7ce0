import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'

// Páginas
import LoginPage from './pages/LoginPage'
import DashboardPage from './pages/DashboardPage'
import VehiclesPage from './pages/VehiclesPage'
import FuelPage from './pages/FuelPage'
import MaintenancePage from './pages/MaintenancePage'
import WashingPage from './pages/WashingPage'
import RevisionPage from './pages/RevisionPage'
import FinancialPage from './pages/FinancialPage'
import UsersPage from './pages/UsersPage'
import ProfilePage from './pages/ProfilePage'
import NotFoundPage from './pages/NotFoundPage'

// Layout
import Layout from './components/Layout/Layout'
import LoadingSpinner from './components/UI/LoadingSpinner'

function App() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Layout>
      <Routes>
        {/* Dashboard */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<DashboardPage />} />

        {/* Veículos */}
        <Route path="/vehicles" element={<VehiclesPage />} />

        {/* Abastecimento */}
        <Route path="/fuel" element={<FuelPage />} />

        {/* Manutenção */}
        <Route path="/maintenance" element={<MaintenancePage />} />

        {/* Lavagem */}
        <Route path="/washing" element={<WashingPage />} />

        {/* Revisão */}
        <Route path="/revision" element={<RevisionPage />} />

        {/* Financeiro */}
        <Route path="/financial" element={<FinancialPage />} />

        {/* Usuários (apenas admin) */}
        {user.role === 'admin' && (
          <Route path="/users" element={<UsersPage />} />
        )}

        {/* Perfil */}
        <Route path="/profile" element={<ProfilePage />} />

        {/* Login (redirecionar se já logado) */}
        <Route path="/login" element={<Navigate to="/dashboard" replace />} />

        {/* 404 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Layout>
  )
}

export default App
