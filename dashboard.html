<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-car-wash"></i> Lavagem</a></li>
                <li><a href="caixa.html"><i class="fas fa-wallet"></i> Controle de Caixa</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li><a href="listview.html"><i class="fas fa-list"></i> Listview</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Dashboard</h1>
            </div>
            
            <div class="topbar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Quick Actions Bar -->
            <div class="quick-actions-bar">
                <button class="quick-action-btn" onclick="openQuickAction('fuel')">
                    <i class="fas fa-gas-pump"></i>
                    <span>Abastecimento Rápido</span>
                </button>
                <button class="quick-action-btn" onclick="openQuickAction('maintenance')">
                    <i class="fas fa-wrench"></i>
                    <span>Registrar Manutenção</span>
                </button>
                <button class="quick-action-btn" onclick="openQuickAction('alert')">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Criar Alerta</span>
                </button>
                <button class="quick-action-btn" onclick="dashboard.refreshAllData()">
                    <i class="fas fa-sync-alt"></i>
                    <span>Atualizar Dados</span>
                </button>
                <button class="quick-action-btn admin-only" onclick="dashboard.showDeleteDataModal()"
                        style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); display: none;">
                    <i class="fas fa-trash-alt"></i>
                    <span>Deletar Dados</span>
                </button>
            </div>

            <!-- Enhanced Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card enhanced" data-trend="up">
                    <div class="stat-header">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+2</span>
                        </div>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalVeiculos">45</h3>
                        <p>Total de Veículos</p>
                        <small class="stat-subtitle">+2 este mês</small>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: 90%"></div>
                    </div>
                </div>

                <div class="stat-card enhanced" data-trend="stable">
                    <div class="stat-header">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-minus"></i>
                            <span>84%</span>
                        </div>
                    </div>
                    <div class="stat-info">
                        <h3 id="veiculosAtivos">38</h3>
                        <p>Veículos Ativos</p>
                        <small class="stat-subtitle">84% da frota</small>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar bg-success" style="width: 84%"></div>
                    </div>
                </div>

                <div class="stat-card enhanced" data-trend="down">
                    <div class="stat-header">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-down"></i>
                            <span>-1</span>
                        </div>
                    </div>
                    <div class="stat-info">
                        <h3 id="veiculosManutencao">5</h3>
                        <p>Em Manutenção</p>
                        <small class="stat-subtitle">-1 esta semana</small>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar bg-warning" style="width: 11%"></div>
                    </div>
                </div>

                <div class="stat-card enhanced" data-trend="critical">
                    <div class="stat-header">
                        <div class="stat-icon bg-danger">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-trend critical">
                            <i class="fas fa-exclamation"></i>
                            <span>CRÍTICO</span>
                        </div>
                    </div>
                    <div class="stat-info">
                        <h3 id="veiculosInativos">2</h3>
                        <p>Veículos Inativos</p>
                        <small class="stat-subtitle">Requer atenção</small>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar bg-danger" style="width: 4%"></div>
                    </div>
                </div>

                <!-- New KPI Cards -->
                <div class="stat-card enhanced" data-trend="up">
                    <div class="stat-header">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>+15%</span>
                        </div>
                    </div>
                    <div class="stat-info">
                        <h3 id="custoMensal">R$ 28.450</h3>
                        <p>Custo Mensal</p>
                        <small class="stat-subtitle">vs mês anterior</small>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar bg-info" style="width: 75%"></div>
                    </div>
                </div>

                <div class="stat-card enhanced" data-trend="down">
                    <div class="stat-header">
                        <div class="stat-icon bg-secondary">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-down"></i>
                            <span>-8%</span>
                        </div>
                    </div>
                    <div class="stat-info">
                        <h3 id="kmMedio">12.5</h3>
                        <p>Km/L Médio</p>
                        <small class="stat-subtitle">Eficiência combustível</small>
                    </div>
                    <div class="stat-progress">
                        <div class="progress-bar bg-secondary" style="width: 62%"></div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Charts Section -->
            <div class="charts-section">
                <!-- Chart Controls -->
                <div class="chart-controls">
                    <div class="control-group">
                        <label>Período:</label>
                        <select id="chartPeriod" class="form-select">
                            <option value="7">Últimos 7 dias</option>
                            <option value="30">Últimos 30 dias</option>
                            <option value="90" selected>Últimos 3 meses</option>
                            <option value="365">Último ano</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>Visualização:</label>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" data-view="charts">
                                <i class="fas fa-chart-line"></i> Gráficos
                            </button>
                            <button type="button" class="btn btn-outline-primary" data-view="table">
                                <i class="fas fa-table"></i> Tabela
                            </button>
                            <button type="button" class="btn btn-outline-primary" data-view="grid">
                                <i class="fas fa-th"></i> Grade
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Charts Grid -->
                <div class="charts-grid">
                    <!-- Main Chart - Consumo -->
                    <div class="chart-container main-chart">
                        <div class="chart-header">
                            <div class="chart-title">
                                <h3><i class="fas fa-gas-pump"></i> Consumo de Combustível</h3>
                                <p>Análise detalhada dos últimos meses</p>
                            </div>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.toggleChartType('consumoChart')">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.exportChart('consumoChart')">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.toggleChartFullscreen('consumoChart')">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="consumoChart"></canvas>
                        </div>
                        <div class="chart-footer">
                            <div class="chart-stats">
                                <span class="stat-item">
                                    <strong>Média:</strong> <span id="consumoMedia">1,280L</span>
                                </span>
                                <span class="stat-item">
                                    <strong>Tendência:</strong>
                                    <span class="trend-up"><i class="fas fa-arrow-up"></i> +5.2%</span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Secondary Chart - Custos -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <div class="chart-title">
                                <h3><i class="fas fa-dollar-sign"></i> Custos por Categoria</h3>
                                <p>Distribuição de gastos</p>
                            </div>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.toggleChartType('custosChart')">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.exportChart('custosChart')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="custosChart"></canvas>
                        </div>
                        <div class="chart-footer">
                            <div class="chart-legend">
                                <span class="legend-item">
                                    <span class="legend-color" style="background: #3498db;"></span>
                                    Combustível (45%)
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: #e74c3c;"></span>
                                    Manutenção (30%)
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: #f39c12;"></span>
                                    Outros (25%)
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- New Chart - Performance -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <div class="chart-title">
                                <h3><i class="fas fa-tachometer-alt"></i> Performance da Frota</h3>
                                <p>Eficiência e utilização</p>
                            </div>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.exportChart('performanceChart')">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>

                    <!-- New Chart - Alerts Timeline -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <div class="chart-title">
                                <h3><i class="fas fa-bell"></i> Timeline de Alertas</h3>
                                <p>Histórico de eventos</p>
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="alertsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Information Section -->
            <div class="info-section">
                <!-- Live Status Panel -->
                <div class="status-panel">
                    <div class="panel-header">
                        <h3><i class="fas fa-satellite-dish"></i> Status em Tempo Real</h3>
                        <div class="live-indicator">
                            <span class="live-dot"></span>
                            <span>LIVE</span>
                        </div>
                    </div>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-icon bg-success">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="status-info">
                                <h4 id="veiculosRota">23</h4>
                                <p>Em Rota</p>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon bg-warning">
                                <i class="fas fa-pause"></i>
                            </div>
                            <div class="status-info">
                                <h4 id="veiculosParados">8</h4>
                                <p>Parados</p>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon bg-info">
                                <i class="fas fa-gas-pump"></i>
                            </div>
                            <div class="status-info">
                                <h4 id="veiculosAbastecendo">3</h4>
                                <p>Abastecendo</p>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-icon bg-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="status-info">
                                <h4 id="alertasCriticos">2</h4>
                                <p>Alertas Críticos</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Tables Row -->
                <div class="tables-row">
                    <!-- Próximas Revisões -->
                    <div class="table-container enhanced">
                        <div class="table-header">
                            <div class="table-title">
                                <h3><i class="fas fa-calendar-check"></i> Próximas Revisões</h3>
                                <span class="table-count" id="revisoesCount">5 pendentes</span>
                            </div>
                            <div class="table-actions">
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.refreshTable('revisoes')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <a href="revisao.html" class="btn btn-sm btn-primary">Ver Todas</a>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Veículo</th>
                                        <th>Placa</th>
                                        <th>Data Prevista</th>
                                        <th>Km Atual</th>
                                        <th>Prioridade</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="proximasRevisoes">
                                    <!-- Dados serão carregados via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Centro de Notificações -->
                    <div class="notifications-center">
                        <div class="notifications-header">
                            <div class="notifications-title">
                                <h3><i class="fas fa-bell"></i> Centro de Notificações</h3>
                                <span class="notifications-count" id="notificationsCount">8 novas</span>
                            </div>
                            <div class="notifications-actions">
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.markAllAsRead()">
                                    <i class="fas fa-check-double"></i> Marcar como lidas
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.refreshNotifications()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Notification Tabs -->
                        <div class="notification-tabs">
                            <button class="tab-btn active" data-tab="all">
                                <i class="fas fa-list"></i> Todas (8)
                            </button>
                            <button class="tab-btn" data-tab="critical">
                                <i class="fas fa-exclamation-triangle"></i> Críticas (2)
                            </button>
                            <button class="tab-btn" data-tab="maintenance">
                                <i class="fas fa-wrench"></i> Manutenção (3)
                            </button>
                            <button class="tab-btn" data-tab="fuel">
                                <i class="fas fa-gas-pump"></i> Combustível (3)
                            </button>
                        </div>

                        <!-- Notifications List -->
                        <div class="notifications-list" id="notificationsList">
                            <!-- Notificações serão carregadas via JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="metrics-row">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h4><i class="fas fa-chart-line"></i> Eficiência Operacional</h4>
                        </div>
                        <div class="metric-body">
                            <div class="metric-item">
                                <span class="metric-label">Taxa de Utilização</span>
                                <div class="metric-value">
                                    <span class="value">87%</span>
                                    <div class="progress-ring">
                                        <svg class="progress-ring-svg" width="60" height="60">
                                            <circle class="progress-ring-circle" cx="30" cy="30" r="25"></circle>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Disponibilidade</span>
                                <div class="metric-value">
                                    <span class="value">94%</span>
                                    <div class="progress-ring">
                                        <svg class="progress-ring-svg" width="60" height="60">
                                            <circle class="progress-ring-circle" cx="30" cy="30" r="25"></circle>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-header">
                            <h4><i class="fas fa-dollar-sign"></i> Análise Financeira</h4>
                        </div>
                        <div class="metric-body">
                            <div class="financial-summary">
                                <div class="financial-item">
                                    <span class="label">Custo por Km</span>
                                    <span class="value">R$ 2,45</span>
                                    <span class="trend down">-3%</span>
                                </div>
                                <div class="financial-item">
                                    <span class="label">ROI Mensal</span>
                                    <span class="value">15.2%</span>
                                    <span class="trend up">+2%</span>
                                </div>
                                <div class="financial-item">
                                    <span class="label">Economia</span>
                                    <span class="value">R$ 3,240</span>
                                    <span class="trend up">+8%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/main.js"></script>

    <!-- Script de teste para debug -->
    <script>
        // Aguardar um pouco e testar se tudo está funcionando
        setTimeout(function() {
            console.log('=== TESTE DE FUNCIONALIDADE ===');
            console.log('Dashboard object:', window.dashboard);
            console.log('Chart.js loaded:', typeof Chart !== 'undefined');

            // Testar elementos
            const elements = ['consumoChart', 'proximasRevisoes', 'notificationsList'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                console.log(`Elemento ${id}:`, element ? 'ENCONTRADO' : 'NÃO ENCONTRADO');
            });

            // Testar métodos
            if (window.dashboard) {
                console.log('Métodos disponíveis:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.dashboard)));
            }
        }, 2000);
    </script>
</body>
</html>
