<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Cálculo de Consumo - Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Teste de Cálculo de Consumo (Km/L)</h1>
        
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Dados de Teste</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success me-2" onclick="criarDadosTeste()">
                            <i class="fas fa-plus"></i> Criar Dados de Teste
                        </button>
                        <button class="btn btn-warning me-2" onclick="limparDados()">
                            <i class="fas fa-trash"></i> Limpar Dados
                        </button>
                        <button class="btn btn-info" onclick="verificarConsumo()">
                            <i class="fas fa-calculator"></i> Verificar Consumo
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="status-veiculos" class="mb-2">
                            <i class="fas fa-car"></i> Veículos: <span id="count-veiculos">0</span>
                        </div>
                        <div id="status-abastecimentos" class="mb-2">
                            <i class="fas fa-gas-pump"></i> Abastecimentos: <span id="count-abastecimentos">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Resultado do Teste</h5>
                    </div>
                    <div class="card-body">
                        <div id="resultado-teste">
                            <p class="text-muted">Clique em "Verificar Consumo" para ver os resultados</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para criar dados de teste
        function criarDadosTeste() {
            // Criar veículo de teste
            const veiculo = {
                id: 1,
                plate: 'ABC-1234',
                brand: 'Toyota',
                model: 'Corolla',
                year: 2023,
                fuel: 'flex',
                status: 'ativo',
                km: 0
            };
            
            localStorage.setItem('frotas_vehicles', JSON.stringify([veiculo]));
            
            // Criar abastecimentos de teste
            const abastecimentos = [
                {
                    id: 1,
                    veiculoId: 1,
                    data: '2024-01-01',
                    posto: 'Posto Teste 1',
                    tipoCombustivel: 'gasolina',
                    litros: 40,
                    valorLitro: 5.50,
                    valorTotal: 220.00,
                    kmAtual: 1000,
                    kmAnterior: null,
                    consumo: null,
                    observacoes: 'Primeiro abastecimento'
                },
                {
                    id: 2,
                    veiculoId: 1,
                    data: '2024-01-15',
                    posto: 'Posto Teste 2',
                    tipoCombustivel: 'gasolina',
                    litros: 35,
                    valorLitro: 5.60,
                    valorTotal: 196.00,
                    kmAtual: 1500,
                    kmAnterior: 1000,
                    consumo: 500 / 35, // 14.29 km/L
                    observacoes: 'Segundo abastecimento'
                },
                {
                    id: 3,
                    veiculoId: 1,
                    data: '2024-02-01',
                    posto: 'Posto Teste 3',
                    tipoCombustivel: 'gasolina',
                    litros: 38,
                    valorLitro: 5.45,
                    valorTotal: 207.10,
                    kmAtual: 2000,
                    kmAnterior: 1500,
                    consumo: 500 / 38, // 13.16 km/L
                    observacoes: 'Terceiro abastecimento'
                }
            ];
            
            localStorage.setItem('abastecimentos', JSON.stringify(abastecimentos));
            
            atualizarStatus();
            alert('Dados de teste criados com sucesso!');
        }
        
        // Função para limpar dados
        function limparDados() {
            if (confirm('Tem certeza que deseja limpar todos os dados?')) {
                localStorage.removeItem('frotas_vehicles');
                localStorage.removeItem('abastecimentos');
                atualizarStatus();
                document.getElementById('resultado-teste').innerHTML = '<p class="text-muted">Dados limpos</p>';
                alert('Dados limpos com sucesso!');
            }
        }
        
        // Função para verificar consumo
        function verificarConsumo() {
            const abastecimentos = JSON.parse(localStorage.getItem('abastecimentos') || '[]');
            const veiculos = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            
            if (abastecimentos.length === 0) {
                document.getElementById('resultado-teste').innerHTML = 
                    '<div class="alert alert-warning">Nenhum abastecimento encontrado. Crie dados de teste primeiro.</div>';
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-striped">';
            html += '<thead><tr><th>Data</th><th>KM Anterior</th><th>KM Atual</th><th>KM Percorridos</th><th>Litros</th><th>Consumo (km/L)</th><th>Status</th></tr></thead><tbody>';
            
            let totalConsumo = 0;
            let countConsumo = 0;
            
            abastecimentos.forEach(abast => {
                const veiculo = veiculos.find(v => v.id === abast.veiculoId);
                const kmPercorridos = abast.kmAnterior ? abast.kmAtual - abast.kmAnterior : 0;
                const consumoCalculado = abast.consumo ? abast.consumo.toFixed(2) : 'N/A';
                const status = abast.consumo ? 'Calculado' : 'Primeiro abastecimento';
                
                if (abast.consumo) {
                    totalConsumo += abast.consumo;
                    countConsumo++;
                }
                
                html += `<tr>
                    <td>${abast.data}</td>
                    <td>${abast.kmAnterior || 'N/A'}</td>
                    <td>${abast.kmAtual}</td>
                    <td>${kmPercorridos}</td>
                    <td>${abast.litros}L</td>
                    <td><strong>${consumoCalculado}</strong></td>
                    <td><span class="badge ${abast.consumo ? 'bg-success' : 'bg-secondary'}">${status}</span></td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            
            const mediaConsumo = countConsumo > 0 ? (totalConsumo / countConsumo).toFixed(2) : 0;
            
            html += `<div class="mt-3">
                <div class="alert alert-info">
                    <h6>Resumo:</h6>
                    <ul class="mb-0">
                        <li>Total de abastecimentos: ${abastecimentos.length}</li>
                        <li>Abastecimentos com consumo calculado: ${countConsumo}</li>
                        <li>Média de consumo: <strong>${mediaConsumo} km/L</strong></li>
                    </ul>
                </div>
            </div>`;
            
            document.getElementById('resultado-teste').innerHTML = html;
        }
        
        // Função para atualizar status
        function atualizarStatus() {
            const veiculos = JSON.parse(localStorage.getItem('frotas_vehicles') || '[]');
            const abastecimentos = JSON.parse(localStorage.getItem('abastecimentos') || '[]');
            
            document.getElementById('count-veiculos').textContent = veiculos.length;
            document.getElementById('count-abastecimentos').textContent = abastecimentos.length;
        }
        
        // Atualizar status ao carregar a página
        document.addEventListener('DOMContentLoaded', atualizarStatus);
    </script>
</body>
</html>
