<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Novas Colunas Abastecimento</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .test-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }
        
        .feature-item i {
            font-size: 1.5rem;
            margin-right: 1rem;
            color: #28a745;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .status-demo {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin: 1rem 0;
        }
        
        .status-card {
            flex: 1;
            min-width: 200px;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .status-pendente { background: #f8f9fa; border: 2px solid #6c757d; }
        .status-cupom { background: #fff3cd; border: 2px solid #ffc107; }
        .status-nota { background: #d1ecf1; border: 2px solid #17a2b8; }
        .status-completo { background: #d4edda; border: 2px solid #28a745; }
        
        .table-demo {
            font-size: 0.9rem;
        }
        
        .btn-test {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1><i class="fas fa-gas-pump text-primary"></i> Novas Colunas - Sistema de Abastecimento</h1>
            <p class="lead">Implementação de Motorista, Cupom Fiscal, Nota Fiscal e Status/Data</p>
            <div class="mt-3">
                <span class="badge bg-success fs-6">✅ IMPLEMENTADO</span>
            </div>
        </div>

        <!-- Novas Funcionalidades -->
        <div class="test-section">
            <h3><i class="fas fa-plus-circle text-success"></i> Novas Funcionalidades Implementadas</h3>
            
            <div class="feature-item">
                <i class="fas fa-user"></i>
                <div>
                    <strong>Coluna Motorista:</strong> Campo para identificar o motorista responsável pelo abastecimento
                </div>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-receipt"></i>
                <div>
                    <strong>Coluna Cupom Fiscal:</strong> Registro do número do cupom fiscal do abastecimento
                </div>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-file-invoice"></i>
                <div>
                    <strong>Coluna Nota Fiscal:</strong> Registro do número da nota fiscal quando disponível
                </div>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-chart-line"></i>
                <div>
                    <strong>Coluna Status/Data:</strong> Status automático baseado no preenchimento dos campos fiscais
                </div>
            </div>
        </div>

        <!-- Sistema de Status -->
        <div class="test-section">
            <h3><i class="fas fa-traffic-light text-warning"></i> Sistema de Status Automático</h3>
            <p>O status é calculado automaticamente baseado no preenchimento dos campos Cupom Fiscal e Nota Fiscal:</p>
            
            <div class="status-demo">
                <div class="status-card status-pendente">
                    <i class="fas fa-clock text-muted"></i>
                    <h5>Pendente</h5>
                    <small>Sem cupom nem nota</small>
                </div>
                
                <div class="status-card status-cupom">
                    <i class="fas fa-receipt text-warning"></i>
                    <h5>Cupom OK</h5>
                    <small>Apenas cupom preenchido</small>
                </div>
                
                <div class="status-card status-nota">
                    <i class="fas fa-file-invoice text-info"></i>
                    <h5>Nota OK</h5>
                    <small>Apenas nota preenchida</small>
                </div>
                
                <div class="status-card status-completo">
                    <i class="fas fa-check-circle text-success"></i>
                    <h5>Completo</h5>
                    <small>Cupom e nota preenchidos</small>
                </div>
            </div>
        </div>

        <!-- Estrutura da Tabela -->
        <div class="test-section">
            <h3><i class="fas fa-table text-info"></i> Nova Estrutura da Tabela</h3>
            <p>A tabela de abastecimentos agora possui as seguintes colunas:</p>
            
            <div class="table-responsive">
                <table class="table table-striped table-demo">
                    <thead class="table-dark">
                        <tr>
                            <th>Data</th>
                            <th>Veículo</th>
                            <th>Placa</th>
                            <th class="text-success">Motorista</th>
                            <th>Posto</th>
                            <th>Litros</th>
                            <th>Valor</th>
                            <th>Km</th>
                            <th>Consumo</th>
                            <th class="text-warning">Cupom Fiscal</th>
                            <th class="text-info">Nota Fiscal</th>
                            <th class="text-primary">Status/Data</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>15/01/2024</td>
                            <td>Honda Civic</td>
                            <td>ABC-1234</td>
                            <td class="text-success">João Silva</td>
                            <td>Shell</td>
                            <td>45L</td>
                            <td>R$ 270,00</td>
                            <td>15.000 km</td>
                            <td>12.5 km/L</td>
                            <td><span class="text-success"><i class="fas fa-receipt"></i> 123456</span></td>
                            <td><span class="text-info"><i class="fas fa-file-invoice"></i> NF789</span></td>
                            <td>
                                <div class="text-success">
                                    <i class="fas fa-check-circle"></i>
                                    <small class="d-block">Completo</small>
                                    <small class="text-muted">15/01/2024</small>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>14/01/2024</td>
                            <td>Toyota Corolla</td>
                            <td>XYZ-5678</td>
                            <td class="text-muted">-</td>
                            <td>Petrobras</td>
                            <td>40L</td>
                            <td>R$ 240,00</td>
                            <td>12.500 km</td>
                            <td>11.8 km/L</td>
                            <td><span class="text-success"><i class="fas fa-receipt"></i> 654321</span></td>
                            <td><span class="text-muted">-</span></td>
                            <td>
                                <div class="text-warning">
                                    <i class="fas fa-receipt"></i>
                                    <small class="d-block">Cupom OK</small>
                                    <small class="text-muted">14/01/2024</small>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Código Implementado -->
        <div class="test-section">
            <h3><i class="fas fa-code text-secondary"></i> Principais Implementações</h3>
            
            <h5>1. Função de Cálculo de Status:</h5>
            <div class="code-block">
calculateStatusInfo(abastecimento) {
    const hasCupom = abastecimento.cupomFiscal && abastecimento.cupomFiscal.trim() !== '';
    const hasNota = abastecimento.notaFiscal && abastecimento.notaFiscal.trim() !== '';
    
    if (hasNota && hasCupom) {
        return { status: 'Completo', statusClass: 'text-success', statusIcon: 'fas fa-check-circle' };
    } else if (hasCupom) {
        return { status: 'Cupom OK', statusClass: 'text-warning', statusIcon: 'fas fa-receipt' };
    } else if (hasNota) {
        return { status: 'Nota OK', statusClass: 'text-info', statusIcon: 'fas fa-file-invoice' };
    } else {
        return { status: 'Pendente', statusClass: 'text-muted', statusIcon: 'fas fa-clock' };
    }
}
            </div>
            
            <h5>2. Estrutura de Dados Atualizada:</h5>
            <div class="code-block">
const abastecimento = {
    id: Date.now(),
    veiculoId: parseInt(data.veiculo),
    data: data.dataAbastecimento,
    motorista: data.motorista || '',           // NOVO CAMPO
    posto: data.posto,
    tipoCombustivel: data.tipoCombustivel,
    litros: litros,
    valorLitro: parseFloat(data.valorLitro),
    valorTotal: parseFloat(data.valorTotal),
    kmAtual: kmAtual,
    kmAnterior: kmAnterior,
    consumo: consumo,
    cupomFiscal: data.cupomFiscal || '',       // NOVO CAMPO
    notaFiscal: data.notaFiscal || '',         // NOVO CAMPO
    observacoes: data.observacoes || ''
};
            </div>
        </div>

        <!-- Arquivos Modificados -->
        <div class="test-section">
            <h3><i class="fas fa-file-code text-primary"></i> Arquivos Modificados</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-html5 text-danger"></i> abastecimento.html</h5>
                    <ul class="list-unstyled">
                        <li>✅ Adicionadas 4 novas colunas na tabela</li>
                        <li>✅ Novos campos no formulário</li>
                        <li>✅ Campo Motorista</li>
                        <li>✅ Campo Cupom Fiscal</li>
                        <li>✅ Campo Nota Fiscal</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h5><i class="fas fa-js text-warning"></i> js/abastecimento.js</h5>
                    <ul class="list-unstyled">
                        <li>✅ calculateStatusInfo() - nova função</li>
                        <li>✅ formatCupomFiscal() - nova função</li>
                        <li>✅ formatNotaFiscal() - nova função</li>
                        <li>✅ Estrutura de dados atualizada</li>
                        <li>✅ Renderização da tabela atualizada</li>
                        <li>✅ Função de edição atualizada</li>
                        <li>✅ Exportação CSV atualizada</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Correções Implementadas -->
        <div class="test-section">
            <h3><i class="fas fa-wrench text-warning"></i> Correções Implementadas</h3>

            <div class="row">
                <div class="col-md-6">
                    <div class="feature-item">
                        <i class="fas fa-calendar-check text-success"></i>
                        <div>
                            <strong>✅ Data Status/Data Corrigida:</strong> Agora mostra a data de lançamento dos documentos fiscais
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="feature-item">
                        <i class="fas fa-font text-primary"></i>
                        <div>
                            <strong>✅ Fontes Reduzidas:</strong> Todas as fontes do sistema foram diminuídas para melhor legibilidade
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <h5>🔧 Detalhes das Correções:</h5>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> <strong>Data do Cupom Fiscal:</strong> Registrada quando o campo é preenchido</li>
                    <li><i class="fas fa-check text-success"></i> <strong>Data da Nota Fiscal:</strong> Registrada quando o campo é preenchido</li>
                    <li><i class="fas fa-check text-success"></i> <strong>Status/Data Inteligente:</strong> Mostra a data do último documento lançado</li>
                    <li><i class="fas fa-check text-success"></i> <strong>Histórico de Documentos:</strong> Visível na edição de registros</li>
                    <li><i class="fas fa-check text-success"></i> <strong>CSV Atualizado:</strong> Inclui colunas de datas dos documentos</li>
                </ul>
            </div>
        </div>

        <!-- Sistema de Datas dos Documentos -->
        <div class="test-section">
            <h3><i class="fas fa-calendar-alt text-info"></i> Sistema de Datas dos Documentos Fiscais</h3>

            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-receipt text-warning"></i> Cupom Fiscal</h5>
                    <ul class="list-unstyled">
                        <li>✅ Data registrada automaticamente quando preenchido</li>
                        <li>✅ Data atualizada se o número do cupom for alterado</li>
                        <li>✅ Exibida no campo Status/Data quando apenas cupom existe</li>
                    </ul>
                </div>

                <div class="col-md-6">
                    <h5><i class="fas fa-file-invoice text-info"></i> Nota Fiscal</h5>
                    <ul class="list-unstyled">
                        <li>✅ Data registrada automaticamente quando preenchida</li>
                        <li>✅ Data atualizada se o número da nota for alterado</li>
                        <li>✅ Exibida no campo Status/Data quando nota existe</li>
                    </ul>
                </div>
            </div>

            <div class="mt-3">
                <h5>📊 Lógica do Campo Status/Data:</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Situação</th>
                                <th>Status</th>
                                <th>Data Exibida</th>
                                <th>Detalhes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sem documentos</td>
                                <td><span class="badge bg-secondary">Pendente</span></td>
                                <td>-</td>
                                <td>Nenhum documento fiscal</td>
                            </tr>
                            <tr>
                                <td>Apenas cupom</td>
                                <td><span class="badge bg-warning">Cupom OK</span></td>
                                <td>Data do cupom</td>
                                <td>Quando cupom foi lançado</td>
                            </tr>
                            <tr>
                                <td>Apenas nota</td>
                                <td><span class="badge bg-info">Nota OK</span></td>
                                <td>Data da nota</td>
                                <td>Quando nota foi lançada</td>
                            </tr>
                            <tr>
                                <td>Cupom + Nota</td>
                                <td><span class="badge bg-success">Completo</span></td>
                                <td>Data da nota</td>
                                <td>Ambas as datas visíveis</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Teste -->
        <div class="test-section text-center">
            <h3><i class="fas fa-play-circle text-success"></i> Testar Implementação</h3>
            <p>Clique no botão abaixo para acessar o sistema de abastecimento e testar as novas funcionalidades:</p>
            <a href="abastecimento.html" class="btn-test">
                <i class="fas fa-gas-pump"></i> Acessar Sistema de Abastecimento
            </a>

            <div class="mt-3">
                <small class="text-muted">
                    ✅ Novas colunas implementadas | ✅ Data corrigida | ✅ Fontes reduzidas
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
