// Sistema de Gerenciamento de Veículos
class VehicleManager {
    constructor() {
        this.vehicles = this.loadVehicles();
        this.currentVehicle = null;
        this.init();
    }

    init() {
        this.loadVehiclesTable();
        this.setupEventListeners();
        this.setupFilters();
        this.updateStatistics();
        this.populateYearFilter();
    }

    // Carregar veículos do localStorage ou dados padrão
    loadVehicles() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            return JSON.parse(savedVehicles);
        }

        // Retornar array vazio se não houver veículos salvos
        return [];
    }

    // Salvar veículos no localStorage
    saveVehicles() {
        localStorage.setItem('frotas_vehicles', JSON.stringify(this.vehicles));
    }

    // Configurar event listeners
    setupEventListeners() {
        // Busca em tempo real
        document.getElementById('searchVehicle').addEventListener('input', () => {
            this.filterVehicles();
        });

        // Filtros
        document.getElementById('filterType').addEventListener('change', () => {
            this.filterVehicles();
        });

        document.getElementById('filterStatus').addEventListener('change', () => {
            this.filterVehicles();
        });

        document.getElementById('filterYear').addEventListener('change', () => {
            this.filterVehicles();
        });

        // Formatação da placa
        document.getElementById('vehiclePlate').addEventListener('input', (e) => {
            this.formatPlate(e.target);
        });
    }

    // Configurar filtros
    setupFilters() {
        this.filterVehicles();
    }

    // Popular filtro de anos
    populateYearFilter() {
        const yearFilter = document.getElementById('filterYear');
        const years = [...new Set(this.vehicles.map(v => v.year))].sort((a, b) => b - a);
        
        years.forEach(year => {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            yearFilter.appendChild(option);
        });
    }

    // Atualizar estatísticas
    updateStatistics() {
        const total = this.vehicles.length;
        const active = this.vehicles.filter(v => v.status === 'ativo').length;
        const maintenance = this.vehicles.filter(v => v.status === 'manutencao').length;
        const inactive = this.vehicles.filter(v => v.status === 'inativo').length;

        document.getElementById('totalVehicles').textContent = total;
        document.getElementById('activeVehicles').textContent = active;
        document.getElementById('maintenanceVehicles').textContent = maintenance;
        document.getElementById('inactiveVehicles').textContent = inactive;
    }

    // Carregar tabela de veículos
    loadVehiclesTable(vehiclesToShow = null) {
        const tbody = document.getElementById('vehiclesTableBody');
        const vehicles = vehiclesToShow || this.vehicles;

        tbody.innerHTML = '';

        vehicles.forEach(vehicle => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="vehicle-checkbox" value="${vehicle.id}">
                </td>
                <td>
                    <strong>${vehicle.plate}</strong>
                </td>
                <td>
                    <div class="vehicle-info">
                        <strong>${vehicle.brand} ${vehicle.model}</strong>
                        <small class="text-muted d-block">${vehicle.color}</small>
                    </div>
                </td>
                <td>
                    <span class="badge badge-${this.getTypeBadgeClass(vehicle.type)}">
                        ${this.getTypeLabel(vehicle.type)}
                    </span>
                </td>
                <td>${vehicle.year}</td>
                <td>${this.formatKm(vehicle.km)}</td>
                <td>
                    <span class="badge badge-${this.getStatusBadgeClass(vehicle.status)}">
                        ${this.getStatusLabel(vehicle.status)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="vehicleManager.editVehicle(${vehicle.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="vehicleManager.deleteVehicle(${vehicle.id})" title="Excluir">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="vehicleManager.viewHistory(${vehicle.id})" title="Histórico">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Filtrar veículos
    filterVehicles() {
        const search = document.getElementById('searchVehicle').value.toLowerCase();
        const typeFilter = document.getElementById('filterType').value;
        const statusFilter = document.getElementById('filterStatus').value;
        const yearFilter = document.getElementById('filterYear').value;

        const filteredVehicles = this.vehicles.filter(vehicle => {
            const matchesSearch = vehicle.plate.toLowerCase().includes(search) || 
                                vehicle.model.toLowerCase().includes(search) ||
                                vehicle.brand.toLowerCase().includes(search);
            const matchesType = !typeFilter || vehicle.type === typeFilter;
            const matchesStatus = !statusFilter || vehicle.status === statusFilter;
            const matchesYear = !yearFilter || vehicle.year.toString() === yearFilter;

            return matchesSearch && matchesType && matchesStatus && matchesYear;
        });

        this.loadVehiclesTable(filteredVehicles);
    }

    // Limpar filtros
    clearFilters() {
        document.getElementById('searchVehicle').value = '';
        document.getElementById('filterType').value = '';
        document.getElementById('filterStatus').value = '';
        document.getElementById('filterYear').value = '';
        this.loadVehiclesTable();
    }

    // Mostrar modal de adicionar veículo
    showAddVehicleModal() {
        this.currentVehicle = null;
        document.getElementById('vehicleModalTitle').textContent = 'Novo Veículo';
        document.getElementById('vehicleForm').reset();
        document.getElementById('vehicleId').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('vehicleModal'));
        modal.show();
    }

    // Editar veículo
    editVehicle(vehicleId) {
        const vehicle = this.vehicles.find(v => v.id === vehicleId);
        if (!vehicle) return;

        this.currentVehicle = vehicle;
        document.getElementById('vehicleModalTitle').textContent = 'Editar Veículo';
        document.getElementById('vehicleId').value = vehicle.id;
        document.getElementById('vehiclePlate').value = vehicle.plate;
        document.getElementById('vehicleModel').value = vehicle.model;
        document.getElementById('vehicleBrand').value = vehicle.brand;
        document.getElementById('vehicleType').value = vehicle.type;
        document.getElementById('vehicleYear').value = vehicle.year;
        document.getElementById('vehicleKm').value = vehicle.km;
        document.getElementById('vehicleStatus').value = vehicle.status;
        document.getElementById('vehicleFuel').value = vehicle.fuel;
        document.getElementById('vehicleColor').value = vehicle.color;
        document.getElementById('vehicleNotes').value = vehicle.notes || '';
        
        const modal = new bootstrap.Modal(document.getElementById('vehicleModal'));
        modal.show();
    }

    // Salvar veículo
    saveVehicle() {
        const form = document.getElementById('vehicleForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const vehicleId = document.getElementById('vehicleId').value;
        const plate = document.getElementById('vehiclePlate').value.toUpperCase();
        const model = document.getElementById('vehicleModel').value;
        const brand = document.getElementById('vehicleBrand').value;
        const type = document.getElementById('vehicleType').value;
        const year = parseInt(document.getElementById('vehicleYear').value);
        const km = parseInt(document.getElementById('vehicleKm').value) || 0;
        const status = document.getElementById('vehicleStatus').value;
        const fuel = document.getElementById('vehicleFuel').value;
        const color = document.getElementById('vehicleColor').value;
        const notes = document.getElementById('vehicleNotes').value;

        // Verificar placa duplicada
        const existingVehicle = this.vehicles.find(v => v.plate === plate && v.id != vehicleId);
        if (existingVehicle) {
            alert('Esta placa já está sendo usada por outro veículo!');
            return;
        }

        if (vehicleId) {
            // Editar veículo existente
            const vehicleIndex = this.vehicles.findIndex(v => v.id == vehicleId);
            if (vehicleIndex !== -1) {
                this.vehicles[vehicleIndex] = {
                    ...this.vehicles[vehicleIndex],
                    plate, model, brand, type, year, km, status, fuel, color, notes,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // Adicionar novo veículo
            const newVehicle = {
                id: Math.max(...this.vehicles.map(v => v.id)) + 1,
                plate, model, brand, type, year, km, status, fuel, color, notes,
                createdAt: new Date().toISOString()
            };
            this.vehicles.push(newVehicle);
        }

        this.saveVehicles();
        this.loadVehiclesTable();
        this.updateStatistics();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('vehicleModal'));
        modal.hide();

        // Mostrar notificação
        const message = vehicleId ? 'Veículo atualizado com sucesso!' : 'Veículo criado com sucesso!';
        console.log('✅', message);
        alert(message);
    }

    // Excluir veículo
    deleteVehicle(vehicleId) {
        const vehicle = this.vehicles.find(v => v.id === vehicleId);
        if (!vehicle) return;

        if (confirm(`Deseja realmente excluir o veículo "${vehicle.plate}"?`)) {
            this.vehicles = this.vehicles.filter(v => v.id !== vehicleId);
            this.saveVehicles();
            this.loadVehiclesTable();
            this.updateStatistics();

            console.log('✅ Veículo excluído com sucesso!');
            alert('Veículo excluído com sucesso!');
        }
    }

    // Ver histórico do veículo
    viewHistory(vehicleId) {
        const vehicle = this.vehicles.find(v => v.id === vehicleId);
        if (!vehicle) return;

        alert(`Histórico do veículo ${vehicle.plate}\n\nEsta funcionalidade será implementada em breve.`);
    }

    // Exportar veículos
    exportVehicles() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `veiculos_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Gerar CSV
    generateCSV() {
        const headers = ['ID', 'Placa', 'Marca', 'Modelo', 'Tipo', 'Ano', 'Quilometragem', 'Status', 'Combustível', 'Cor'];
        const rows = this.vehicles.map(vehicle => [
            vehicle.id,
            vehicle.plate,
            vehicle.brand,
            vehicle.model,
            this.getTypeLabel(vehicle.type),
            vehicle.year,
            vehicle.km,
            this.getStatusLabel(vehicle.status),
            vehicle.fuel,
            vehicle.color
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    // Formatar placa
    formatPlate(input) {
        let value = input.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
        
        if (value.length > 3) {
            value = value.substring(0, 3) + '-' + value.substring(3, 7);
        }
        
        input.value = value;
    }

    // Utilitários
    getTypeBadgeClass(type) {
        switch (type) {
            case 'carro': return 'primary';
            case 'caminhao': return 'warning';
            case 'van': return 'info';
            case 'moto': return 'success';
            default: return 'secondary';
        }
    }

    getTypeLabel(type) {
        switch (type) {
            case 'carro': return 'Carro';
            case 'caminhao': return 'Caminhão';
            case 'van': return 'Van';
            case 'moto': return 'Moto';
            default: return 'Desconhecido';
        }
    }

    getStatusBadgeClass(status) {
        switch (status) {
            case 'ativo': return 'success';
            case 'manutencao': return 'warning';
            case 'inativo': return 'secondary';
            default: return 'secondary';
        }
    }

    getStatusLabel(status) {
        switch (status) {
            case 'ativo': return 'Ativo';
            case 'manutencao': return 'Em Manutenção';
            case 'inativo': return 'Inativo';
            default: return 'Desconhecido';
        }
    }

    formatKm(km) {
        return new Intl.NumberFormat('pt-BR').format(km) + ' km';
    }
}

// Funções globais
function showAddVehicleModal() {
    vehicleManager.showAddVehicleModal();
}

function saveVehicle() {
    vehicleManager.saveVehicle();
}

function clearFilters() {
    vehicleManager.clearFilters();
}

function exportVehicles() {
    vehicleManager.exportVehicles();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.vehicle-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    window.vehicleManager = new VehicleManager();
});
