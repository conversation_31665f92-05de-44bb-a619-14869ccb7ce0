# Sistema de Armazenamento Baseado em Arquivos

## Visão Geral

Este sistema substitui o localStorage por um sistema de armazenamento baseado em arquivos JSON locais, oferecendo maior flexibilidade e persistência de dados.

## Componentes

### 1. FileStorage (`js/file-storage.js`)
Classe principal que gerencia o armazenamento em arquivos JSON.

**Características:**
- Suporte para ambiente Node.js e Browser
- Cache em memória para melhor performance
- File System Access API para browsers modernos
- Fallback para localStorage quando necessário
- Operações assíncronas

**Métodos principais:**
```javascript
await fileStorage.setItem(key, data)     // Salvar dados
await fileStorage.getItem(key)           // Carregar dados
await fileStorage.removeItem(key)        // Remover dados
await fileStorage.clear()                // Limpar todos os dados
await fileStorage.getAllKeys()           // Listar chaves
await fileStorage.getStorageStats()      // Estatísticas
```

### 2. StorageAdapter (`js/storage-adapter.js`)
Adaptador que fornece interface compatível com localStorage.

**Características:**
- Interface compatível com localStorage
- Métodos assíncronos para melhor controle
- Migração automática de dados
- Métodos adicionais para objetos

**Métodos principais:**
```javascript
await storageAdapter.setItem(key, value)           // Compatível com localStorage
await storageAdapter.getItem(key)                  // Retorna string JSON
await storageAdapter.getItemAsObject(key)          // Retorna objeto diretamente
await storageAdapter.migrateFromLocalStorage()     // Migrar dados
await storageAdapter.clearLocalStorageAfterMigration() // Limpar localStorage
```

### 3. Interface de Migração (`migrar-dados.html`)
Página web para gerenciar a migração de dados do localStorage para o sistema de arquivos.

**Funcionalidades:**
- Verificação de status do sistema
- Migração interativa com progresso
- Limpeza do localStorage após migração
- Estatísticas detalhadas de armazenamento
- Log de operações em tempo real

## Como Usar

### 1. Incluir os Scripts
```html
<script src="js/file-storage.js"></script>
<script src="js/storage-adapter.js"></script>
```

### 2. Inicializar em um Módulo
```javascript
class MinhaClasse {
    constructor() {
        this.storageAdapter = new StorageAdapter();
        this.dados = [];
        this.init();
    }

    async init() {
        // Aguardar inicialização
        await this.storageAdapter.waitForReady();
        
        // Carregar dados
        await this.carregarDados();
        
        // Configurar interface
        this.configurarInterface();
    }

    async carregarDados() {
        try {
            const data = await this.storageAdapter.getItemAsObject('minha_chave');
            this.dados = data || [];
            console.log(`${this.dados.length} itens carregados`);
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            this.dados = [];
        }
    }

    async salvarDados() {
        try {
            await this.storageAdapter.setItem('minha_chave', this.dados);
            console.log('Dados salvos com sucesso');
            return true;
        } catch (error) {
            console.error('Erro ao salvar dados:', error);
            return false;
        }
    }
}
```

### 3. Migração de Dados Existentes

1. Abra `migrar-dados.html` no navegador
2. Clique em "Verificar Dados" para ver o status atual
3. Clique em "Iniciar Migração" para migrar dados do localStorage
4. Após migração bem-sucedida, clique em "Limpar localStorage"

## Estrutura de Arquivos

```
projeto/
├── js/
│   ├── file-storage.js          # Sistema principal de armazenamento
│   ├── storage-adapter.js       # Adaptador compatível com localStorage
│   └── veiculos-file-storage.js # Exemplo de módulo atualizado
├── data/                        # Diretório para arquivos de dados
│   ├── frotas_vehicles.json     # Dados de veículos
│   ├── frotas_maintenance.json  # Dados de manutenção
│   └── ...                      # Outros arquivos de dados
└── migrar-dados.html           # Interface de migração
```

## Vantagens do Sistema

### 1. **Persistência Melhorada**
- Dados salvos em arquivos não são perdidos ao limpar cache do browser
- Backup e restauração mais fáceis
- Controle de versão possível

### 2. **Flexibilidade**
- Funciona em ambiente Node.js e Browser
- Suporte a File System Access API
- Fallback automático para localStorage

### 3. **Performance**
- Cache em memória para acesso rápido
- Operações assíncronas não bloqueantes
- Carregamento sob demanda

### 4. **Manutenibilidade**
- Interface compatível com localStorage
- Migração transparente
- Logs detalhados de operações

## Exemplo de Migração de Módulo

### Antes (localStorage):
```javascript
class VehicleManager {
    constructor() {
        this.vehicles = JSON.parse(localStorage.getItem('vehicles')) || [];
    }

    saveVehicles() {
        localStorage.setItem('vehicles', JSON.stringify(this.vehicles));
    }
}
```

### Depois (FileStorage):
```javascript
class VehicleManager {
    constructor() {
        this.vehicles = [];
        this.storageAdapter = new StorageAdapter();
        this.init();
    }

    async init() {
        await this.storageAdapter.waitForReady();
        await this.loadVehicles();
    }

    async loadVehicles() {
        this.vehicles = await this.storageAdapter.getItemAsObject('vehicles') || [];
    }

    async saveVehicles() {
        return await this.storageAdapter.setItem('vehicles', this.vehicles);
    }
}
```

## Configuração para Produção

### Node.js
```javascript
// Configurar caminho personalizado para dados
const fileStorage = new FileStorage();
fileStorage.dataPath = './dados-producao/';
```

### Browser com File System Access API
```javascript
// O usuário será solicitado a escolher um diretório
// Os dados serão salvos no diretório escolhido
```

### Fallback para localStorage
```javascript
// Funciona automaticamente quando File System Access API não está disponível
// Mantém compatibilidade com browsers mais antigos
```

## Troubleshooting

### Problema: "File System Access API não disponível"
**Solução:** O sistema automaticamente usa localStorage como fallback.

### Problema: "Erro ao salvar arquivo"
**Solução:** Verificar permissões do diretório e espaço em disco.

### Problema: "Dados não carregam após migração"
**Solução:** Verificar se a migração foi concluída com sucesso e se os arquivos foram criados.

## Próximos Passos

1. **Migrar todos os módulos** para usar o novo sistema
2. **Implementar backup automático** dos arquivos de dados
3. **Adicionar compressão** para arquivos grandes
4. **Implementar sincronização** entre dispositivos (opcional)

## Suporte

Para dúvidas ou problemas:
1. Verificar logs no console do navegador
2. Usar a página `migrar-dados.html` para diagnóstico
3. Verificar se todos os scripts estão incluídos corretamente
