# 🚀 Guia de Configuração do Supabase - Sistema de Gestão de Frotas

## 📋 Pré-requisitos
- Conta no Supabase (<EMAIL>)
- Node.js instalado
- Git instalado

## 🔧 Passo 1: Criar Projeto no Supabase

### 1.1 Acesse o Supabase
1. Vá para [supabase.com](https://supabase.com)
2. Faça login com: **<EMAIL>** / **Ra5izen2kim#**
3. Clique em "New Project"

### 1.2 Configurações do Projeto
```
Nome do Projeto: gestao-frotas
Organização: Sua organização padrão
Região: South America (São Paulo) - sa-east-1
Senha do Banco: Ra5izen2kim#
```

### 1.3 Aguardar Criação
- O projeto levará alguns minutos para ser criado
- Anote a URL do projeto e as chaves de API

## 🗄️ Passo 2: Configurar Banco de Dados

### 2.1 Acessar SQL Editor
1. No painel do Supabase, vá para "SQL Editor"
2. Execute o script de criação das tabelas (fornecido abaixo)

### 2.2 Script SQL para Criação das Tabelas
```sql
-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabela de usuários (estende auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de veículos
CREATE TABLE public.vehicles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    placa TEXT NOT NULL,
    modelo TEXT NOT NULL,
    marca TEXT NOT NULL,
    ano INTEGER NOT NULL,
    cor TEXT,
    combustivel TEXT NOT NULL,
    km_atual INTEGER DEFAULT 0,
    status TEXT DEFAULT 'ativo' CHECK (status IN ('ativo', 'inativo', 'manutencao')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de fornecedores
CREATE TABLE public.suppliers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    nome TEXT NOT NULL,
    tipo TEXT NOT NULL CHECK (tipo IN ('posto', 'oficina', 'lavajato', 'outros')),
    endereco TEXT,
    telefone TEXT,
    email TEXT,
    cnpj TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de abastecimentos
CREATE TABLE public.fuel_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES public.suppliers(id),
    data_abastecimento TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    litros DECIMAL(10,3) NOT NULL,
    valor_litro DECIMAL(10,3) NOT NULL,
    valor_total DECIMAL(10,2) NOT NULL,
    motorista TEXT,
    cupom_fiscal TEXT,
    nota_fiscal TEXT,
    status_data TIMESTAMP WITH TIME ZONE,
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de manutenções
CREATE TABLE public.maintenance_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES public.suppliers(id),
    data_manutencao TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    tipo_manutencao TEXT NOT NULL,
    descricao TEXT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'concluida' CHECK (status IN ('agendada', 'em_andamento', 'concluida', 'cancelada')),
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de lavagens
CREATE TABLE public.washing_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    data_lavagem TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    tipo_lavagem TEXT NOT NULL,
    local_lavagem TEXT NOT NULL CHECK (local_lavagem IN ('interno', 'externo')),
    nome_lavajato TEXT,
    endereco_lavajato TEXT,
    responsavel TEXT,
    valor DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'realizada' CHECK (status IN ('agendada', 'realizada', 'cancelada')),
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de revisões
CREATE TABLE public.revision_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_id UUID REFERENCES public.vehicles(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES public.suppliers(id),
    data_revisao TIMESTAMP WITH TIME ZONE NOT NULL,
    km_atual INTEGER NOT NULL,
    km_proxima_revisao INTEGER,
    tipo_revisao TEXT NOT NULL,
    itens_verificados TEXT[],
    valor DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'concluida' CHECK (status IN ('agendada', 'concluida', 'cancelada')),
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de controle de caixa
CREATE TABLE public.cash_flow (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    data_transacao TIMESTAMP WITH TIME ZONE NOT NULL,
    tipo TEXT NOT NULL CHECK (tipo IN ('entrada', 'saida')),
    categoria TEXT NOT NULL,
    descricao TEXT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    referencia_id UUID, -- ID de referência para outras tabelas
    referencia_tipo TEXT, -- Tipo da referência (fuel, maintenance, etc.)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para melhor performance
CREATE INDEX idx_vehicles_user_id ON public.vehicles(user_id);
CREATE INDEX idx_vehicles_placa ON public.vehicles(placa);
CREATE INDEX idx_fuel_records_user_id ON public.fuel_records(user_id);
CREATE INDEX idx_fuel_records_vehicle_id ON public.fuel_records(vehicle_id);
CREATE INDEX idx_fuel_records_data ON public.fuel_records(data_abastecimento);
CREATE INDEX idx_maintenance_records_user_id ON public.maintenance_records(user_id);
CREATE INDEX idx_maintenance_records_vehicle_id ON public.maintenance_records(vehicle_id);
CREATE INDEX idx_washing_records_user_id ON public.washing_records(user_id);
CREATE INDEX idx_washing_records_vehicle_id ON public.washing_records(vehicle_id);
CREATE INDEX idx_revision_records_user_id ON public.revision_records(user_id);
CREATE INDEX idx_revision_records_vehicle_id ON public.revision_records(vehicle_id);
CREATE INDEX idx_cash_flow_user_id ON public.cash_flow(user_id);
CREATE INDEX idx_cash_flow_data ON public.cash_flow(data_transacao);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON public.vehicles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON public.suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_fuel_records_updated_at BEFORE UPDATE ON public.fuel_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON public.maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_washing_records_updated_at BEFORE UPDATE ON public.washing_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_revision_records_updated_at BEFORE UPDATE ON public.revision_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cash_flow_updated_at BEFORE UPDATE ON public.cash_flow FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 🔐 Passo 3: Configurar Autenticação

### 3.1 Configurar Políticas RLS (Row Level Security)
```sql
-- Habilitar RLS em todas as tabelas
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fuel_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.washing_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revision_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cash_flow ENABLE ROW LEVEL SECURITY;

-- Políticas para profiles
CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Políticas para vehicles
CREATE POLICY "Users can view own vehicles" ON public.vehicles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own vehicles" ON public.vehicles FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own vehicles" ON public.vehicles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own vehicles" ON public.vehicles FOR DELETE USING (auth.uid() = user_id);

-- Políticas similares para outras tabelas...
-- (Continua no próximo arquivo devido ao limite de linhas)
```

## 📝 Próximos Passos
1. Execute o script SQL no Supabase
2. Configure as variáveis de ambiente
3. Instale as dependências do projeto
4. Execute a migração dos dados

## 🔑 Variáveis de Ambiente Necessárias
```env
SUPABASE_URL=sua_url_do_supabase
SUPABASE_ANON_KEY=sua_chave_anonima
SUPABASE_SERVICE_KEY=sua_chave_de_servico
DATABASE_URL=sua_url_do_banco_postgresql
```
