/**
 * Sistema de Armazenamento Baseado em Arquivos
 * Substitui o localStorage por arquivos JSON locais
 */
class FileStorage {
    constructor() {
        this.dataPath = './data/';
        this.initialized = false;
        this.cache = new Map();
        this.initializeStorage();
    }

    // Inicializar sistema de armazenamento
    async initializeStorage() {
        try {
            // Verificar se estamos em ambiente Node.js ou browser
            this.isNodeEnvironment = typeof window === 'undefined';
            
            if (this.isNodeEnvironment) {
                // Ambiente Node.js - usar sistema de arquivos
                const fs = require('fs').promises;
                const path = require('path');
                
                this.fs = fs;
                this.path = path;
                
                // Criar diretório de dados se não existir
                try {
                    await this.fs.access(this.dataPath);
                } catch (error) {
                    await this.fs.mkdir(this.dataPath, { recursive: true });
                    console.log('📁 Diretório de dados criado:', this.dataPath);
                }
            } else {
                // Ambiente browser - usar File System Access API ou fallback para localStorage
                if ('showDirectoryPicker' in window) {
                    console.log('🌐 Usando File System Access API');
                    this.useFileSystemAPI = true;
                } else {
                    console.log('🌐 Fallback para localStorage (File System API não disponível)');
                    this.useFileSystemAPI = false;
                }
            }
            
            this.initialized = true;
            console.log('✅ Sistema de armazenamento inicializado');
        } catch (error) {
            console.error('❌ Erro ao inicializar armazenamento:', error);
            this.initialized = false;
        }
    }

    // Obter caminho completo do arquivo
    getFilePath(key) {
        return this.isNodeEnvironment ? 
            this.path.join(this.dataPath, `${key}.json`) : 
            `${key}.json`;
    }

    // Salvar dados
    async setItem(key, data) {
        try {
            const jsonData = JSON.stringify(data, null, 2);
            
            if (this.isNodeEnvironment) {
                // Node.js - salvar em arquivo
                const filePath = this.getFilePath(key);
                await this.fs.writeFile(filePath, jsonData, 'utf8');
                console.log(`💾 Dados salvos em arquivo: ${filePath}`);
            } else if (this.useFileSystemAPI) {
                // Browser com File System Access API
                await this.saveWithFileSystemAPI(key, jsonData);
            } else {
                // Fallback para localStorage
                localStorage.setItem(key, JSON.stringify(data));
                console.log(`💾 Dados salvos no localStorage: ${key}`);
            }
            
            // Atualizar cache
            this.cache.set(key, data);
            return true;
        } catch (error) {
            console.error(`❌ Erro ao salvar ${key}:`, error);
            return false;
        }
    }

    // Carregar dados
    async getItem(key) {
        try {
            // Verificar cache primeiro
            if (this.cache.has(key)) {
                return this.cache.get(key);
            }

            let data = null;

            if (this.isNodeEnvironment) {
                // Node.js - ler de arquivo
                const filePath = this.getFilePath(key);
                try {
                    const jsonData = await this.fs.readFile(filePath, 'utf8');
                    data = JSON.parse(jsonData);
                    console.log(`📖 Dados carregados do arquivo: ${filePath}`);
                } catch (error) {
                    if (error.code !== 'ENOENT') {
                        throw error;
                    }
                    // Arquivo não existe, retornar null
                    return null;
                }
            } else if (this.useFileSystemAPI) {
                // Browser com File System Access API
                data = await this.loadWithFileSystemAPI(key);
            } else {
                // Fallback para localStorage
                const stored = localStorage.getItem(key);
                if (stored) {
                    data = JSON.parse(stored);
                    console.log(`📖 Dados carregados do localStorage: ${key}`);
                }
            }

            // Atualizar cache
            if (data !== null) {
                this.cache.set(key, data);
            }

            return data;
        } catch (error) {
            console.error(`❌ Erro ao carregar ${key}:`, error);
            return null;
        }
    }

    // Remover dados
    async removeItem(key) {
        try {
            if (this.isNodeEnvironment) {
                // Node.js - deletar arquivo
                const filePath = this.getFilePath(key);
                try {
                    await this.fs.unlink(filePath);
                    console.log(`🗑️ Arquivo removido: ${filePath}`);
                } catch (error) {
                    if (error.code !== 'ENOENT') {
                        throw error;
                    }
                }
            } else if (this.useFileSystemAPI) {
                // Browser com File System Access API
                await this.removeWithFileSystemAPI(key);
            } else {
                // Fallback para localStorage
                localStorage.removeItem(key);
                console.log(`🗑️ Item removido do localStorage: ${key}`);
            }

            // Remover do cache
            this.cache.delete(key);
            return true;
        } catch (error) {
            console.error(`❌ Erro ao remover ${key}:`, error);
            return false;
        }
    }

    // Listar todas as chaves
    async getAllKeys() {
        try {
            if (this.isNodeEnvironment) {
                // Node.js - listar arquivos
                const files = await this.fs.readdir(this.dataPath);
                return files
                    .filter(file => file.endsWith('.json'))
                    .map(file => file.replace('.json', ''));
            } else {
                // Browser - usar localStorage ou cache
                const keys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    keys.push(localStorage.key(i));
                }
                return keys;
            }
        } catch (error) {
            console.error('❌ Erro ao listar chaves:', error);
            return [];
        }
    }

    // Limpar todos os dados
    async clear() {
        try {
            const keys = await this.getAllKeys();
            
            for (const key of keys) {
                await this.removeItem(key);
            }
            
            this.cache.clear();
            console.log('🧹 Todos os dados foram limpos');
            return true;
        } catch (error) {
            console.error('❌ Erro ao limpar dados:', error);
            return false;
        }
    }

    // Salvar com File System Access API (browser)
    async saveWithFileSystemAPI(key, jsonData) {
        try {
            if (!this.directoryHandle) {
                this.directoryHandle = await window.showDirectoryPicker();
            }
            
            const fileHandle = await this.directoryHandle.getFileHandle(`${key}.json`, {
                create: true
            });
            
            const writable = await fileHandle.createWritable();
            await writable.write(jsonData);
            await writable.close();
            
            console.log(`💾 Dados salvos via File System API: ${key}.json`);
        } catch (error) {
            console.error('❌ Erro ao salvar com File System API:', error);
            throw error;
        }
    }

    // Carregar com File System Access API (browser)
    async loadWithFileSystemAPI(key) {
        try {
            if (!this.directoryHandle) {
                return null;
            }
            
            const fileHandle = await this.directoryHandle.getFileHandle(`${key}.json`);
            const file = await fileHandle.getFile();
            const text = await file.text();
            
            console.log(`📖 Dados carregados via File System API: ${key}.json`);
            return JSON.parse(text);
        } catch (error) {
            if (error.name === 'NotFoundError') {
                return null;
            }
            console.error('❌ Erro ao carregar com File System API:', error);
            throw error;
        }
    }

    // Remover com File System Access API (browser)
    async removeWithFileSystemAPI(key) {
        try {
            if (!this.directoryHandle) {
                return;
            }
            
            await this.directoryHandle.removeEntry(`${key}.json`);
            console.log(`🗑️ Arquivo removido via File System API: ${key}.json`);
        } catch (error) {
            if (error.name !== 'NotFoundError') {
                console.error('❌ Erro ao remover com File System API:', error);
                throw error;
            }
        }
    }

    // Obter estatísticas de armazenamento
    async getStorageStats() {
        try {
            const keys = await this.getAllKeys();
            const stats = {
                totalKeys: keys.length,
                totalSize: 0,
                keys: []
            };

            for (const key of keys) {
                const data = await this.getItem(key);
                const size = JSON.stringify(data).length;
                stats.totalSize += size;
                stats.keys.push({
                    key,
                    size,
                    itemCount: Array.isArray(data) ? data.length : 1
                });
            }

            return stats;
        } catch (error) {
            console.error('❌ Erro ao obter estatísticas:', error);
            return null;
        }
    }
}

// Criar instância global
const fileStorage = new FileStorage();

// Exportar para uso em outros módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileStorage;
}
