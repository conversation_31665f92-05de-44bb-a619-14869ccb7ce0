<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Controle de Caixa - Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        .balance-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .balance-amount {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 1rem 0;
        }
        
        .balance-positive {
            color: #28a745;
        }
        
        .balance-negative {
            color: #dc3545;
        }
        
        .transaction-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        
        .transaction-income {
            border-left-color: #28a745;
        }
        
        .transaction-expense {
            border-left-color: #dc3545;
        }
        
        .monthly-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .chart-container {
            height: 400px;
            margin: 2rem 0;
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .metric-card.success {
            border-left-color: #28a745;
        }

        .metric-card.danger {
            border-left-color: #dc3545;
        }

        .metric-card.warning {
            border-left-color: #ffc107;
        }

        .metric-card.info {
            border-left-color: #17a2b8;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-icon {
            font-size: 2.5rem;
            opacity: 0.7;
        }

        .trend-indicator {
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            margin-left: 0.5rem;
        }

        .trend-up {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .trend-down {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .dashboard-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #495057;
        }

        .quick-stats {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .alert-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-car-wash"></i> Lavagem</a></li>
                <li><a href="caixa.html" class="active"><i class="fas fa-wallet"></i> Controle de Caixa</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Controle de Caixa</h1>
            </div>
            <div class="topbar-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span>Admin</span>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
        <!-- Saldo Atual -->
        <div class="row">
            <div class="col-12">
                <div class="balance-card text-center">
                    <h2><i class="fas fa-wallet"></i> Controle de Caixa</h2>
                    <div class="balance-amount" id="saldoAtual">R$ 0,00</div>
                    <div class="mt-3">
                        <button class="btn btn-outline-light btn-sm me-2" onclick="definirSaldoInicial()">
                            <i class="fas fa-edit"></i> Definir Saldo Inicial
                        </button>
                        <button class="btn btn-outline-light btn-sm me-2" onclick="gerarRelatorioPDF()">
                            <i class="fas fa-file-pdf"></i> Relatório PDF
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="exportarDados()">
                            <i class="fas fa-download"></i> Exportar Dados
                        </button>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <h5>Total de Entradas</h5>
                            <div class="h4 text-success" id="totalEntradas">R$ 0,00</div>
                            <span class="trend-indicator" id="trendEntradas"></span>
                        </div>
                        <div class="col-md-3">
                            <h5>Total de Saídas</h5>
                            <div class="h4 text-danger" id="totalSaidas">R$ 0,00</div>
                            <span class="trend-indicator" id="trendSaidas"></span>
                        </div>
                        <div class="col-md-3">
                            <h5>Transações</h5>
                            <div class="h4" id="totalTransacoes">0</div>
                            <span class="trend-indicator" id="trendTransacoes"></span>
                        </div>
                        <div class="col-md-3">
                            <h5>Média Mensal</h5>
                            <div class="h4" id="mediaMensal">R$ 0,00</div>
                            <span class="trend-indicator" id="trendMedia"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métricas Rápidas -->
        <div class="dashboard-section">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="metric-card success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Maior Entrada</div>
                                <div class="metric-value text-success" id="maiorEntrada">R$ 0,00</div>
                                <small class="text-muted" id="dataMaiorEntrada">-</small>
                            </div>
                            <div class="metric-icon text-success">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="metric-card danger">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Maior Saída</div>
                                <div class="metric-value text-danger" id="maiorSaida">R$ 0,00</div>
                                <small class="text-muted" id="dataMaiorSaida">-</small>
                            </div>
                            <div class="metric-icon text-danger">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="metric-card warning">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Gasto Combustível</div>
                                <div class="metric-value text-warning" id="gastoCombustivel">R$ 0,00</div>
                                <small class="text-muted" id="percentualCombustivel">0% do total</small>
                            </div>
                            <div class="metric-icon text-warning">
                                <i class="fas fa-gas-pump"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="metric-card info">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="metric-label">Gasto Manutenção</div>
                                <div class="metric-value text-info" id="gastoManutencao">R$ 0,00</div>
                                <small class="text-muted" id="percentualManutencao">0% do total</small>
                            </div>
                            <div class="metric-icon text-info">
                                <i class="fas fa-wrench"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="dashboard-section">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> Evolução do Saldo</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="saldoChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie"></i> Distribuição de Gastos</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="gastosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertas e Insights -->
        <div class="dashboard-section">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-exclamation-triangle"></i> Alertas Financeiros</h5>
                        </div>
                        <div class="card-body" id="alertasFinanceiros">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-lightbulb"></i> Insights e Recomendações</h5>
                        </div>
                        <div class="card-body" id="insightsRecomendacoes">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estatísticas Rápidas -->
        <div class="dashboard-section">
            <div class="quick-stats">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="metric-label">Transações Hoje</div>
                        <div class="h5 text-primary" id="transacoesHoje">0</div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-label">Gasto Médio/Dia</div>
                        <div class="h5 text-warning" id="gastoMedioDia">R$ 0,00</div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-label">Dias até Zerar</div>
                        <div class="h5 text-danger" id="diasAteZerar">∞</div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-label">Economia Mensal</div>
                        <div class="h5 text-success" id="economiaMensal">R$ 0,00</div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-label">Meta Mensal</div>
                        <div class="h5 text-info" id="metaMensal">-</div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-label">Status</div>
                        <div class="h5" id="statusFinanceiro">
                            <span class="badge bg-success">Saudável</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controles -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus-circle"></i> Lançar Entrada</h5>
                    </div>
                    <div class="card-body">
                        <form id="entradaForm">
                            <div class="mb-3">
                                <label for="valorEntrada" class="form-label">Valor</label>
                                <input type="number" class="form-control" id="valorEntrada" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label for="descricaoEntrada" class="form-label">Descrição</label>
                                <input type="text" class="form-control" id="descricaoEntrada" required>
                            </div>
                            <div class="mb-3">
                                <label for="dataEntrada" class="form-label">Data</label>
                                <input type="date" class="form-control" id="dataEntrada" required>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Adicionar Entrada
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-minus-circle"></i> Lançar Saída</h5>
                    </div>
                    <div class="card-body">
                        <form id="saidaForm">
                            <div class="mb-3">
                                <label for="valorSaida" class="form-label">Valor</label>
                                <input type="number" class="form-control" id="valorSaida" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label for="descricaoSaida" class="form-label">Descrição</label>
                                <input type="text" class="form-control" id="descricaoSaida" required>
                            </div>
                            <div class="mb-3">
                                <label for="dataSaida" class="form-label">Data</label>
                                <input type="date" class="form-control" id="dataSaida" required>
                            </div>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-minus"></i> Adicionar Saída
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-filter"></i> Filtros</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filtroMes" class="form-label">Mês</label>
                                <select class="form-select" id="filtroMes">
                                    <option value="">Todos os meses</option>
                                    <option value="01">Janeiro</option>
                                    <option value="02">Fevereiro</option>
                                    <option value="03">Março</option>
                                    <option value="04">Abril</option>
                                    <option value="05">Maio</option>
                                    <option value="06">Junho</option>
                                    <option value="07">Julho</option>
                                    <option value="08">Agosto</option>
                                    <option value="09">Setembro</option>
                                    <option value="10">Outubro</option>
                                    <option value="11">Novembro</option>
                                    <option value="12">Dezembro</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filtroAno" class="form-label">Ano</label>
                                <select class="form-select" id="filtroAno">
                                    <option value="">Todos os anos</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filtroTipo" class="form-label">Tipo</label>
                                <select class="form-select" id="filtroTipo">
                                    <option value="">Todos</option>
                                    <option value="entrada">Entradas</option>
                                    <option value="saida">Saídas</option>
                                    <option value="abastecimento">Abastecimentos</option>
                                    <option value="manutencao">Manutenções</option>
                                    <option value="lavagem">Lavagens</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button class="btn btn-primary" onclick="aplicarFiltros()">
                                    <i class="fas fa-search"></i> Filtrar
                                </button>
                                <button class="btn btn-secondary ms-2" onclick="limparFiltros()">
                                    <i class="fas fa-times"></i> Limpar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resumo Mensal -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Resumo Mensal</h5>
                    </div>
                    <div class="card-body">
                        <div id="resumoMensal">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Histórico de Transações -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Histórico de Transações</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-success" onclick="exportarDados()">
                                <i class="fas fa-download"></i> Exportar
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="sincronizarDados()">
                                <i class="fas fa-sync"></i> Sincronizar
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>Tipo</th>
                                        <th>Descrição</th>
                                        <th>Entrada</th>
                                        <th>Saída</th>
                                        <th>Saldo</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody id="historicoTransacoes">
                                    <!-- Será preenchido via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div> <!-- Fecha content-area -->
    </div> <!-- Fecha main-content -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/caixa.js"></script>
</body>
</html>
