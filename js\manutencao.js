// Sistema de Manutenção
class ManutencaoSystem {
    constructor() {
        this.manutencoes = JSON.parse(localStorage.getItem('manutencoes')) || [];
        this.veiculos = this.getVeiculosFromStorage();
        this.filteredData = [...this.manutencoes];

        this.initializeSystem();
    }

    // Inicializar sistema
    initializeSystem() {
        this.loadVeiculos();
        this.loadManutencoes();
        this.setupEventListeners();
        this.updateStats();
    }

    // Carregar veículos do sistema
    getVeiculosFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                return vehicles.map(v => ({
                    id: v.id,
                    modelo: `${v.brand} ${v.model} ${v.year}`,
                    placa: v.plate,
                    kmAtual: v.km || 0
                }));
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
                return [];
            }
        }
        return [];
    }



    // Carregar veículos no select
    loadVeiculos() {
        const veiculoSelect = document.getElementById('veiculo');
        
        if (veiculoSelect) {
            veiculoSelect.innerHTML = '<option value="">Selecione o veículo</option>';
            this.veiculos.forEach(veiculo => {
                const option = document.createElement('option');
                option.value = veiculo.id;
                option.textContent = `${veiculo.modelo} - ${veiculo.placa}`;
                veiculoSelect.appendChild(option);
            });
        }
    }

    // Carregar manutenções nas tabelas
    loadManutencoes() {
        this.loadPendentes();
        this.loadAndamento();
        this.loadHistorico();
    }

    // Carregar manutenções pendentes
    loadPendentes() {
        const tbody = document.getElementById('pendentesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const pendentes = this.filteredData.filter(m => 
            m.status === 'agendada'
        ).sort((a, b) => new Date(a.dataAgendamento) - new Date(b.dataAgendamento));

        pendentes.forEach(manutencao => {
            const veiculo = this.veiculos.find(v => v.id == manutencao.veiculoId);
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td><span class="badge ${this.getTipoBadgeClass(manutencao.tipo)}">${this.getTipoText(manutencao.tipo)}</span></td>
                <td class="text-truncate" style="max-width: 200px;" title="${manutencao.problema}">${manutencao.problema}</td>
                <td><span class="badge ${this.getPrioridadeBadgeClass(manutencao.prioridade)}">${this.getPrioridadeText(manutencao.prioridade)}</span></td>
                <td>${Utils.formatDate(manutencao.dataAgendamento)}</td>
                <td>${manutencao.oficina}</td>
                <td>
                    <button class="btn btn-sm btn-outline-success" onclick="manutencaoSystem.iniciarManutencao(${manutencao.id})" title="Iniciar">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="manutencaoSystem.editManutencao(${manutencao.id})" title="Editar">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="manutencaoSystem.deleteManutencao(${manutencao.id})" title="Excluir">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Carregar manutenções em andamento
    loadAndamento() {
        const tbody = document.getElementById('andamentoTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const andamento = this.filteredData.filter(m => 
            m.status === 'em_andamento'
        ).sort((a, b) => new Date(a.dataInicio) - new Date(b.dataInicio));

        andamento.forEach(manutencao => {
            const veiculo = this.veiculos.find(v => v.id == manutencao.veiculoId);
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td><span class="badge ${this.getTipoBadgeClass(manutencao.tipo)}">${this.getTipoText(manutencao.tipo)}</span></td>
                <td class="text-truncate" style="max-width: 200px;" title="${manutencao.problema}">${manutencao.problema}</td>
                <td>${manutencao.oficina}</td>
                <td>${manutencao.dataInicio ? Utils.formatDate(manutencao.dataInicio) : 'N/A'}</td>
                <td>${manutencao.previsaoConclusao ? Utils.formatDate(manutencao.previsaoConclusao) : 'N/A'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-success" onclick="manutencaoSystem.concluirManutencao(${manutencao.id})" title="Concluir">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="manutencaoSystem.viewManutencao(${manutencao.id})" title="Visualizar">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="manutencaoSystem.editManutencao(${manutencao.id})" title="Editar">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Carregar histórico de manutenções
    loadHistorico() {
        const tbody = document.getElementById('historicoTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        const historico = this.filteredData
            .sort((a, b) => new Date(b.dataAgendamento) - new Date(a.dataAgendamento));

        historico.forEach(manutencao => {
            const veiculo = this.veiculos.find(v => v.id == manutencao.veiculoId);
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${Utils.formatDate(manutencao.dataAgendamento)}</td>
                <td>${veiculo ? veiculo.modelo : 'N/A'}</td>
                <td>${veiculo ? veiculo.placa : 'N/A'}</td>
                <td><span class="badge ${this.getTipoBadgeClass(manutencao.tipo)}">${this.getTipoText(manutencao.tipo)}</span></td>
                <td class="text-truncate" style="max-width: 200px;" title="${manutencao.problema}">${manutencao.problema}</td>
                <td>${manutencao.oficina}</td>
                <td>${manutencao.valorReal ? Utils.formatCurrency(manutencao.valorReal) : (manutencao.valorEstimado ? Utils.formatCurrency(manutencao.valorEstimado) : 'N/A')}</td>
                <td><span class="badge ${this.getStatusBadgeClass(manutencao.status)}">${this.getStatusText(manutencao.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="manutencaoSystem.viewManutencao(${manutencao.id})" title="Visualizar">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="manutencaoSystem.editManutencao(${manutencao.id})" title="Editar">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="manutencaoSystem.deleteManutencao(${manutencao.id})" title="Excluir">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }

    // Obter classe CSS do tipo
    getTipoBadgeClass(tipo) {
        switch (tipo) {
            case 'preventiva': return 'bg-info';
            case 'corretiva': return 'bg-warning';
            case 'emergencial': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    // Obter texto do tipo
    getTipoText(tipo) {
        switch (tipo) {
            case 'preventiva': return 'Preventiva';
            case 'corretiva': return 'Corretiva';
            case 'emergencial': return 'Emergencial';
            default: return tipo;
        }
    }

    // Obter classe CSS da prioridade
    getPrioridadeBadgeClass(prioridade) {
        switch (prioridade) {
            case 'baixa': return 'bg-success';
            case 'media': return 'bg-warning';
            case 'alta': return 'bg-danger';
            case 'critica': return 'bg-dark';
            default: return 'bg-secondary';
        }
    }

    // Obter texto da prioridade
    getPrioridadeText(prioridade) {
        switch (prioridade) {
            case 'baixa': return 'Baixa';
            case 'media': return 'Média';
            case 'alta': return 'Alta';
            case 'critica': return 'Crítica';
            default: return prioridade;
        }
    }

    // Obter classe CSS do status
    getStatusBadgeClass(status) {
        switch (status) {
            case 'agendada': return 'bg-warning';
            case 'em_andamento': return 'bg-info';
            case 'concluida': return 'bg-success';
            case 'cancelada': return 'bg-secondary';
            default: return 'bg-secondary';
        }
    }

    // Obter texto do status
    getStatusText(status) {
        switch (status) {
            case 'agendada': return 'Agendada';
            case 'em_andamento': return 'Em Andamento';
            case 'concluida': return 'Concluída';
            case 'cancelada': return 'Cancelada';
            default: return status;
        }
    }

    // Atualizar estatísticas
    updateStats() {
        const total = this.filteredData.length;
        const andamento = this.filteredData.filter(m => m.status === 'em_andamento').length;
        const criticas = this.filteredData.filter(m => m.prioridade === 'critica').length;
        const custoTotal = this.filteredData
            .filter(m => m.valorReal || m.valorEstimado)
            .reduce((sum, m) => sum + (m.valorReal || m.valorEstimado), 0);

        document.getElementById('totalManutencoes').textContent = total;
        document.getElementById('manutencoesAndamento').textContent = andamento;
        document.getElementById('manutencoesCriticas').textContent = criticas;
        document.getElementById('custoTotal').textContent = Utils.formatCurrency(custoTotal);
    }

    // Configurar event listeners
    setupEventListeners() {
        // Formulário de manutenção
        const form = document.getElementById('manutencaoForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Filtros
        const searchInput = document.getElementById('searchInput');
        const filterStatus = document.getElementById('filterStatus');
        const filterTipo = document.getElementById('filterTipo');
        const filterPrioridade = document.getElementById('filterPrioridade');

        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce(() => this.applyFilters(), 300));
        }

        if (filterStatus) {
            filterStatus.addEventListener('change', () => this.applyFilters());
        }

        if (filterTipo) {
            filterTipo.addEventListener('change', () => this.applyFilters());
        }

        if (filterPrioridade) {
            filterPrioridade.addEventListener('change', () => this.applyFilters());
        }
    }

    // Aplicar filtros
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const status = document.getElementById('filterStatus')?.value || '';
        const tipo = document.getElementById('filterTipo')?.value || '';
        const prioridade = document.getElementById('filterPrioridade')?.value || '';

        this.filteredData = this.manutencoes.filter(manutencao => {
            const veiculo = this.veiculos.find(v => v.id == manutencao.veiculoId);
            
            // Filtro de busca
            const matchesSearch = !searchTerm || 
                (veiculo && (veiculo.modelo.toLowerCase().includes(searchTerm) || 
                veiculo.placa.toLowerCase().includes(searchTerm))) ||
                manutencao.problema.toLowerCase().includes(searchTerm) ||
                manutencao.oficina.toLowerCase().includes(searchTerm);

            // Filtro de status
            const matchesStatus = !status || manutencao.status === status;

            // Filtro de tipo
            const matchesTipo = !tipo || manutencao.tipo === tipo;

            // Filtro de prioridade
            const matchesPrioridade = !prioridade || manutencao.prioridade === prioridade;

            return matchesSearch && matchesStatus && matchesTipo && matchesPrioridade;
        });

        this.loadManutencoes();
        this.updateStats();
    }

    // Manipular envio do formulário
    handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        
        const manutencao = {
            id: Date.now(),
            veiculoId: parseInt(data.veiculo),
            tipo: data.tipoManutencao,
            problema: data.problema,
            prioridade: data.prioridade,
            dataAgendamento: data.dataAgendamento,
            oficina: data.oficina,
            contato: data.contato || '',
            valorEstimado: data.valorEstimado ? parseFloat(data.valorEstimado) : null,
            valorReal: null,
            status: 'agendada',
            previsaoConclusao: data.previsaoConclusao || null,
            pecasNecessarias: data.pecasNecessarias || '',
            observacoes: data.observacoes || '',
            dataInicio: null,
            dataConclusao: null
        };

        this.manutencoes.unshift(manutencao);
        localStorage.setItem('manutencoes', JSON.stringify(this.manutencoes));
        
        this.filteredData = [...this.manutencoes];
        this.loadManutencoes();
        this.updateStats();
        
        // Fechar modal e limpar formulário
        const modal = bootstrap.Modal.getInstance(document.getElementById('manutencaoModal'));
        modal.hide();
        e.target.reset();
        
        frotasSystem.showNotification('Manutenção agendada com sucesso!', 'success');
    }

    // Iniciar manutenção
    iniciarManutencao(id) {
        const manutencaoIndex = this.manutencoes.findIndex(m => m.id === id);
        if (manutencaoIndex !== -1) {
            this.manutencoes[manutencaoIndex].status = 'em_andamento';
            this.manutencoes[manutencaoIndex].dataInicio = new Date().toISOString();
            
            localStorage.setItem('manutencoes', JSON.stringify(this.manutencoes));
            this.filteredData = [...this.manutencoes];
            this.loadManutencoes();
            this.updateStats();
            
            console.log('✅ Manutenção iniciada!');
            alert('Manutenção iniciada!');
        }
    }

    // Concluir manutenção
    concluirManutencao(id) {
        const valorReal = prompt('Informe o valor real da manutenção (opcional):');
        
        const manutencaoIndex = this.manutencoes.findIndex(m => m.id === id);
        if (manutencaoIndex !== -1) {
            this.manutencoes[manutencaoIndex].status = 'concluida';
            this.manutencoes[manutencaoIndex].dataConclusao = new Date().toISOString();
            if (valorReal && !isNaN(parseFloat(valorReal))) {
                this.manutencoes[manutencaoIndex].valorReal = parseFloat(valorReal);
            }
            
            localStorage.setItem('manutencoes', JSON.stringify(this.manutencoes));
            this.filteredData = [...this.manutencoes];
            this.loadManutencoes();
            this.updateStats();
            
            console.log('✅ Manutenção concluída com sucesso!');
            alert('Manutenção concluída com sucesso!');
        }
    }

    // Editar manutenção
    editManutencao(id) {
        const manutencao = this.manutencoes.find(m => m.id === id);
        if (!manutencao) return;

        // Preencher formulário com dados existentes
        document.getElementById('veiculo').value = manutencao.veiculoId;
        document.getElementById('tipoManutencao').value = manutencao.tipo;
        document.getElementById('problema').value = manutencao.problema;
        document.getElementById('prioridade').value = manutencao.prioridade;
        document.getElementById('dataAgendamento').value = manutencao.dataAgendamento;
        document.getElementById('oficina').value = manutencao.oficina;
        document.getElementById('contato').value = manutencao.contato;
        document.getElementById('valorEstimado').value = manutencao.valorEstimado || '';
        document.getElementById('previsaoConclusao').value = manutencao.previsaoConclusao || '';
        document.getElementById('pecasNecessarias').value = manutencao.pecasNecessarias;
        document.getElementById('observacoes').value = manutencao.observacoes;

        // Abrir modal
        const modal = new bootstrap.Modal(document.getElementById('manutencaoModal'));
        modal.show();

        // Alterar comportamento do formulário para edição
        const form = document.getElementById('manutencaoForm');
        form.dataset.editId = id;
    }

    // Visualizar manutenção
    viewManutencao(id) {
        const manutencao = this.manutencoes.find(m => m.id === id);
        const veiculo = this.veiculos.find(v => v.id == manutencao.veiculoId);
        
        if (!manutencao) return;

        const detalhes = `
            <strong>Veículo:</strong> ${veiculo ? veiculo.modelo + ' - ' + veiculo.placa : 'N/A'}<br>
            <strong>Tipo:</strong> ${this.getTipoText(manutencao.tipo)}<br>
            <strong>Prioridade:</strong> ${this.getPrioridadeText(manutencao.prioridade)}<br>
            <strong>Problema:</strong> ${manutencao.problema}<br>
            <strong>Data Agendada:</strong> ${Utils.formatDate(manutencao.dataAgendamento)}<br>
            <strong>Oficina:</strong> ${manutencao.oficina}<br>
            <strong>Contato:</strong> ${manutencao.contato}<br>
            <strong>Valor:</strong> ${manutencao.valorReal ? Utils.formatCurrency(manutencao.valorReal) : (manutencao.valorEstimado ? Utils.formatCurrency(manutencao.valorEstimado) : 'N/A')}<br>
            <strong>Status:</strong> ${this.getStatusText(manutencao.status)}<br>
            <strong>Peças:</strong> ${manutencao.pecasNecessarias}<br>
            <strong>Observações:</strong> ${manutencao.observacoes}
        `;

        // Criar modal simples para exibir detalhes
        const modalHtml = `
            <div class="modal fade" id="viewManutencaoModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Detalhes da Manutenção</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${detalhes}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal anterior se existir
        const existingModal = document.getElementById('viewManutencaoModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Adicionar novo modal
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('viewManutencaoModal'));
        modal.show();
    }

    // Deletar manutenção
    deleteManutencao(id) {
        if (confirm('Deseja realmente excluir esta manutenção?')) {
            this.manutencoes = this.manutencoes.filter(m => m.id !== id);
            localStorage.setItem('manutencoes', JSON.stringify(this.manutencoes));
            
            this.filteredData = [...this.manutencoes];
            this.loadManutencoes();
            this.updateStats();
            
            console.log('✅ Manutenção excluída com sucesso!');
            alert('Manutenção excluída com sucesso!');
        }
    }
}

// Funções globais
function openManutencaoModal() {
    // Limpar dados de edição
    const form = document.getElementById('manutencaoForm');
    form.removeAttribute('data-edit-id');
    form.reset();
    
    // Definir data atual + 1 dia
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setMinutes(tomorrow.getMinutes() - tomorrow.getTimezoneOffset());
    document.getElementById('dataAgendamento').value = tomorrow.toISOString().slice(0, 16);
    
    const modal = new bootstrap.Modal(document.getElementById('manutencaoModal'));
    modal.show();
}

// Inicializar sistema quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('pendentesTable')) {
        window.manutencaoSystem = new ManutencaoSystem();
    }
});
