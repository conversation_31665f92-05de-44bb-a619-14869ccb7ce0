const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticate } = require('../middleware/authMiddleware');
const { asyncHandler, validateRequest } = require('../middleware/errorMiddleware');
const Joi = require('joi');

const router = express.Router();

// Schema de validação para veículos
const vehicleSchema = Joi.object({
  placa: Joi.string().required().messages({
    'any.required': 'Placa é obrigatória'
  }),
  modelo: Joi.string().required().messages({
    'any.required': 'Modelo é obrigatório'
  }),
  marca: Joi.string().required().messages({
    'any.required': 'Marca é obrigatória'
  }),
  ano: Joi.number().integer().min(1900).max(new Date().getFullYear() + 1).required().messages({
    'number.min': 'Ano deve ser maior que 1900',
    'number.max': 'Ano não pode ser maior que o próximo ano',
    'any.required': 'Ano é obrigatório'
  }),
  cor: Joi.string().optional(),
  combustivel: Joi.string().valid('gasolina', 'etanol', 'diesel', 'flex', 'gnv', 'eletrico', 'hibrido').required().messages({
    'any.only': 'Tipo de combustível inválido',
    'any.required': 'Tipo de combustível é obrigatório'
  }),
  km_atual: Joi.number().integer().min(0).default(0),
  status: Joi.string().valid('ativo', 'inativo', 'manutencao').default('ativo'),
});

const updateVehicleSchema = vehicleSchema.fork(['placa', 'modelo', 'marca', 'ano', 'combustivel'], (schema) => schema.optional());

// Aplicar autenticação a todas as rotas
router.use(authenticate);

// @desc    Listar todos os veículos do usuário
// @route   GET /api/vehicles
// @access  Private
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, status, search } = req.query;
  const offset = (page - 1) * limit;

  let query = supabase
    .from('vehicles')
    .select('*', { count: 'exact' })
    .eq('user_id', req.user.id)
    .order('created_at', { ascending: false });

  // Filtrar por status
  if (status && status !== 'all') {
    query = query.eq('status', status);
  }

  // Buscar por placa, modelo ou marca
  if (search) {
    query = query.or(`placa.ilike.%${search}%,modelo.ilike.%${search}%,marca.ilike.%${search}%`);
  }

  // Paginação
  query = query.range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) {
    return res.status(500).json({
      error: 'Erro ao buscar veículos',
      code: 'VEHICLES_FETCH_ERROR'
    });
  }

  res.json({
    vehicles: data,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count,
      pages: Math.ceil(count / limit),
    }
  });
}));

// @desc    Obter veículo por ID
// @route   GET /api/vehicles/:id
// @access  Private
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const { data, error } = await supabase
    .from('vehicles')
    .select('*')
    .eq('id', id)
    .eq('user_id', req.user.id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return res.status(404).json({
        error: 'Veículo não encontrado',
        code: 'VEHICLE_NOT_FOUND'
      });
    }
    return res.status(500).json({
      error: 'Erro ao buscar veículo',
      code: 'VEHICLE_FETCH_ERROR'
    });
  }

  res.json({ vehicle: data });
}));

// @desc    Criar novo veículo
// @route   POST /api/vehicles
// @access  Private
router.post('/', validateRequest(vehicleSchema), asyncHandler(async (req, res) => {
  const vehicleData = {
    ...req.body,
    user_id: req.user.id,
  };

  // Verificar se placa já existe para este usuário
  const { data: existingVehicle } = await supabase
    .from('vehicles')
    .select('id')
    .eq('placa', vehicleData.placa)
    .eq('user_id', req.user.id)
    .single();

  if (existingVehicle) {
    return res.status(409).json({
      error: 'Já existe um veículo com esta placa',
      code: 'DUPLICATE_PLATE'
    });
  }

  const { data, error } = await supabase
    .from('vehicles')
    .insert([vehicleData])
    .select()
    .single();

  if (error) {
    return res.status(500).json({
      error: 'Erro ao criar veículo',
      code: 'VEHICLE_CREATE_ERROR'
    });
  }

  res.status(201).json({
    message: 'Veículo criado com sucesso',
    vehicle: data
  });
}));

// @desc    Atualizar veículo
// @route   PUT /api/vehicles/:id
// @access  Private
router.put('/:id', validateRequest(updateVehicleSchema), asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Verificar se o veículo pertence ao usuário
  const { data: existingVehicle, error: fetchError } = await supabase
    .from('vehicles')
    .select('id')
    .eq('id', id)
    .eq('user_id', req.user.id)
    .single();

  if (fetchError || !existingVehicle) {
    return res.status(404).json({
      error: 'Veículo não encontrado',
      code: 'VEHICLE_NOT_FOUND'
    });
  }

  // Se a placa foi alterada, verificar duplicatas
  if (req.body.placa) {
    const { data: duplicatePlate } = await supabase
      .from('vehicles')
      .select('id')
      .eq('placa', req.body.placa)
      .eq('user_id', req.user.id)
      .neq('id', id)
      .single();

    if (duplicatePlate) {
      return res.status(409).json({
        error: 'Já existe outro veículo com esta placa',
        code: 'DUPLICATE_PLATE'
      });
    }
  }

  const { data, error } = await supabase
    .from('vehicles')
    .update(req.body)
    .eq('id', id)
    .eq('user_id', req.user.id)
    .select()
    .single();

  if (error) {
    return res.status(500).json({
      error: 'Erro ao atualizar veículo',
      code: 'VEHICLE_UPDATE_ERROR'
    });
  }

  res.json({
    message: 'Veículo atualizado com sucesso',
    vehicle: data
  });
}));

// @desc    Deletar veículo
// @route   DELETE /api/vehicles/:id
// @access  Private
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Verificar se existem registros relacionados
  const relatedTables = ['fuel_records', 'maintenance_records', 'washing_records', 'revision_records'];
  
  for (const table of relatedTables) {
    const { data: relatedRecords } = await supabase
      .from(table)
      .select('id')
      .eq('vehicle_id', id)
      .limit(1);

    if (relatedRecords && relatedRecords.length > 0) {
      return res.status(409).json({
        error: 'Não é possível excluir veículo com registros relacionados',
        code: 'VEHICLE_HAS_RECORDS'
      });
    }
  }

  const { error } = await supabase
    .from('vehicles')
    .delete()
    .eq('id', id)
    .eq('user_id', req.user.id);

  if (error) {
    return res.status(500).json({
      error: 'Erro ao deletar veículo',
      code: 'VEHICLE_DELETE_ERROR'
    });
  }

  res.json({
    message: 'Veículo deletado com sucesso'
  });
}));

// @desc    Obter estatísticas dos veículos
// @route   GET /api/vehicles/stats/overview
// @access  Private
router.get('/stats/overview', asyncHandler(async (req, res) => {
  const { data: vehicles, error } = await supabase
    .from('vehicles')
    .select('status, combustivel')
    .eq('user_id', req.user.id);

  if (error) {
    return res.status(500).json({
      error: 'Erro ao buscar estatísticas',
      code: 'STATS_ERROR'
    });
  }

  const stats = {
    total: vehicles.length,
    byStatus: vehicles.reduce((acc, vehicle) => {
      acc[vehicle.status] = (acc[vehicle.status] || 0) + 1;
      return acc;
    }, {}),
    byFuel: vehicles.reduce((acc, vehicle) => {
      acc[vehicle.combustivel] = (acc[vehicle.combustivel] || 0) + 1;
      return acc;
    }, {}),
  };

  res.json({ stats });
}));

module.exports = router;
