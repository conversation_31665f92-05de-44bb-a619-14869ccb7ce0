// Enhanced Dashboard JavaScript
class Dashboard {
    constructor() {
        console.log('🚀 Iniciando Dashboard...');

        this.charts = {};
        this.data = {};
        this.refreshInterval = null;
        this.currentPeriod = 90;
        this.currentView = 'charts';

        // Verificar se todos os elementos necessários estão presentes
        if (!this.checkRequiredElements()) {
            console.error('❌ Elementos necessários não encontrados');
            return;
        }

        console.log('📊 Inicializando dados...');
        this.initializeData();

        console.log('📈 Inicializando gráficos...');
        this.initializeCharts();

        console.log('📋 Carregando dados do dashboard...');
        this.loadDashboardData();

        console.log('🎯 Configurando event listeners...');
        this.setupEventListeners();

        console.log('🔄 Iniciando auto-refresh...');
        this.startAutoRefresh();

        console.log('🔐 Configurando controles de administrador...');
        this.setupAdminControls();

        console.log('✅ Dashboard Enhanced inicializado com sucesso');
    }

    // Verificar elementos necessários
    checkRequiredElements() {
        const requiredElements = [
            'consumoChart',
            'custosChart',
            'performanceChart',
            'alertsChart',
            'proximasRevisoes',
            'notificationsList'
        ];

        const missing = [];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                missing.push(id);
            }
        });

        if (missing.length > 0) {
            console.error('❌ Elementos não encontrados:', missing);
            return false;
        }

        console.log('✅ Todos os elementos necessários encontrados');
        return true;
    }

    // Configurar controles de administrador
    setupAdminControls() {
        console.log('🔐 Verificando permissões de administrador...');

        if (this.isAdmin()) {
            console.log('✅ Usuário é administrador - habilitando controles especiais');

            // Mostrar botão de deletar dados
            const deleteButton = document.querySelector('.admin-only');
            if (deleteButton) {
                deleteButton.style.display = 'flex';
                console.log('✅ Botão de deletar dados habilitado');
            }

            // Adicionar indicador visual de admin
            this.addAdminIndicator();

        } else {
            console.log('ℹ️ Usuário não é administrador - controles especiais ocultos');

            // Garantir que botões admin estejam ocultos
            const adminElements = document.querySelectorAll('.admin-only');
            adminElements.forEach(element => {
                element.style.display = 'none';
            });
        }
    }

    // Adicionar indicador visual de administrador
    addAdminIndicator() {
        const userNameElement = document.getElementById('userName');
        if (userNameElement && !userNameElement.querySelector('.admin-badge')) {
            const adminBadge = document.createElement('span');
            adminBadge.className = 'admin-badge ms-2';
            adminBadge.innerHTML = '<i class="fas fa-crown text-warning"></i>';
            adminBadge.title = 'Administrador do Sistema';
            userNameElement.appendChild(adminBadge);
        }
    }

    // Inicializar dados reais do sistema
    initializeData() {
        console.log('🔄 Inicializando dados...');

        this.data = {
            vehicles: this.loadVehicleData(),
            fuel: this.loadFuelData(),
            costs: this.loadCostData(),
            maintenance: this.loadMaintenanceData(),
            alerts: this.loadAlertData(),
            performance: this.loadPerformanceData()
        };

        console.log('✅ Dados inicializados:', {
            vehicles: this.data.vehicles?.total || 0,
            fuel: this.data.fuel?.length || 0,
            costs: this.data.costs?.total || 0,
            maintenance: this.data.maintenance?.length || 0,
            alerts: this.data.alerts?.length || 0,
            performance: !!this.data.performance
        });
    }

    // Carregar dados reais de veículos
    loadVehicleData() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        let vehicles = [];

        if (savedVehicles) {
            try {
                vehicles = JSON.parse(savedVehicles);
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
            }
        }

        const total = vehicles.length;
        const active = vehicles.filter(v => v.status === 'ativo').length;
        const maintenance = vehicles.filter(v => v.status === 'manutencao').length;
        const inactive = vehicles.filter(v => v.status === 'inativo').length;

        return {
            total: total,
            active: active,
            maintenance: maintenance,
            inactive: inactive,
            onRoute: 0, // Será implementado quando houver sistema de rastreamento
            stopped: 0,
            fueling: 0,
            criticalAlerts: 0
        };
    }

    // Carregar dados reais de combustível
    loadFuelData() {
        const savedFuel = localStorage.getItem('abastecimentos');
        if (savedFuel) {
            try {
                return JSON.parse(savedFuel);
            } catch (error) {
                console.error('Erro ao carregar dados de combustível:', error);
            }
        }
        return [];
    }

    // Carregar dados reais de custos
    loadCostData() {
        const fuelData = this.loadFuelData();
        const maintenanceData = this.loadMaintenanceData();

        const fuelCost = fuelData.reduce((total, item) => total + (item.valor || 0), 0);
        const maintenanceCost = maintenanceData.reduce((total, item) => total + (item.valorReal || item.valorEstimado || 0), 0);
        const totalCost = fuelCost + maintenanceCost;

        if (totalCost === 0) {
            return [];
        }

        return [
            { category: 'Combustível', value: Math.round((fuelCost / totalCost) * 100), color: '#3498db' },
            { category: 'Manutenção', value: Math.round((maintenanceCost / totalCost) * 100), color: '#e74c3c' },
            { category: 'Seguro', value: 0, color: '#f39c12' },
            { category: 'Outros', value: 0, color: '#95a5a6' }
        ];
    }

    // Carregar dados reais de manutenção
    loadMaintenanceData() {
        const savedMaintenance = localStorage.getItem('manutencoes');
        if (savedMaintenance) {
            try {
                return JSON.parse(savedMaintenance);
            } catch (error) {
                console.error('Erro ao carregar dados de manutenção:', error);
            }
        }
        return [];
    }

    // Carregar dados reais de alertas
    loadAlertData() {
        // Por enquanto retorna array vazio, será implementado quando houver sistema de alertas
        return [];
    }

    // Carregar dados reais de performance
    loadPerformanceData() {
        const vehicles = this.loadVehicleData();
        const fuelData = this.loadFuelData();
        const maintenanceData = this.loadMaintenanceData();

        return {
            efficiency: 0, // Será calculado quando houver dados suficientes
            availability: vehicles.total > 0 ? Math.round((vehicles.active / vehicles.total) * 100) : 0,
            costPerKm: 0, // Será calculado quando houver dados de quilometragem
            monthlyROI: 0,
            savings: 0
        };
    }

    // Inicializar gráficos
    initializeCharts() {
        console.log('🔧 Inicializando gráficos...');

        try {
            // Verificar se Chart.js está disponível
            if (typeof Chart === 'undefined') {
                console.error('❌ Chart.js não está carregado');
                return;
            }

            console.log('✅ Chart.js está disponível, versão:', Chart.version);

            console.log('📊 Criando gráfico de consumo...');
            this.createConsumoChart();

            console.log('📊 Criando gráfico de custos...');
            this.createCustosChart();

            console.log('📊 Criando gráfico de performance...');
            this.createPerformanceChart();

            console.log('📊 Criando gráfico de alertas...');
            this.createAlertsChart();

            console.log('✅ Todos os gráficos inicializados');
        } catch (error) {
            console.error('❌ Erro ao inicializar gráficos:', error);
            console.error('Stack trace:', error.stack);
        }
    }

    // Gráfico de consumo de combustível melhorado
    createConsumoChart() {
        console.log('🔄 Criando gráfico de consumo...');

        const ctx = document.getElementById('consumoChart');
        if (!ctx) {
            console.error('❌ Canvas consumoChart não encontrado');
            return;
        }

        try {
            // Destruir gráfico existente se houver
            if (this.charts.consumo) {
                this.charts.consumo.destroy();
            }

            const fuelData = this.data.fuel;
            console.log('📊 Dados de combustível:', fuelData);

            if (!fuelData || fuelData.length === 0) {
                console.error('❌ Dados de combustível não disponíveis');
                return;
            }

            const chartData = {
                labels: fuelData.map(item => item.month),
                datasets: [{
                    label: 'Consumo (Litros)',
                    data: fuelData.map(item => item.consumption),
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3498db',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }, {
                    label: 'Custo (R$)',
                    data: fuelData.map(item => item.cost / 10), // Escala para visualização
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            };

            console.log('📊 Configuração do gráfico:', chartData);

            console.log('🔧 Criando instância do Chart.js...');

            this.charts.consumo = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    if (context.datasetIndex === 0) {
                                        return `Consumo: ${context.parsed.y}L`;
                                    } else {
                                        return `Custo: R$ ${(context.parsed.y * 10).toLocaleString()}`;
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            title: {
                                display: true,
                                text: 'Litros'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                            title: {
                                display: true,
                                text: 'Custo (R$ x10)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });

            console.log('✅ Gráfico de consumo criado:', this.charts.consumo);
        } catch (error) {
            console.error('❌ Erro ao criar gráfico de consumo:', error);
            console.error('Stack trace:', error.stack);
        }
    }

    // Gráfico de custos por categoria melhorado
    createCustosChart() {
        console.log('🔄 Criando gráfico de custos...');

        const ctx = document.getElementById('custosChart');
        if (!ctx) {
            console.error('❌ Canvas custosChart não encontrado');
            return;
        }

        try {
            // Destruir gráfico existente se houver
            if (this.charts.custos) {
                this.charts.custos.destroy();
            }

            const costData = this.data.costs;
            console.log('📊 Dados de custos:', costData);

            if (!costData || costData.length === 0) {
                console.error('❌ Dados de custos não disponíveis');
                return;
            }

            console.log('🔧 Criando gráfico de custos...');

            this.charts.custos = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: costData.map(item => item.category),
                    datasets: [{
                        data: costData.map(item => item.value),
                        backgroundColor: costData.map(item => item.color),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed}%`;
                                }
                            }
                        }
                    }
                }
            });

            console.log('✅ Gráfico de custos criado:', this.charts.custos);
        } catch (error) {
            console.error('❌ Erro ao criar gráfico de custos:', error);
            console.error('Stack trace:', error.stack);
        }
    }

    // Novo gráfico de performance
    createPerformanceChart() {
        console.log('🔄 Criando gráfico de performance...');

        const ctx = document.getElementById('performanceChart');
        if (!ctx) {
            console.error('❌ Canvas performanceChart não encontrado');
            return;
        }

        try {
            // Destruir gráfico existente se houver
            if (this.charts.performance) {
                this.charts.performance.destroy();
            }

            const maintenanceData = this.data.maintenance;
            console.log('📊 Dados de manutenção:', maintenanceData);

            if (!maintenanceData || maintenanceData.length === 0) {
                console.error('❌ Dados de manutenção não disponíveis');
                return;
            }

            console.log('🔧 Criando gráfico de performance...');

            this.charts.performance = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: maintenanceData.map(item => item.month),
                    datasets: [{
                        label: 'Manutenção Preventiva',
                        data: maintenanceData.map(item => item.preventive),
                        backgroundColor: '#2ecc71'
                    }, {
                        label: 'Manutenção Corretiva',
                        data: maintenanceData.map(item => item.corrective),
                        backgroundColor: '#e74c3c'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Quantidade'
                            }
                        }
                    }
                }
            });

            console.log('✅ Gráfico de performance criado:', this.charts.performance);
        } catch (error) {
            console.error('❌ Erro ao criar gráfico de performance:', error);
            console.error('Stack trace:', error.stack);
        }
    }

    // Novo gráfico de timeline de alertas
    createAlertsChart() {
        console.log('🔄 Criando gráfico de alertas...');

        const ctx = document.getElementById('alertsChart');
        if (!ctx) {
            console.error('❌ Canvas alertsChart não encontrado');
            return;
        }

        try {
            // Destruir gráfico existente se houver
            if (this.charts.alerts) {
                this.charts.alerts.destroy();
            }

            const alertData = this.data.alerts.slice(-7); // Últimos 7 dias
            console.log('📊 Dados de alertas:', alertData);

            if (!alertData || alertData.length === 0) {
                console.error('❌ Dados de alertas não disponíveis');
                return;
            }

            console.log('🔧 Criando gráfico de alertas...');

            this.charts.alerts = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: alertData.map(item => `Dia ${item.day}`),
                    datasets: [{
                        label: 'Alertas por Dia',
                        data: alertData.map(item => item.alerts),
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Número de Alertas'
                            }
                        }
                    }
                }
            });

            console.log('✅ Gráfico de alertas criado:', this.charts.alerts);
        } catch (error) {
            console.error('❌ Erro ao criar gráfico de alertas:', error);
            console.error('Stack trace:', error.stack);
        }
    }

    // Carregar dados do dashboard
    loadDashboardData() {
        this.updateStats();
        this.updateLiveStatus();
        this.loadProximasRevisoes();
        this.loadNotifications();
        this.updatePerformanceMetrics();
        this.updateFinancialMetrics();
        console.log('✅ Dados do dashboard carregados');
    }

    // Atualizar estatísticas principais
    updateStats() {
        const vehicles = this.data.vehicles;

        // Atualizar cards principais
        this.updateElement('totalVeiculos', vehicles.total);
        this.updateElement('veiculosAtivos', vehicles.active);
        this.updateElement('manutencao', vehicles.maintenance);
        this.updateElement('alertas', vehicles.criticalAlerts);

        // Atualizar novos KPIs se existirem
        this.updateElement('custoMensal', 'R$ 45.230');
        this.updateElement('eficienciaCombustivel', '12.5 km/L');

        console.log('✅ Estatísticas atualizadas');
    }

    // Atualizar status em tempo real
    updateLiveStatus() {
        const vehicles = this.data.vehicles;

        this.updateElement('veiculosRota', vehicles.onRoute);
        this.updateElement('veiculosParados', vehicles.stopped);
        this.updateElement('veiculosAbastecendo', vehicles.fueling);
        this.updateElement('alertasCriticos', vehicles.criticalAlerts);

        console.log('✅ Status em tempo real atualizado');
    }

    // Atualizar métricas de performance
    updatePerformanceMetrics() {
        const performance = this.data.performance;

        // Atualizar anéis de progresso
        this.updateProgressRing('efficiency', performance.efficiency);
        this.updateProgressRing('availability', performance.availability);

        console.log('✅ Métricas de performance atualizadas');
    }

    // Atualizar métricas financeiras
    updateFinancialMetrics() {
        const performance = this.data.performance;

        this.updateElement('costPerKm', `R$ ${performance.costPerKm.toFixed(2)}`);
        this.updateElement('monthlyROI', `${performance.monthlyROI}%`);
        this.updateElement('savings', `R$ ${performance.savings.toLocaleString()}`);

        console.log('✅ Métricas financeiras atualizadas');
    }

    // Método auxiliar para atualizar elementos
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    // Atualizar anel de progresso
    updateProgressRing(type, percentage) {
        const circle = document.querySelector(`[data-progress="${type}"] .progress-ring-circle`);
        if (circle) {
            const circumference = 2 * Math.PI * 25; // raio = 25
            const offset = circumference - (percentage / 100) * circumference;
            circle.style.strokeDasharray = circumference;
            circle.style.strokeDashoffset = offset;
            circle.style.stroke = percentage > 80 ? '#2ecc71' : percentage > 60 ? '#f39c12' : '#e74c3c';
        }
    }

    // Carregar próximas revisões
    loadProximasRevisoes() {
        const revisoes = [
            {
                veiculo: 'Civic 2020',
                placa: 'ABC-1234',
                dataPrevista: '2024-01-15',
                kmAtual: '48.500',
                status: 'Pendente'
            },
            {
                veiculo: 'Corolla 2019',
                placa: 'DEF-5678',
                dataPrevista: '2024-01-18',
                kmAtual: '52.300',
                status: 'Agendado'
            },
            {
                veiculo: 'HB20 2021',
                placa: 'GHI-9012',
                dataPrevista: '2024-01-22',
                kmAtual: '35.800',
                status: 'Pendente'
            }
        ];

        const tbody = document.getElementById('proximasRevisoes');
        if (!tbody) {
            console.error('❌ Elemento proximasRevisoes não encontrado');
            return;
        }

        console.log('✅ Elemento proximasRevisoes encontrado');
        tbody.innerHTML = '';

        revisoes.forEach(revisao => {
            const row = document.createElement('tr');
            
            const statusClass = revisao.status === 'Agendado' ? 'badge bg-success' : 'badge bg-warning';
            
            row.innerHTML = `
                <td>${revisao.veiculo}</td>
                <td>${revisao.placa}</td>
                <td>${this.formatDate(revisao.dataPrevista)}</td>
                <td>${revisao.kmAtual} km</td>
                <td><span class="${statusClass}">${revisao.status}</span></td>
            `;
            
            tbody.appendChild(row);
        });

        console.log(`✅ ${revisoes.length} revisões carregadas`);
    }

    // Carregar notificações
    loadNotifications() {
        console.log('🔄 Carregando notificações...');
        const notifications = [
            {
                id: 1,
                type: 'critical',
                icon: 'fas fa-exclamation-triangle',
                title: 'Manutenção Urgente',
                message: 'Civic 2020 - Revisão dos 50.000km vencida',
                time: '2 horas atrás',
                category: 'maintenance',
                unread: true
            },
            {
                id: 2,
                type: 'warning',
                icon: 'fas fa-gas-pump',
                title: 'Combustível Baixo',
                message: 'Corolla 2019 - Nível crítico (15%)',
                time: '4 horas atrás',
                category: 'fuel',
                unread: true
            },
            {
                id: 3,
                type: 'info',
                icon: 'fas fa-calendar-check',
                title: 'Revisão Agendada',
                message: 'HB20 2021 - Agendamento confirmado para amanhã',
                time: '1 dia atrás',
                category: 'maintenance',
                unread: false
            },
            {
                id: 4,
                type: 'success',
                icon: 'fas fa-check-circle',
                title: 'Manutenção Concluída',
                message: 'Onix 2020 - Troca de óleo realizada',
                time: '2 dias atrás',
                category: 'maintenance',
                unread: false
            }
        ];

        const container = document.getElementById('notificationsList');
        if (!container) {
            console.error('❌ Elemento notificationsList não encontrado');
            return;
        }

        console.log('✅ Elemento notificationsList encontrado');

        container.innerHTML = notifications.map(notification => `
            <div class="notification-item ${notification.unread ? 'unread' : ''}" data-id="${notification.id}">
                <div class="notification-icon ${notification.type}">
                    <i class="${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-header">
                        <h5>${notification.title}</h5>
                        <span class="notification-time">${notification.time}</span>
                    </div>
                    <p>${notification.message}</p>
                    <div class="notification-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="dashboard.markAsRead(${notification.id})">
                            <i class="fas fa-check"></i> Marcar como lida
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.dismissNotification(${notification.id})">
                            <i class="fas fa-times"></i> Dispensar
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // Atualizar contador
        const unreadCount = notifications.filter(n => n.unread).length;
        this.updateElement('notificationsCount', `${unreadCount} novas`);

        console.log('✅ Notificações carregadas');
    }

    // Obter ícone do alerta
    getAlertIcon(tipo) {
        switch (tipo) {
            case 'warning': return 'fa-exclamation-triangle';
            case 'danger': return 'fa-times-circle';
            case 'info': return 'fa-info-circle';
            default: return 'fa-bell';
        }
    }

    // Formatar data
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    // Configurar event listeners
    setupEventListeners() {
        // Controles de período dos gráficos
        const periodSelect = document.getElementById('chartPeriod');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                this.currentPeriod = parseInt(e.target.value);
                this.updateChartsData();
            });
        }

        // Tabs de notificações
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchNotificationTab(e.target.dataset.tab);
            });
        });

        // Quick actions
        this.setupQuickActions();

        // Chart controls
        this.setupChartControls();

        // View controls (gráficos, tabela, grade)
        this.setupViewControls();

        console.log('✅ Event listeners configurados');
    }

    // Configurar ações rápidas
    setupQuickActions() {
        // Implementar ações rápidas quando os botões existirem
        const quickActions = {
            'quick-fuel': () => this.showQuickFuelEntry(),
            'quick-maintenance': () => this.showQuickMaintenanceEntry(),
            'quick-alert': () => this.showQuickAlertCreation(),
            'quick-refresh': () => this.refreshAllData()
        };

        Object.keys(quickActions).forEach(actionId => {
            const btn = document.getElementById(actionId);
            if (btn) {
                btn.addEventListener('click', quickActions[actionId]);
            }
        });
    }

    // Configurar controles de gráficos
    setupChartControls() {
        // Toggle chart type
        document.querySelectorAll('[data-chart-toggle]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chartId = e.target.dataset.chartToggle;
                this.toggleChartType(chartId);
            });
        });

        // Export chart
        document.querySelectorAll('[data-chart-export]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chartId = e.target.dataset.chartExport;
                this.exportChart(chartId);
            });
        });

        // Fullscreen chart
        document.querySelectorAll('[data-chart-fullscreen]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chartId = e.target.dataset.chartFullscreen;
                this.toggleChartFullscreen(chartId);
            });
        });
    }

    // Configurar controles de visualização (gráficos, tabela, grade)
    setupViewControls() {
        console.log('🎛️ Configurando controles de visualização...');

        // Botões de visualização
        const viewButtons = document.querySelectorAll('[data-view]');
        console.log('📊 Botões de visualização encontrados:', viewButtons.length);

        if (viewButtons.length === 0) {
            console.warn('⚠️ Nenhum botão de visualização encontrado');
            return;
        }

        viewButtons.forEach((btn, index) => {
            console.log(`Configurando botão ${index + 1}: data-view="${btn.dataset.view}"`);

            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const viewType = e.target.dataset.view || e.target.closest('[data-view]').dataset.view;
                console.log('🔄 Mudando visualização para:', viewType);
                this.switchView(viewType);
            });
        });

        console.log('✅ Controles de visualização configurados');
    }

    // Alternar entre visualizações
    switchView(viewType) {
        console.log('🔄 Alternando para visualização:', viewType);

        // Atualizar estado atual
        this.currentView = viewType;

        // Remover classe active de todos os botões
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.classList.remove('active');
        });

        // Adicionar classe active ao botão selecionado
        const activeBtn = document.querySelector(`[data-view="${viewType}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Alternar visualização
        const chartsGrid = document.querySelector('.charts-grid');
        const tableView = document.querySelector('.table-view');
        const gridView = document.querySelector('.grid-view');

        // Esconder todas as visualizações
        console.log('🔄 Escondendo todas as visualizações...');
        if (chartsGrid) {
            chartsGrid.style.display = 'none';
            console.log('📊 Charts grid escondido');
        }
        if (tableView) {
            tableView.style.display = 'none';
            console.log('📋 Table view escondido');
        }
        if (gridView) {
            gridView.style.display = 'none';
            console.log('🔲 Grid view escondido');
        }

        // Mostrar visualização selecionada
        switch (viewType) {
            case 'charts':
                console.log('📊 Mostrando visualização de gráficos...');
                if (chartsGrid) {
                    console.log('✅ Charts grid encontrado, mostrando...');
                    chartsGrid.style.display = 'grid';

                    // Verificar se os elementos canvas existem
                    const canvasElements = ['consumoChart', 'custosChart', 'performanceChart', 'alertsChart'];
                    const missingCanvas = [];

                    canvasElements.forEach(id => {
                        const element = document.getElementById(id);
                        if (!element) {
                            missingCanvas.push(id);
                        } else {
                            console.log(`✅ Canvas ${id} encontrado`);
                        }
                    });

                    if (missingCanvas.length > 0) {
                        console.error('❌ Canvas elementos ausentes:', missingCanvas);
                    }

                    // Verificar se os gráficos existem
                    console.log('🔍 Verificando gráficos existentes:', Object.keys(this.charts));

                    // Sempre recriar gráficos quando voltamos para visualização de gráficos
                    // para garantir que estejam funcionando corretamente
                    console.log('🔄 Recriando gráficos para garantir funcionamento...');

                    // Destruir gráficos existentes primeiro
                    Object.values(this.charts).forEach(chart => {
                        if (chart && typeof chart.destroy === 'function') {
                            try {
                                chart.destroy();
                            } catch (e) {
                                console.warn('⚠️ Erro ao destruir gráfico:', e);
                            }
                        }
                    });
                    this.charts = {};

                    // Recriar todos os gráficos
                    this.initializeCharts();

                    // Verificar se os gráficos estão realmente visíveis
                    setTimeout(() => {
                        console.log('🔍 Verificando visibilidade final dos gráficos...');
                        const canvasElements = ['consumoChart', 'custosChart', 'performanceChart', 'alertsChart'];
                        canvasElements.forEach(id => {
                            const element = document.getElementById(id);
                            if (element) {
                                const rect = element.getBoundingClientRect();
                                const isVisible = rect.width > 0 && rect.height > 0;
                                console.log(`Canvas ${id}: ${isVisible ? 'Visível' : 'Oculto'} (${rect.width}x${rect.height})`);
                            }
                        });

                        console.log('🔄 Redimensionando gráficos...');
                        this.resizeCharts();
                    }, 200);
                } else {
                    console.error('❌ Charts grid não encontrado!');
                }
                this.showNotification('Visualização alterada para Gráficos', 'success');
                break;

            case 'table':
                if (!tableView) {
                    this.createTableView();
                } else {
                    tableView.style.display = 'block';
                }
                this.showNotification('Visualização alterada para Tabela', 'success');
                break;

            case 'grid':
                if (!gridView) {
                    this.createGridView();
                } else {
                    gridView.style.display = 'grid';
                }
                this.showNotification('Visualização alterada para Grade', 'success');
                break;

            default:
                console.warn('Tipo de visualização desconhecido:', viewType);
        }

        console.log('✅ Visualização alterada para:', viewType);
    }

    // Redimensionar gráficos
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }

    // Criar visualização em tabela
    createTableView() {
        console.log('📊 Criando visualização em tabela...');

        const chartsSection = document.querySelector('.charts-section');
        if (!chartsSection) return;

        // Criar container da tabela
        const tableView = document.createElement('div');
        tableView.className = 'table-view';
        tableView.style.display = 'block';

        tableView.innerHTML = `
            <div class="table-container">
                <div class="table-header">
                    <h3><i class="fas fa-table"></i> Dados em Tabela</h3>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="dashboard.exportTableData()">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Período</th>
                                <th>Consumo (L)</th>
                                <th>Custo Combustível</th>
                                <th>Custo Manutenção</th>
                                <th>Km Rodados</th>
                                <th>Eficiência (Km/L)</th>
                                <th>Alertas</th>
                            </tr>
                        </thead>
                        <tbody id="dashboardTableBody">
                            <!-- Dados serão inseridos aqui -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        chartsSection.appendChild(tableView);
        this.populateTableData();

        console.log('✅ Visualização em tabela criada');
    }

    // Criar visualização em grade
    createGridView() {
        console.log('🔲 Criando visualização em grade...');

        const chartsSection = document.querySelector('.charts-section');
        if (!chartsSection) return;

        // Criar container da grade
        const gridView = document.createElement('div');
        gridView.className = 'grid-view';
        gridView.style.display = 'grid';
        gridView.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
        gridView.style.gap = '20px';

        // Criar cards para cada métrica
        const metrics = [
            { title: 'Consumo Total', value: '12,450L', icon: 'fas fa-gas-pump', color: '#3498db' },
            { title: 'Custo Total', value: 'R$ 28,450', icon: 'fas fa-dollar-sign', color: '#e74c3c' },
            { title: 'Km Rodados', value: '156,780 km', icon: 'fas fa-road', color: '#2ecc71' },
            { title: 'Eficiência Média', value: '12.5 Km/L', icon: 'fas fa-tachometer-alt', color: '#f39c12' },
            { title: 'Manutenções', value: '23 serviços', icon: 'fas fa-wrench', color: '#9b59b6' },
            { title: 'Alertas Ativos', value: '5 alertas', icon: 'fas fa-exclamation-triangle', color: '#e67e22' }
        ];

        metrics.forEach(metric => {
            const card = document.createElement('div');
            card.className = 'metric-card';
            card.innerHTML = `
                <div class="metric-header" style="background: ${metric.color}">
                    <i class="${metric.icon}"></i>
                </div>
                <div class="metric-body">
                    <h3>${metric.value}</h3>
                    <p>${metric.title}</p>
                </div>
            `;
            gridView.appendChild(card);
        });

        chartsSection.appendChild(gridView);

        console.log('✅ Visualização em grade criada');
    }

    // Preencher dados da tabela com dados reais
    populateTableData() {
        const tbody = document.getElementById('dashboardTableBody');
        if (!tbody) return;

        // Carregar dados reais
        const fuelData = this.loadFuelData();
        const maintenanceData = this.loadMaintenanceData();

        if (fuelData.length === 0 && maintenanceData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                        Nenhum dado disponível. Adicione registros de abastecimento e manutenção.
                    </td>
                </tr>
            `;
            return;
        }

        // Agrupar dados por mês (últimos 6 meses)
        const months = [];
        const now = new Date();
        for (let i = 5; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            months.push({
                name: date.toLocaleDateString('pt-BR', { month: 'short' }),
                year: date.getFullYear(),
                month: date.getMonth()
            });
        }

        const tableData = months.map(monthInfo => {
            const monthFuel = fuelData.filter(f => {
                const fuelDate = new Date(f.data);
                return fuelDate.getFullYear() === monthInfo.year && fuelDate.getMonth() === monthInfo.month;
            });

            const monthMaintenance = maintenanceData.filter(m => {
                const maintDate = new Date(m.dataInicio);
                return maintDate.getFullYear() === monthInfo.year && maintDate.getMonth() === monthInfo.month;
            });

            const consumption = monthFuel.reduce((sum, f) => sum + (f.litros || 0), 0);
            const fuelCost = monthFuel.reduce((sum, f) => sum + (f.valor || 0), 0);
            const maintenanceCost = monthMaintenance.reduce((sum, m) => sum + (m.valorReal || m.valorEstimado || 0), 0);

            return {
                period: monthInfo.name,
                consumption: consumption,
                fuelCost: fuelCost,
                maintenanceCost: maintenanceCost,
                km: 0, // Será implementado quando houver dados de quilometragem
                efficiency: 0, // Será calculado quando houver dados suficientes
                alerts: 0 // Será implementado quando houver sistema de alertas
            };
        });

        tbody.innerHTML = tableData.map(row => `
            <tr>
                <td><strong>${row.period}</strong></td>
                <td>${row.consumption.toLocaleString()}L</td>
                <td>R$ ${row.fuelCost.toLocaleString()}</td>
                <td>R$ ${row.maintenanceCost.toLocaleString()}</td>
                <td>${row.km.toLocaleString()} km</td>
                <td>${row.efficiency} Km/L</td>
                <td>
                    <span class="badge ${row.alerts > 2 ? 'bg-danger' : row.alerts > 0 ? 'bg-warning' : 'bg-success'}">
                        ${row.alerts}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    // Mostrar notificação
    showNotification(message, type = 'info') {
        console.log(`📢 Notificação (${type}):`, message);

        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `toast-notification toast-${type}`;
        notification.innerHTML = `
            <div class="toast-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Adicionar ao container de notificações
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }

        container.appendChild(notification);

        // Auto-remover após 5 segundos
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Obter ícone da notificação
    getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // Exportar dados da tabela
    exportTableData() {
        console.log('📤 Exportando dados da tabela...');
        this.showNotification('Funcionalidade de exportação será implementada em breve', 'info');
    }

    // Iniciar auto-refresh
    startAutoRefresh() {
        // Atualizar dados a cada 30 segundos
        this.refreshInterval = setInterval(() => {
            this.updateLiveStatus();
            this.updateStats();
        }, 30000);

        // Atualizar gráficos a cada 5 minutos
        setInterval(() => {
            this.loadDashboardData();
        }, 300000);

        console.log('✅ Auto-refresh iniciado');
    }

    // Parar auto-refresh
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // Atualizar dados dos gráficos
    updateChartsData() {
        // Carregar dados reais baseado no período selecionado
        this.data.fuel = this.loadFuelData();
        this.data.maintenance = this.loadMaintenanceData();
        this.data.alerts = this.loadAlertData();

        // Recriar gráficos
        this.createConsumoChart();
        this.createPerformanceChart();
        this.createAlertsChart();

        console.log(`✅ Gráficos atualizados para período de ${this.currentPeriod} dias`);
    }

    // Alternar tab de notificações
    switchNotificationTab(tab) {
        // Remover classe active de todas as tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Adicionar classe active na tab selecionada
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // Filtrar notificações
        this.filterNotifications(tab);
    }

    // Filtrar notificações
    filterNotifications(category) {
        const items = document.querySelectorAll('.notification-item');
        items.forEach(item => {
            if (category === 'all') {
                item.style.display = 'flex';
            } else {
                const itemCategory = item.dataset.category;
                item.style.display = itemCategory === category ? 'flex' : 'none';
            }
        });
    }

    // Marcar notificação como lida
    markAsRead(notificationId) {
        const item = document.querySelector(`[data-id="${notificationId}"]`);
        if (item) {
            item.classList.remove('unread');
            this.showNotification('Notificação marcada como lida', 'success');
        }
    }

    // Dispensar notificação
    dismissNotification(notificationId) {
        const item = document.querySelector(`[data-id="${notificationId}"]`);
        if (item) {
            item.style.animation = 'slideOut 0.3s ease-out';
            setTimeout(() => {
                item.remove();
                this.showNotification('Notificação dispensada', 'info');
            }, 300);
        }
    }

    // Marcar todas como lidas
    markAllAsRead() {
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            item.classList.remove('unread');
        });
        this.updateElement('notificationsCount', '0 novas');
        this.showNotification('Todas as notificações foram marcadas como lidas', 'success');
    }

    // Atualizar notificações
    refreshNotifications() {
        this.loadNotifications();
        this.showNotification('Notificações atualizadas', 'success');
    }
    // Métodos de ações rápidas
    showQuickFuelEntry() {
        console.log('⛽ Abrindo entrada rápida de combustível...');
        this.showNotification('Abrindo formulário de abastecimento...', 'info');

        // Criar modal de abastecimento rápido
        const modalHtml = `
            <div class="modal fade" id="quickFuelModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-gas-pump me-2"></i>Abastecimento Rápido
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="quickFuelForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Veículo</label>
                                        <select class="form-select" id="fuelVehicle" required>
                                            <option value="">Selecionar veículo...</option>
                                            <option value="ABC-1234">ABC-1234 - Caminhão</option>
                                            <option value="DEF-5678">DEF-5678 - Van</option>
                                            <option value="GHI-9012">GHI-9012 - Carro</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Combustível</label>
                                        <select class="form-select" id="fuelType" required>
                                            <option value="">Tipo...</option>
                                            <option value="diesel">Diesel</option>
                                            <option value="gasolina">Gasolina</option>
                                            <option value="etanol">Etanol</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Litros</label>
                                        <input type="number" class="form-control" id="fuelLiters"
                                               placeholder="0.00" step="0.01" min="0" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Valor Total (R$)</label>
                                        <input type="number" class="form-control" id="fuelCost"
                                               placeholder="0.00" step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Posto/Local</label>
                                    <input type="text" class="form-control" id="fuelStation"
                                           placeholder="Nome do posto ou local">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Observações</label>
                                    <textarea class="form-control" id="fuelNotes" rows="2"
                                              placeholder="Observações adicionais..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancelar
                            </button>
                            <button type="button" class="btn btn-primary" onclick="dashboard.saveFuelEntry()">
                                <i class="fas fa-save me-1"></i>Registrar Abastecimento
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal existente se houver
        const existingModal = document.getElementById('quickFuelModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Adicionar modal ao DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('quickFuelModal'));
        modal.show();
    }

    // Salvar entrada de combustível
    saveFuelEntry() {
        console.log('💾 Salvando entrada de combustível...');

        const form = document.getElementById('quickFuelForm');
        const formData = new FormData(form);

        const fuelData = {
            vehicle: document.getElementById('fuelVehicle').value,
            type: document.getElementById('fuelType').value,
            liters: parseFloat(document.getElementById('fuelLiters').value),
            cost: parseFloat(document.getElementById('fuelCost').value),
            station: document.getElementById('fuelStation').value,
            notes: document.getElementById('fuelNotes').value,
            date: new Date().toISOString()
        };

        // Validar dados
        if (!fuelData.vehicle || !fuelData.type || !fuelData.liters || !fuelData.cost) {
            this.showNotification('Por favor, preencha todos os campos obrigatórios', 'error');
            return;
        }

        // Simular salvamento (aqui você integraria com sua API)
        console.log('📊 Dados do abastecimento:', fuelData);

        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('quickFuelModal'));
        modal.hide();

        // Mostrar sucesso
        this.showNotification(`Abastecimento registrado: ${fuelData.liters}L por R$ ${fuelData.cost.toFixed(2)}`, 'success');

        // Atualizar dados do dashboard
        setTimeout(() => {
            this.refreshAllData();
        }, 1000);
    }

    showQuickMaintenanceEntry() {
        console.log('🔧 Abrindo registro rápido de manutenção...');
        this.showNotification('Abrindo formulário de manutenção...', 'info');

        // Criar modal de manutenção rápida
        const modalHtml = `
            <div class="modal fade" id="quickMaintenanceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-wrench me-2"></i>Registrar Manutenção
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="quickMaintenanceForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Veículo</label>
                                        <select class="form-select" id="maintenanceVehicle" required>
                                            <option value="">Selecionar veículo...</option>
                                            <option value="ABC-1234">ABC-1234 - Caminhão</option>
                                            <option value="DEF-5678">DEF-5678 - Van</option>
                                            <option value="GHI-9012">GHI-9012 - Carro</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Tipo de Manutenção</label>
                                        <select class="form-select" id="maintenanceType" required>
                                            <option value="">Selecionar tipo...</option>
                                            <option value="preventiva">Preventiva</option>
                                            <option value="corretiva">Corretiva</option>
                                            <option value="emergencial">Emergencial</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Serviço</label>
                                        <select class="form-select" id="maintenanceService" required>
                                            <option value="">Tipo de serviço...</option>
                                            <option value="troca-oleo">Troca de Óleo</option>
                                            <option value="revisao">Revisão Geral</option>
                                            <option value="pneus">Pneus</option>
                                            <option value="freios">Freios</option>
                                            <option value="motor">Motor</option>
                                            <option value="eletrica">Elétrica</option>
                                            <option value="outros">Outros</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Custo (R$)</label>
                                        <input type="number" class="form-control" id="maintenanceCost"
                                               placeholder="0.00" step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Oficina/Responsável</label>
                                    <input type="text" class="form-control" id="maintenanceProvider"
                                           placeholder="Nome da oficina ou responsável">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Descrição do Serviço</label>
                                    <textarea class="form-control" id="maintenanceDescription" rows="3"
                                              placeholder="Descreva o serviço realizado..." required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Quilometragem</label>
                                        <input type="number" class="form-control" id="maintenanceKm"
                                               placeholder="Km atual do veículo">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Próxima Manutenção (Km)</label>
                                        <input type="number" class="form-control" id="nextMaintenanceKm"
                                               placeholder="Km para próxima manutenção">
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancelar
                            </button>
                            <button type="button" class="btn btn-warning" onclick="dashboard.saveMaintenanceEntry()">
                                <i class="fas fa-save me-1"></i>Registrar Manutenção
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal existente se houver
        const existingModal = document.getElementById('quickMaintenanceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Adicionar modal ao DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('quickMaintenanceModal'));
        modal.show();
    }

    // Salvar entrada de manutenção
    saveMaintenanceEntry() {
        console.log('💾 Salvando entrada de manutenção...');

        const maintenanceData = {
            vehicle: document.getElementById('maintenanceVehicle').value,
            type: document.getElementById('maintenanceType').value,
            service: document.getElementById('maintenanceService').value,
            cost: parseFloat(document.getElementById('maintenanceCost').value),
            provider: document.getElementById('maintenanceProvider').value,
            description: document.getElementById('maintenanceDescription').value,
            km: parseInt(document.getElementById('maintenanceKm').value),
            nextKm: parseInt(document.getElementById('nextMaintenanceKm').value),
            date: new Date().toISOString()
        };

        // Validar dados
        if (!maintenanceData.vehicle || !maintenanceData.type || !maintenanceData.service ||
            !maintenanceData.cost || !maintenanceData.description) {
            this.showNotification('Por favor, preencha todos os campos obrigatórios', 'error');
            return;
        }

        // Simular salvamento
        console.log('🔧 Dados da manutenção:', maintenanceData);

        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('quickMaintenanceModal'));
        modal.hide();

        // Mostrar sucesso
        this.showNotification(`Manutenção registrada: ${maintenanceData.service} - R$ ${maintenanceData.cost.toFixed(2)}`, 'success');

        // Atualizar dados
        setTimeout(() => {
            this.refreshAllData();
        }, 1000);
    }

    showQuickAlertCreation() {
        console.log('🚨 Abrindo criação rápida de alerta...');
        this.showNotification('Abrindo formulário de alerta...', 'info');

        // Criar modal de alerta rápido
        const modalHtml = `
            <div class="modal fade" id="quickAlertModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>Criar Alerta
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="quickAlertForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Tipo de Alerta</label>
                                        <select class="form-select" id="alertType" required>
                                            <option value="">Selecionar tipo...</option>
                                            <option value="manutencao">Manutenção Vencida</option>
                                            <option value="combustivel">Combustível Baixo</option>
                                            <option value="documento">Documento Vencendo</option>
                                            <option value="seguro">Seguro Vencendo</option>
                                            <option value="inspecao">Inspeção Vencida</option>
                                            <option value="outros">Outros</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Prioridade</label>
                                        <select class="form-select" id="alertPriority" required>
                                            <option value="">Selecionar prioridade...</option>
                                            <option value="baixa">🟢 Baixa</option>
                                            <option value="media">🟡 Média</option>
                                            <option value="alta">🟠 Alta</option>
                                            <option value="critica">🔴 Crítica</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Veículo (Opcional)</label>
                                    <select class="form-select" id="alertVehicle">
                                        <option value="">Todos os veículos</option>
                                        <option value="ABC-1234">ABC-1234 - Caminhão</option>
                                        <option value="DEF-5678">DEF-5678 - Van</option>
                                        <option value="GHI-9012">GHI-9012 - Carro</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Título do Alerta</label>
                                    <input type="text" class="form-control" id="alertTitle"
                                           placeholder="Título descritivo do alerta" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Descrição</label>
                                    <textarea class="form-control" id="alertDescription" rows="3"
                                              placeholder="Descreva o alerta em detalhes..." required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Data de Vencimento</label>
                                        <input type="date" class="form-control" id="alertDueDate">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Lembrar em (dias)</label>
                                        <input type="number" class="form-control" id="alertReminder"
                                               placeholder="Ex: 7" min="1" max="365">
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancelar
                            </button>
                            <button type="button" class="btn btn-danger" onclick="dashboard.saveAlertEntry()">
                                <i class="fas fa-save me-1"></i>Criar Alerta
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal existente se houver
        const existingModal = document.getElementById('quickAlertModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Adicionar modal ao DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('quickAlertModal'));
        modal.show();
    }

    // Salvar entrada de alerta
    saveAlertEntry() {
        console.log('💾 Salvando entrada de alerta...');

        const alertData = {
            type: document.getElementById('alertType').value,
            priority: document.getElementById('alertPriority').value,
            vehicle: document.getElementById('alertVehicle').value,
            title: document.getElementById('alertTitle').value,
            description: document.getElementById('alertDescription').value,
            dueDate: document.getElementById('alertDueDate').value,
            reminder: parseInt(document.getElementById('alertReminder').value) || 0,
            createdDate: new Date().toISOString(),
            status: 'ativo'
        };

        // Validar dados
        if (!alertData.type || !alertData.priority || !alertData.title || !alertData.description) {
            this.showNotification('Por favor, preencha todos os campos obrigatórios', 'error');
            return;
        }

        // Simular salvamento
        console.log('🚨 Dados do alerta:', alertData);

        // Fechar modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('quickAlertModal'));
        modal.hide();

        // Mostrar sucesso
        const priorityIcon = {
            'baixa': '🟢',
            'media': '🟡',
            'alta': '🟠',
            'critica': '🔴'
        };

        this.showNotification(`${priorityIcon[alertData.priority]} Alerta criado: ${alertData.title}`, 'success');

        // Atualizar dados
        setTimeout(() => {
            this.refreshAllData();
        }, 1000);
    }

    refreshAllData() {
        console.log('🔄 Atualizando todos os dados...');
        this.showNotification('Atualizando dados...', 'info');

        // Recarregar dados
        this.initializeData();

        // Recriar gráficos
        this.initializeCharts();

        // Atualizar estatísticas
        this.loadDashboardData();

        // Mostrar notificação de sucesso
        setTimeout(() => {
            this.showNotification('Todos os dados foram atualizados com sucesso!', 'success');
        }, 1000);
    }

    // Verificar se usuário é administrador
    isAdmin() {
        if (typeof auth !== 'undefined' && auth.getCurrentUser) {
            const currentUser = auth.getCurrentUser();
            return currentUser && (currentUser.role === 'admin' || currentUser.permissions?.includes('all'));
        }
        return false;
    }

    // Mostrar modal de confirmação para deletar dados
    showDeleteDataModal() {
        console.log('🗑️ Abrindo modal de deletar dados...');

        if (!this.isAdmin()) {
            this.showNotification('❌ Acesso negado! Apenas administradores podem deletar dados.', 'error');
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="deleteDataModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-trash-alt me-2"></i>Deletar Dados do Sistema
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>ATENÇÃO:</strong> Esta ação é irreversível! Os dados deletados não poderão ser recuperados.
                            </div>

                            <h6 class="mb-3">Selecione os tipos de dados que deseja deletar:</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="deleteFuel">
                                        <label class="form-check-label" for="deleteFuel">
                                            <i class="fas fa-gas-pump text-primary me-2"></i>
                                            <strong>Dados de Abastecimento</strong>
                                            <br><small class="text-muted">Registros de combustível, custos e consumo</small>
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="deleteMaintenance">
                                        <label class="form-check-label" for="deleteMaintenance">
                                            <i class="fas fa-wrench text-warning me-2"></i>
                                            <strong>Dados de Manutenção</strong>
                                            <br><small class="text-muted">Histórico de manutenções e revisões</small>
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="deleteAlerts">
                                        <label class="form-check-label" for="deleteAlerts">
                                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                            <strong>Alertas e Notificações</strong>
                                            <br><small class="text-muted">Todos os alertas criados no sistema</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="deleteWashing">
                                        <label class="form-check-label" for="deleteWashing">
                                            <i class="fas fa-tint text-info me-2"></i>
                                            <strong>Dados de Lavagem</strong>
                                            <br><small class="text-muted">Registros de lavagem e limpeza</small>
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="deleteReports">
                                        <label class="form-check-label" for="deleteReports">
                                            <i class="fas fa-chart-bar text-success me-2"></i>
                                            <strong>Relatórios Salvos</strong>
                                            <br><small class="text-muted">Relatórios personalizados e histórico</small>
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="deleteAll">
                                        <label class="form-check-label" for="deleteAll">
                                            <i class="fas fa-database text-dark me-2"></i>
                                            <strong>TODOS OS DADOS</strong>
                                            <br><small class="text-danger">⚠️ Limpar completamente o sistema</small>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="mb-3">
                                <label class="form-label">Para confirmar, digite <strong>DELETAR</strong> no campo abaixo:</label>
                                <input type="text" class="form-control" id="deleteConfirmation"
                                       placeholder="Digite DELETAR para confirmar">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancelar
                            </button>
                            <button type="button" class="btn btn-danger" onclick="dashboard.executeDataDeletion()">
                                <i class="fas fa-trash-alt me-1"></i>Deletar Dados Selecionados
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remover modal existente se houver
        const existingModal = document.getElementById('deleteDataModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Adicionar modal ao DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Adicionar event listener para o checkbox "Todos os dados"
        document.getElementById('deleteAll').addEventListener('change', function() {
            const checkboxes = ['deleteFuel', 'deleteMaintenance', 'deleteAlerts', 'deleteWashing', 'deleteReports'];
            checkboxes.forEach(id => {
                document.getElementById(id).checked = this.checked;
                document.getElementById(id).disabled = this.checked;
            });
        });

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('deleteDataModal'));
        modal.show();
    }

    // Executar deleção de dados
    executeDataDeletion() {
        console.log('🗑️ Executando deleção de dados...');

        if (!this.isAdmin()) {
            this.showNotification('❌ Acesso negado! Apenas administradores podem deletar dados.', 'error');
            return;
        }

        // Verificar confirmação
        const confirmation = document.getElementById('deleteConfirmation').value;
        if (confirmation !== 'DELETAR') {
            this.showNotification('❌ Digite "DELETAR" para confirmar a operação', 'error');
            return;
        }

        // Verificar se pelo menos um tipo foi selecionado
        const checkboxes = ['deleteFuel', 'deleteMaintenance', 'deleteAlerts', 'deleteWashing', 'deleteReports', 'deleteAll'];
        const selectedTypes = checkboxes.filter(id => document.getElementById(id).checked);

        if (selectedTypes.length === 0) {
            this.showNotification('❌ Selecione pelo menos um tipo de dados para deletar', 'error');
            return;
        }

        // Confirmação final
        const typesText = selectedTypes.map(id => {
            switch(id) {
                case 'deleteFuel': return 'Abastecimento';
                case 'deleteMaintenance': return 'Manutenção';
                case 'deleteAlerts': return 'Alertas';
                case 'deleteWashing': return 'Lavagem';
                case 'deleteReports': return 'Relatórios';
                case 'deleteAll': return 'TODOS OS DADOS';
                default: return id;
            }
        }).join(', ');

        if (!confirm(`⚠️ CONFIRMAÇÃO FINAL ⚠️\n\nVocê está prestes a deletar:\n${typesText}\n\nEsta ação é IRREVERSÍVEL!\n\nDeseja continuar?`)) {
            return;
        }

        // Executar deleção
        this.showNotification('🗑️ Deletando dados selecionados...', 'info');

        let deletedCount = 0;
        const deletionResults = [];

        try {
            // Deletar dados de abastecimento
            if (document.getElementById('deleteFuel').checked || document.getElementById('deleteAll').checked) {
                localStorage.removeItem('fuelData');
                localStorage.removeItem('fuelRecords');
                localStorage.removeItem('consumptionData');
                deletedCount++;
                deletionResults.push('✅ Dados de abastecimento deletados');
                console.log('🗑️ Dados de abastecimento deletados');
            }

            // Deletar dados de manutenção
            if (document.getElementById('deleteMaintenance').checked || document.getElementById('deleteAll').checked) {
                localStorage.removeItem('maintenanceData');
                localStorage.removeItem('maintenanceRecords');
                localStorage.removeItem('maintenanceHistory');
                deletedCount++;
                deletionResults.push('✅ Dados de manutenção deletados');
                console.log('🗑️ Dados de manutenção deletados');
            }

            // Deletar alertas
            if (document.getElementById('deleteAlerts').checked || document.getElementById('deleteAll').checked) {
                localStorage.removeItem('alertsData');
                localStorage.removeItem('notifications');
                localStorage.removeItem('systemAlerts');
                deletedCount++;
                deletionResults.push('✅ Alertas e notificações deletados');
                console.log('🗑️ Alertas deletados');
            }

            // Deletar dados de lavagem
            if (document.getElementById('deleteWashing').checked || document.getElementById('deleteAll').checked) {
                localStorage.removeItem('washingData');
                localStorage.removeItem('washingRecords');
                localStorage.removeItem('cleaningHistory');
                deletedCount++;
                deletionResults.push('✅ Dados de lavagem deletados');
                console.log('🗑️ Dados de lavagem deletados');
            }

            // Deletar relatórios
            if (document.getElementById('deleteReports').checked || document.getElementById('deleteAll').checked) {
                localStorage.removeItem('reportsData');
                localStorage.removeItem('savedReports');
                localStorage.removeItem('reportHistory');
                localStorage.removeItem('customReports');
                deletedCount++;
                deletionResults.push('✅ Relatórios salvos deletados');
                console.log('🗑️ Relatórios deletados');
            }

            // Se "Todos os dados" foi selecionado, limpar dados adicionais
            if (document.getElementById('deleteAll').checked) {
                // Manter apenas dados de usuários e autenticação
                const preservedKeys = ['users', 'currentUser'];
                const allKeys = Object.keys(localStorage);

                allKeys.forEach(key => {
                    if (!preservedKeys.includes(key)) {
                        localStorage.removeItem(key);
                    }
                });

                deletionResults.push('🗑️ Limpeza completa do sistema realizada');
                console.log('🗑️ Limpeza completa realizada');
            }

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteDataModal'));
            modal.hide();

            // Mostrar resultado
            const resultMessage = `🗑️ Deleção concluída!\n\n${deletionResults.join('\n')}\n\n📊 Total: ${deletedCount} tipo(s) de dados deletados`;

            this.showNotification('✅ Dados deletados com sucesso!', 'success');

            // Mostrar detalhes em um alert
            setTimeout(() => {
                alert(resultMessage);

                // Recarregar dados do dashboard
                this.refreshAllData();
            }, 1000);

        } catch (error) {
            console.error('❌ Erro ao deletar dados:', error);
            this.showNotification('❌ Erro ao deletar dados: ' + error.message, 'error');
        }
    }

    // Função para lidar com ações rápidas chamadas pelos botões HTML
    openQuickAction(actionType) {
        console.log('🚀 Abrindo ação rápida:', actionType);

        switch(actionType) {
            case 'fuel':
                this.showQuickFuelEntry();
                break;
            case 'maintenance':
                this.showQuickMaintenanceEntry();
                break;
            case 'alert':
                this.showQuickAlertCreation();
                break;
            default:
                console.warn('⚠️ Tipo de ação não reconhecido:', actionType);
                this.showNotification('Ação não disponível', 'warning');
        }
    }

    // Métodos de controle de gráficos
    toggleChartType(chartId) {
        const chart = this.charts[chartId];
        if (chart) {
            // Implementar toggle entre tipos de gráfico
            this.showNotification(`Tipo de gráfico ${chartId} alterado`, 'info');
        }
    }

    exportChart(chartId) {
        const chart = this.charts[chartId];
        if (chart) {
            const url = chart.toBase64Image();
            const link = document.createElement('a');
            link.download = `grafico-${chartId}.png`;
            link.href = url;
            link.click();
            this.showNotification(`Gráfico ${chartId} exportado`, 'success');
        }
    }

    toggleChartFullscreen(chartId) {
        const container = document.querySelector(`#${chartId}`).closest('.chart-container');
        if (container) {
            container.classList.toggle('fullscreen');
            this.showNotification(`Modo tela cheia ${container.classList.contains('fullscreen') ? 'ativado' : 'desativado'}`, 'info');
        }
    }

    // Atualizar tabela
    refreshTable(tableType) {
        switch (tableType) {
            case 'revisoes':
                this.loadProximasRevisoes();
                this.showNotification('Tabela de revisões atualizada', 'success');
                break;
            default:
                this.showNotification('Tabela atualizada', 'success');
        }
    }

    // Mostrar notificação toast
    showNotification(message, type = 'info') {
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `toast-notification ${type}`;
        notification.innerHTML = `
            <div class="toast-content">
                <i class="fas ${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Adicionar ao container de notificações
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }

        container.appendChild(notification);

        // Auto-remover após 5 segundos
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // Obter ícone do toast
    getToastIcon(type) {
        switch (type) {
            case 'success': return 'fa-check-circle';
            case 'warning': return 'fa-exclamation-triangle';
            case 'error': return 'fa-times-circle';
            case 'info': return 'fa-info-circle';
            default: return 'fa-bell';
        }
    }

    // Destruir dashboard
    destroy() {
        this.stopAutoRefresh();

        // Destruir todos os gráficos
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });

        console.log('✅ Dashboard destruído');
    }
}

// Variável global para acesso aos métodos
let dashboard;

// Função para inicializar o dashboard
function initializeDashboard() {
    console.log('🔄 Inicializando dashboard...');

    // Verificar se estamos na página do dashboard
    const consumoChart = document.getElementById('consumoChart');
    const proximasRevisoes = document.getElementById('proximasRevisoes');
    const notificationsList = document.getElementById('notificationsList');

    console.log('Elementos encontrados:', {
        consumoChart: !!consumoChart,
        proximasRevisoes: !!proximasRevisoes,
        notificationsList: !!notificationsList,
        chartJsLoaded: typeof Chart !== 'undefined'
    });

    if (consumoChart) {
        // Verificar se Chart.js está carregado
        if (typeof Chart === 'undefined') {
            console.log('⏳ Aguardando Chart.js carregar...');
            setTimeout(initializeDashboard, 100);
            return;
        }

        console.log('✅ Inicializando Dashboard...');
        dashboard = new Dashboard();

        // Expor métodos globalmente para uso em onclick
        window.dashboard = dashboard;
        window.refreshTable = (type) => dashboard.refreshTable(type);
        window.markAllAsRead = () => dashboard.markAllAsRead();
        window.refreshNotifications = () => dashboard.refreshNotifications();

        console.log('✅ Dashboard inicializado e métodos expostos globalmente');
    } else {
        console.log('ℹ️ Não é a página do dashboard, pulando inicialização');
    }
}

// Inicializar dashboard quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 DOM carregado, verificando elementos...');

    // Aguardar um pouco para garantir que todos os scripts foram carregados
    setTimeout(initializeDashboard, 50);
});

// Função para toggle da sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

// Função para toggle do submenu
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar submenus
    initializeSubmenus();

    // Inicializar dropdown do usuário
    initializeUserDropdown();
});

function initializeSubmenus() {
    const submenuItems = document.querySelectorAll('.has-submenu > a');

    submenuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const parent = this.parentElement;
            const submenu = parent.querySelector('.submenu');

            // Fechar outros submenus abertos
            const otherOpenMenus = document.querySelectorAll('.has-submenu.open');
            otherOpenMenus.forEach(menu => {
                if (menu !== parent) {
                    menu.classList.remove('open');
                    const otherSubmenu = menu.querySelector('.submenu');
                    if (otherSubmenu) {
                        otherSubmenu.classList.remove('open');
                    }
                }
            });

            // Toggle do submenu atual
            parent.classList.toggle('open');
            if (submenu) {
                submenu.classList.toggle('open');
            }
        });
    });

    // Fechar submenus ao clicar fora
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.has-submenu')) {
            const openMenus = document.querySelectorAll('.has-submenu.open');
            openMenus.forEach(menu => {
                menu.classList.remove('open');
                const submenu = menu.querySelector('.submenu');
                if (submenu) {
                    submenu.classList.remove('open');
                }
            });
        }
    });
}

function initializeUserDropdown() {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.querySelector('.dropdown-content');

    if (userMenu && dropdown) {
        // Toggle dropdown ao clicar
        userMenu.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });

        // Fechar dropdown ao clicar fora
        document.addEventListener('click', function(e) {
            if (!userMenu.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });
    }
}

// Responsividade da sidebar em mobile
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.getElementById('sidebar');
    
    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
    }
    
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            sidebar.classList.add('collapsed');
        } else {
            sidebar.classList.remove('collapsed');
        }
    });
});

// Adicionar classe CSS para badges
const style = document.createElement('style');
style.textContent = `
    .badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.375rem;
    }
    
    .badge.bg-success {
        color: #fff;
        background-color: #198754;
    }
    
    .badge.bg-warning {
        color: #000;
        background-color: #ffc107;
    }
    
    .badge.bg-danger {
        color: #fff;
        background-color: #dc3545;
    }
    
    .text-muted {
        color: #6c757d !important;
    }
`;
document.head.appendChild(style);

// Função global para ações rápidas (chamada pelos botões HTML)
window.openQuickAction = function(actionType) {
    console.log('🚀 Chamando ação rápida global:', actionType);

    if (window.dashboard && typeof window.dashboard.openQuickAction === 'function') {
        window.dashboard.openQuickAction(actionType);
    } else {
        console.error('❌ Dashboard não está disponível');
        // Fallback: mostrar notificação simples
        alert('Dashboard não está carregado. Recarregue a página.');
    }
};

// Função global para deletar dados (apenas administradores)
window.showDeleteDataModal = function() {
    console.log('🗑️ Chamando modal de deletar dados...');

    if (window.dashboard && typeof window.dashboard.showDeleteDataModal === 'function') {
        window.dashboard.showDeleteDataModal();
    } else {
        console.error('❌ Dashboard não está disponível');
        alert('Dashboard não está carregado. Recarregue a página.');
    }
};
