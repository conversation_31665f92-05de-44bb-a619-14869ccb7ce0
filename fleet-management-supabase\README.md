# Sistema de Gestão de Frotas - Supabase + Node.js + React

## 🚀 Arquitetura do Projeto

Este projeto migra o sistema de gestão de frotas para uma arquitetura moderna usando:

- **Backend**: Node.js + Express.js + Supabase
- **Frontend**: React + TypeScript + Vite
- **Banco de Dados**: PostgreSQL (Supabase)
- **Autenticação**: Supabase Auth
- **Deploy**: Vercel (Frontend) + Railway/Render (Backend)

## 📁 Estrutura do Projeto

```
fleet-management-supabase/
├── backend/                 # API Node.js
│   ├── src/
│   │   ├── controllers/     # Controladores da API
│   │   ├── middleware/      # Middlewares (auth, cors, etc)
│   │   ├── routes/          # Rotas da API
│   │   ├── services/        # Lógica de negócio
│   │   ├── utils/           # Utilitários
│   │   └── app.js           # Configuração do Express
│   ├── package.json
│   └── .env.example
├── frontend/                # Aplicação React
│   ├── src/
│   │   ├── components/      # Componentes React
│   │   ├── pages/           # Páginas da aplicação
│   │   ├── hooks/           # Custom hooks
│   │   ├── services/        # Serviços de API
│   │   ├── utils/           # Utilitários
│   │   └── App.tsx          # Componente principal
│   ├── package.json
│   └── vite.config.ts
├── database/                # Scripts SQL e migrações
│   ├── migrations/          # Migrações do banco
│   ├── seeds/               # Dados iniciais
│   └── schema.sql           # Schema completo
└── docs/                    # Documentação
    ├── api.md               # Documentação da API
    └── deployment.md        # Guia de deploy
```

## 🗄️ Modelo de Dados

### Entidades Principais

1. **users** - Usuários do sistema
2. **vehicles** - Veículos da frota
3. **fuel_records** - Registros de abastecimento
4. **maintenance_records** - Registros de manutenção
5. **washing_records** - Registros de lavagem
6. **revision_records** - Registros de revisão
7. **financial_transactions** - Controle financeiro

## 🔧 Funcionalidades

### Módulos do Sistema

- ✅ **Autenticação e Usuários**
  - Login/Logout
  - Gestão de usuários (Admin, Supervisor, Operador)
  - Controle de permissões

- ✅ **Gestão de Veículos**
  - Cadastro de veículos
  - Controle de status
  - Histórico completo

- ✅ **Abastecimento**
  - Registro de abastecimentos
  - Controle de consumo
  - Cupom e nota fiscal
  - Motorista responsável

- ✅ **Manutenção**
  - Agendamento de manutenções
  - Controle de status
  - Valores estimados e reais
  - Oficinas e contatos

- ✅ **Lavagem**
  - Registro de lavagens
  - Tipos de serviço
  - Controle de custos
  - Locais e responsáveis

- ✅ **Revisão**
  - Agendamento de revisões
  - Calendário integrado
  - Controle de quilometragem
  - Itens de revisão

- ✅ **Controle Financeiro**
  - Fluxo de caixa
  - Sincronização automática
  - Relatórios mensais
  - Transações manuais

- ✅ **Dashboard e Relatórios**
  - Gráficos interativos
  - Estatísticas em tempo real
  - Exportação de dados
  - Alertas e notificações

## 🚀 Como Executar

### Pré-requisitos

- Node.js 18+
- npm ou yarn
- Conta no Supabase

### Backend

```bash
cd backend
npm install
cp .env.example .env
# Configure as variáveis de ambiente
npm run dev
```

### Frontend

```bash
cd frontend
npm install
npm run dev
```

## 🔐 Configuração do Supabase

1. Criar projeto no Supabase
2. Executar migrações do banco
3. Configurar autenticação
4. Obter chaves da API

## 📝 Credenciais Padrão

- **Admin**: <EMAIL> / admin123
- **Supervisor**: <EMAIL> / super123
- **Operador**: <EMAIL> / oper123

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.
