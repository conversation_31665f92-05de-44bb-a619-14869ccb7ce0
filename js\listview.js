// Listview JavaScript - Sistema de Gestão de Frotas
class ListView {
    constructor() {
        console.log('🚀 Iniciando ListView...');
        
        this.data = {
            fuel: [],
            maintenance: [],
            washing: []
        };
        
        this.filters = {
            period: 30,
            category: 'all',
            vehicle: 'all'
        };
        
        this.visibleSections = {
            fuel: true,
            maintenance: true,
            washing: true
        };
        
        this.initializeData();
        this.setupEventListeners();
        this.populateVehicleFilter();
        this.renderAll();
        
        console.log('✅ ListView inicializado com sucesso');
    }
    
    // Inicializar dados reais do sistema
    initializeData() {
        console.log('📊 Inicializando dados reais...');

        // Carregar dados reais do localStorage
        this.data.fuel = this.loadFuelDataFromStorage();
        this.data.maintenance = this.loadMaintenanceDataFromStorage();
        this.data.washing = this.loadWashingDataFromStorage();

        // Se não há dados reais, gerar dados de demonstração
        if (this.data.fuel.length === 0 && this.data.maintenance.length === 0 && this.data.washing.length === 0) {
            console.log('⚠️ Nenhum dado real encontrado, gerando dados de demonstração...');
            this.generateSampleData();
        }

        console.log('✅ Dados inicializados:', {
            fuel: this.data.fuel.length,
            maintenance: this.data.maintenance.length,
            washing: this.data.washing.length
        });
    }

    // Gerar dados de demonstração
    generateSampleData() {
        const today = new Date();
        const vehicles = ['ABC-1234', 'DEF-5678', 'GHI-9012'];

        // Dados de combustível
        for (let i = 0; i < 10; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - Math.floor(Math.random() * 30));

            this.data.fuel.push({
                date: date.toISOString().split('T')[0],
                vehicle: vehicles[Math.floor(Math.random() * vehicles.length)],
                liters: 30 + Math.random() * 20,
                pricePerLiter: 5.20 + Math.random() * 0.50,
                total: 0, // Será calculado
                station: `Posto ${Math.floor(Math.random() * 5) + 1}`,
                km: 10000 + Math.floor(Math.random() * 50000)
            });
        }

        // Calcular total para combustível
        this.data.fuel.forEach(item => {
            item.total = item.liters * item.pricePerLiter;
        });

        // Dados de manutenção
        const maintenanceTypes = ['Troca de óleo', 'Revisão', 'Pneus', 'Freios', 'Filtros'];
        for (let i = 0; i < 8; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - Math.floor(Math.random() * 60));

            this.data.maintenance.push({
                date: date.toISOString().split('T')[0],
                vehicle: vehicles[Math.floor(Math.random() * vehicles.length)],
                type: maintenanceTypes[Math.floor(Math.random() * maintenanceTypes.length)],
                description: 'Manutenção preventiva',
                cost: 100 + Math.random() * 500,
                status: Math.random() > 0.8 ? 'Pendente' : 'Concluída',
                km: 10000 + Math.floor(Math.random() * 50000)
            });
        }

        // Dados de lavagem
        const washTypes = ['Simples', 'Completa', 'Enceramento', 'Detalhamento'];
        for (let i = 0; i < 6; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() - Math.floor(Math.random() * 45));

            this.data.washing.push({
                date: date.toISOString().split('T')[0],
                vehicle: vehicles[Math.floor(Math.random() * vehicles.length)],
                type: washTypes[Math.floor(Math.random() * washTypes.length)],
                cost: 15 + Math.random() * 35,
                location: Math.random() > 0.5 ? 'Interno' : 'Externo',
                observations: Math.random() > 0.7 ? 'Lavagem completa realizada' : ''
            });
        }

        console.log('✅ Dados de demonstração gerados');
    }

    // Carregar dados de abastecimento do localStorage
    loadFuelDataFromStorage() {
        const saved = localStorage.getItem('abastecimentos');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                console.log('📊 Dados de abastecimento carregados:', data.length, 'registros');

                // Normalizar dados para o formato esperado pelo ListView
                return data.map(item => ({
                    date: item.data || item.date,
                    vehicle: item.veiculo || item.vehicle || 'Não informado',
                    liters: parseFloat(item.litros || item.liters || 0),
                    pricePerLiter: parseFloat(item.valorLitro || item.pricePerLiter || 0),
                    total: parseFloat(item.valorTotal || item.total || 0),
                    station: item.posto || item.station || 'Não informado',
                    km: parseInt(item.kmAtual || item.km || 0)
                }));
            } catch (error) {
                console.error('Erro ao carregar dados de abastecimento:', error);
            }
        }
        console.log('⚠️ Nenhum dado de abastecimento encontrado');
        return [];
    }

    // Carregar dados de manutenção do localStorage
    loadMaintenanceDataFromStorage() {
        const saved = localStorage.getItem('manutencoes');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                console.log('🔧 Dados de manutenção carregados:', data.length, 'registros');

                // Normalizar dados para o formato esperado pelo ListView
                return data.map(item => ({
                    date: item.data || item.date,
                    vehicle: item.veiculo || item.vehicle || 'Não informado',
                    type: item.tipo || item.type || 'Não informado',
                    description: item.descricao || item.description || 'Não informado',
                    cost: parseFloat(item.valor || item.cost || 0),
                    status: item.status || 'Concluída',
                    km: parseInt(item.kmAtual || item.km || 0)
                }));
            } catch (error) {
                console.error('Erro ao carregar dados de manutenção:', error);
            }
        }
        console.log('⚠️ Nenhum dado de manutenção encontrado');
        return [];
    }

    // Carregar dados de lavagem do localStorage
    loadWashingDataFromStorage() {
        const saved = localStorage.getItem('lavagens');
        if (saved) {
            try {
                const data = JSON.parse(saved);
                console.log('🧽 Dados de lavagem carregados:', data.length, 'registros');

                // Normalizar dados para o formato esperado pelo ListView
                return data.map(item => ({
                    date: item.dataLavagem || item.date,
                    vehicle: item.veiculo || item.vehicle || 'Não informado',
                    type: item.tipoLavagem || item.type || 'Não informado',
                    cost: parseFloat(item.valor || item.cost || 0),
                    location: item.localLavagem || item.location || 'Não informado',
                    observations: item.observacoes || item.observations || ''
                }));
            } catch (error) {
                console.error('Erro ao carregar dados de lavagem:', error);
            }
        }
        console.log('⚠️ Nenhum dado de lavagem encontrado');
        return [];
    }
    

    
    // Configurar event listeners
    setupEventListeners() {
        // Filtros
        document.getElementById('periodFilter').addEventListener('change', (e) => {
            this.filters.period = parseInt(e.target.value);
            this.applyFilters();
        });
        
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.filters.category = e.target.value;
            this.applyFilters();
        });
        
        document.getElementById('vehicleFilter').addEventListener('change', (e) => {
            this.filters.vehicle = e.target.value;
            this.applyFilters();
        });
    }
    

    
    // Preencher filtro de veículos
    populateVehicleFilter() {
        const vehicleFilter = document.getElementById('vehicleFilter');
        if (!vehicleFilter) return;

        vehicleFilter.innerHTML = '<option value="all">Todos</option>';

        // Carregar veículos do sistema de configuração
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                const vehicles = JSON.parse(savedVehicles);
                vehicles.forEach(vehicle => {
                    const option = document.createElement('option');
                    option.value = vehicle.plate;
                    option.textContent = `${vehicle.plate} - ${vehicle.brand} ${vehicle.model}`;
                    vehicleFilter.appendChild(option);
                });
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
            }
        }
    }
    
    // Aplicar filtros
    applyFilters() {
        console.log('🔍 Aplicando filtros:', this.filters);
        this.renderAll();
    }
    
    // Limpar filtros
    clearFilters() {
        this.filters = {
            period: 30,
            category: 'all',
            vehicle: 'all'
        };
        
        document.getElementById('periodFilter').value = '30';
        document.getElementById('categoryFilter').value = 'all';
        document.getElementById('vehicleFilter').value = 'all';
        
        this.renderAll();
    }
    
    // Filtrar dados por período
    filterByPeriod(data) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - this.filters.period);
        
        return data.filter(item => {
            const itemDate = new Date(item.date);
            return itemDate >= cutoffDate;
        });
    }
    
    // Filtrar dados por veículo
    filterByVehicle(data) {
        if (this.filters.vehicle === 'all') return data;
        return data.filter(item => item.vehicle === this.filters.vehicle);
    }
    
    // Renderizar tudo
    renderAll() {
        if (this.filters.category === 'all' || this.filters.category === 'fuel') {
            this.renderFuelSection();
        }
        
        if (this.filters.category === 'all' || this.filters.category === 'maintenance') {
            this.renderMaintenanceSection();
        }
        
        if (this.filters.category === 'all' || this.filters.category === 'washing') {
            this.renderWashingSection();
        }
        
        this.updateSectionVisibility();
    }
    
    // Renderizar seção de combustível
    renderFuelSection() {
        let data = this.filterByPeriod(this.data.fuel);
        data = this.filterByVehicle(data);
        
        // Calcular totais
        const totalLiters = data.reduce((sum, item) => sum + item.liters, 0);
        const totalCost = data.reduce((sum, item) => sum + item.total, 0);
        const avgPrice = totalLiters > 0 ? totalCost / totalLiters : 0;
        
        // Atualizar totais
        document.getElementById('totalFuelLiters').textContent = totalLiters.toFixed(1);
        document.getElementById('totalFuelCost').textContent = `R$ ${totalCost.toFixed(2)}`;
        document.getElementById('avgFuelPrice').textContent = `R$ ${avgPrice.toFixed(2)}`;
        document.getElementById('totalFuelRecords').textContent = data.length;
        
        // Renderizar tabela
        const tbody = document.getElementById('fuelTableBody');
        tbody.innerHTML = '';
        
        if (data.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Nenhum registro encontrado</td></tr>';
            return;
        }
        
        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(item.date).toLocaleDateString('pt-BR')}</td>
                <td><strong>${item.vehicle}</strong></td>
                <td>${item.liters.toFixed(1)}L</td>
                <td>R$ ${item.pricePerLiter.toFixed(2)}</td>
                <td><strong>R$ ${item.total.toFixed(2)}</strong></td>
                <td>${item.station}</td>
                <td>${item.km.toLocaleString()} km</td>
            `;
            tbody.appendChild(row);
        });
    }
    
    // Renderizar seção de manutenção
    renderMaintenanceSection() {
        let data = this.filterByPeriod(this.data.maintenance);
        data = this.filterByVehicle(data);
        
        // Calcular totais
        const totalCost = data.reduce((sum, item) => sum + item.cost, 0);
        const avgCost = data.length > 0 ? totalCost / data.length : 0;
        const pendingCount = data.filter(item => item.status === 'Pendente').length;
        
        // Atualizar totais
        document.getElementById('totalMaintenanceCost').textContent = `R$ ${totalCost.toFixed(2)}`;
        document.getElementById('avgMaintenanceCost').textContent = `R$ ${avgCost.toFixed(2)}`;
        document.getElementById('totalMaintenanceRecords').textContent = data.length;
        document.getElementById('pendingMaintenance').textContent = pendingCount;
        
        // Renderizar tabela
        const tbody = document.getElementById('maintenanceTableBody');
        tbody.innerHTML = '';
        
        if (data.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Nenhum registro encontrado</td></tr>';
            return;
        }
        
        data.forEach(item => {
            const statusClass = {
                'Concluído': 'status-active',
                'Em andamento': 'status-maintenance',
                'Agendado': 'status-maintenance',
                'Pendente': 'status-inactive'
            }[item.status] || 'status-maintenance';
            
            const priorityClass = {
                'Alta': 'priority-high',
                'Média': 'priority-medium',
                'Baixa': 'priority-low'
            }[item.priority] || 'priority-medium';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(item.date).toLocaleDateString('pt-BR')}</td>
                <td><strong>${item.vehicle}</strong></td>
                <td>${item.service}</td>
                <td><strong>R$ ${item.cost.toFixed(2)}</strong></td>
                <td><span class="status-badge ${statusClass}">${item.status}</span></td>
                <td><span class="status-badge ${priorityClass}">${item.priority}</span></td>
                <td>${new Date(item.nextDate).toLocaleDateString('pt-BR')}</td>
            `;
            tbody.appendChild(row);
        });
    }
    
    // Renderizar seção de lavagem
    renderWashingSection() {
        let data = this.filterByPeriod(this.data.washing);
        data = this.filterByVehicle(data);
        
        // Calcular totais
        const totalCost = data.reduce((sum, item) => sum + item.cost, 0);
        const avgCost = data.length > 0 ? totalCost / data.length : 0;
        const monthlyFreq = data.length > 0 ? (data.length / (this.filters.period / 30)) : 0;
        
        // Atualizar totais
        document.getElementById('totalWashingCost').textContent = `R$ ${totalCost.toFixed(2)}`;
        document.getElementById('avgWashingCost').textContent = `R$ ${avgCost.toFixed(2)}`;
        document.getElementById('totalWashingRecords').textContent = data.length;
        document.getElementById('washingFrequency').textContent = monthlyFreq.toFixed(1);
        
        // Renderizar tabela
        const tbody = document.getElementById('washingTableBody');
        tbody.innerHTML = '';
        
        if (data.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Nenhum registro encontrado</td></tr>';
            return;
        }
        
        data.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(item.date).toLocaleDateString('pt-BR')}</td>
                <td><strong>${item.vehicle}</strong></td>
                <td>${item.type}</td>
                <td><strong>R$ ${item.cost.toFixed(2)}</strong></td>
                <td>${item.location}</td>
                <td>${item.observations || '-'}</td>
            `;
            tbody.appendChild(row);
        });
    }
    
    // Atualizar visibilidade das seções
    updateSectionVisibility() {
        const sections = ['fuel', 'maintenance', 'washing'];
        
        sections.forEach(section => {
            const element = document.getElementById(`${section}Section`);
            if (element) {
                if (this.filters.category === 'all' || this.filters.category === section) {
                    element.style.display = this.visibleSections[section] ? 'block' : 'none';
                } else {
                    element.style.display = 'none';
                }
            }
        });
    }
    
    // Toggle seção
    toggleSection(section) {
        this.visibleSections[section] = !this.visibleSections[section];
        this.updateSectionVisibility();
    }
    
    // Exportar dados
    exportData() {
        console.log('📥 Exportando dados...');
        
        let exportData = {};
        
        if (this.filters.category === 'all' || this.filters.category === 'fuel') {
            exportData.fuel = this.filterByVehicle(this.filterByPeriod(this.data.fuel));
        }
        
        if (this.filters.category === 'all' || this.filters.category === 'maintenance') {
            exportData.maintenance = this.filterByVehicle(this.filterByPeriod(this.data.maintenance));
        }
        
        if (this.filters.category === 'all' || this.filters.category === 'washing') {
            exportData.washing = this.filterByVehicle(this.filterByPeriod(this.data.washing));
        }
        
        // Criar e baixar arquivo JSON
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `listview-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log('✅ Dados exportados com sucesso');
    }
}

// Funções globais
function applyFilters() {
    if (window.listview) {
        window.listview.applyFilters();
    }
}

function clearFilters() {
    if (window.listview) {
        window.listview.clearFilters();
    }
}

function toggleSection(section) {
    if (window.listview) {
        window.listview.toggleSection(section);
    }
}

function exportData() {
    if (window.listview) {
        window.listview.exportData();
    }
}

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Inicializando ListView...');
    window.listview = new ListView();
});
