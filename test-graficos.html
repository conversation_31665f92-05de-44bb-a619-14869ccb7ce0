<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Sistema de Gráficos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .test-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-chart-line text-primary"></i>
                    Teste do Sistema de Gráficos
                </h1>
                
                <div class="test-info">
                    <h5><i class="fas fa-info-circle"></i> Informações do Teste</h5>
                    <p>Esta página testa se o sistema de gráficos está funcionando corretamente.</p>
                    <div id="testResults"></div>
                </div>

                <!-- Botões de Teste -->
                <div class="mb-4">
                    <button class="btn btn-primary me-2" onclick="testChartJs()">
                        <i class="fas fa-check"></i> Testar Chart.js
                    </button>
                    <button class="btn btn-success me-2" onclick="testChartsManager()">
                        <i class="fas fa-cogs"></i> Testar ChartsManager
                    </button>
                    <button class="btn btn-warning me-2" onclick="createTestChart()">
                        <i class="fas fa-chart-bar"></i> Criar Gráfico de Teste
                    </button>
                    <button class="btn btn-info me-2" onclick="reinitializeSystem()">
                        <i class="fas fa-refresh"></i> Reinicializar Sistema
                    </button>
                </div>

                <!-- Área de Status -->
                <div id="statusArea"></div>

                <!-- Gráfico de Teste -->
                <div class="chart-container">
                    <h5>Gráfico de Teste</h5>
                    <canvas id="testChart"></canvas>
                </div>

                <!-- Gráficos Principais -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5>Consumo de Combustível</h5>
                            <canvas id="fuelConsumptionChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5>Custos por Categoria</h5>
                            <canvas id="costsByCategoryChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5>Status da Frota</h5>
                            <canvas id="fleetStatusChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5>Manutenções</h5>
                            <canvas id="maintenanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="js/graficos.js"></script>
    <script src="fix-graficos.js"></script>
    
    <script>
        // Funções de teste
        function addStatus(message, type = 'info') {
            const statusArea = document.getElementById('statusArea');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i> ${message}`;
            statusArea.appendChild(statusDiv);
            
            // Auto-remover após 10 segundos
            setTimeout(() => {
                if (statusDiv.parentElement) {
                    statusDiv.remove();
                }
            }, 10000);
        }

        function testChartJs() {
            if (typeof Chart !== 'undefined') {
                addStatus(`Chart.js está carregado! Versão: ${Chart.version}`, 'success');
            } else {
                addStatus('Chart.js NÃO está carregado!', 'error');
            }
        }

        function testChartsManager() {
            if (typeof ChartsManager !== 'undefined') {
                addStatus('ChartsManager está disponível!', 'success');
                
                if (window.chartsManager) {
                    addStatus('Instância do ChartsManager existe!', 'success');
                } else {
                    addStatus('Instância do ChartsManager NÃO existe. Criando...', 'warning');
                    try {
                        window.chartsManager = new ChartsManager();
                        addStatus('ChartsManager criado com sucesso!', 'success');
                    } catch (error) {
                        addStatus(`Erro ao criar ChartsManager: ${error.message}`, 'error');
                    }
                }
            } else {
                addStatus('ChartsManager NÃO está disponível!', 'error');
            }
        }

        function createTestChart() {
            const canvas = document.getElementById('testChart');
            if (!canvas) {
                addStatus('Canvas de teste não encontrado!', 'error');
                return;
            }

            try {
                const ctx = canvas.getContext('2d');
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                        datasets: [{
                            label: 'Teste',
                            data: [12, 19, 3, 5, 2, 3],
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: { beginAtZero: true }
                        }
                    }
                });
                
                addStatus('Gráfico de teste criado com sucesso!', 'success');
            } catch (error) {
                addStatus(`Erro ao criar gráfico de teste: ${error.message}`, 'error');
            }
        }

        function reinitializeSystem() {
            addStatus('Reinicializando sistema de gráficos...', 'warning');
            
            // Destruir gráficos existentes
            if (window.chartsManager && window.chartsManager.charts) {
                Object.values(window.chartsManager.charts).forEach(chart => {
                    if (chart && typeof chart.destroy === 'function') {
                        chart.destroy();
                    }
                });
            }
            
            // Reinicializar
            window.chartsManager = null;
            
            setTimeout(() => {
                try {
                    if (typeof reinitializeCharts === 'function') {
                        reinitializeCharts();
                        addStatus('Sistema reinicializado via função global!', 'success');
                    } else if (typeof ChartsManager !== 'undefined') {
                        window.chartsManager = new ChartsManager();
                        addStatus('Sistema reinicializado via ChartsManager!', 'success');
                    } else {
                        addStatus('Não foi possível reinicializar o sistema!', 'error');
                    }
                } catch (error) {
                    addStatus(`Erro na reinicialização: ${error.message}`, 'error');
                }
            }, 500);
        }

        // Inicialização automática
        document.addEventListener('DOMContentLoaded', function() {
            addStatus('Página de teste carregada!', 'success');
            
            setTimeout(() => {
                testChartJs();
                testChartsManager();
            }, 1000);
        });
    </script>
</body>
</html>
