import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '../lib/supabase'
import { User, Session } from '@supabase/supabase-js'
import toast from 'react-hot-toast'

interface AuthUser {
  id: string
  email: string
  name: string
  role: 'admin' | 'supervisor' | 'operador'
  status: string
}

interface AuthContextType {
  user: AuthUser | null
  session: Session | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => Promise<void>
  register: (data: RegisterData) => Promise<void>
}

interface RegisterData {
  email: string
  password: string
  name: string
  role: 'admin' | 'supervisor' | 'operador'
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Verificar sessão inicial
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      if (session?.user) {
        loadUserData(session.user)
      } else {
        setLoading(false)
      }
    })

    // Escutar mudanças na autenticação
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      
      if (session?.user) {
        await loadUserData(session.user)
      } else {
        setUser(null)
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.id)
        .single()

      if (error) {
        console.error('Erro ao carregar dados do usuário:', error)
        toast.error('Erro ao carregar dados do usuário')
        return
      }

      if (data.status !== 'ativo') {
        toast.error('Usuário inativo')
        await supabase.auth.signOut()
        return
      }

      setUser({
        id: data.id,
        email: data.email,
        name: data.name,
        role: data.role,
        status: data.status
      })

      // Atualizar último acesso
      await supabase
        .from('users')
        .update({ last_access: new Date().toISOString() })
        .eq('id', data.id)

    } catch (error) {
      console.error('Erro ao carregar usuário:', error)
      toast.error('Erro ao carregar dados do usuário')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        throw error
      }

      if (data.user) {
        await loadUserData(data.user)
        toast.success('Login realizado com sucesso!')
      }
    } catch (error: any) {
      console.error('Erro no login:', error)
      
      if (error.message === 'Invalid login credentials') {
        toast.error('Email ou senha incorretos')
      } else {
        toast.error('Erro ao fazer login: ' + error.message)
      }
      
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        throw error
      }

      setUser(null)
      setSession(null)
      toast.success('Logout realizado com sucesso!')
    } catch (error: any) {
      console.error('Erro no logout:', error)
      toast.error('Erro ao fazer logout: ' + error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const register = async (data: RegisterData) => {
    try {
      setLoading(true)

      // Verificar se já existe um admin
      if (data.role === 'admin') {
        const { data: existingAdmin } = await supabase
          .from('users')
          .select('id')
          .eq('role', 'admin')
          .limit(1)

        if (existingAdmin && existingAdmin.length > 0) {
          throw new Error('Apenas um administrador é permitido no sistema')
        }
      }

      // Criar usuário no Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password
      })

      if (authError) {
        throw authError
      }

      if (!authData.user) {
        throw new Error('Erro ao criar usuário')
      }

      // Criar registro na tabela users
      const { error: userError } = await supabase
        .from('users')
        .insert([{
          id: authData.user.id,
          email: data.email,
          name: data.name,
          role: data.role,
          status: 'ativo'
        }])

      if (userError) {
        // Se falhar ao criar o registro, remover o usuário do Auth
        await supabase.auth.admin.deleteUser(authData.user.id)
        throw userError
      }

      toast.success('Usuário criado com sucesso!')
    } catch (error: any) {
      console.error('Erro no registro:', error)
      toast.error('Erro ao criar usuário: ' + error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const value = {
    user,
    session,
    loading,
    login,
    logout,
    register
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider')
  }
  return context
}
