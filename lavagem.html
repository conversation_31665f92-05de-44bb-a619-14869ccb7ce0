<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lavagem - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <link href="css/font-size-adjustments.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html" class="active"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                        <li><a href="configuracoes.html"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Lavagem</h1>
            </div>
            
            <div class="topbar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lavagem Content -->
        <div class="dashboard-content">
            <!-- Action Bar -->
            <div class="action-bar">
                <button class="btn btn-primary" onclick="openLavagemModal()">
                    <i class="fas fa-plus"></i> Nova Lavagem
                </button>
                
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Buscar por veículo, placa..." id="searchInput">
                    </div>
                    
                    <select id="filterPeriodo" class="form-select">
                        <option value="">Todos os períodos</option>
                        <option value="hoje">Hoje</option>
                        <option value="semana">Esta semana</option>
                        <option value="mes">Este mês</option>
                        <option value="trimestre">Este trimestre</option>
                    </select>
                    
                    <select id="filterTipo" class="form-select">
                        <option value="">Todos os tipos</option>
                        <option value="simples">Simples</option>
                        <option value="completa">Completa</option>
                        <option value="enceramento">Enceramento</option>
                        <option value="detalhamento">Detalhamento</option>
                    </select>
                    
                    <select id="filterLocal" class="form-select">
                        <option value="">Todos os locais</option>
                        <option value="interno">Interno</option>
                        <option value="externo">Externo</option>
                    </select>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-spray-can"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalLavagens">89</h3>
                        <p>Total de Lavagens</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-calendar-week"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="lavagensEstaSemana">12</h3>
                        <p>Esta Semana</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="custoTotal">R$ 2.340</h3>
                        <p>Custo Total</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="custoMedio">R$ 26,30</h3>
                        <p>Custo Médio</p>
                    </div>
                </div>
            </div>

            <!-- Próximas Lavagens Agendadas -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Próximas Lavagens Agendadas</h3>
                    <button class="btn btn-sm btn-outline-primary" onclick="agendarLavagem()">
                        <i class="fas fa-calendar-plus"></i> Agendar
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table" id="proximasLavagensTable">
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Veículo</th>
                                <th>Placa</th>
                                <th>Tipo</th>
                                <th>Local</th>
                                <th>Valor</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="proximasLavagensTableBody">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Histórico de Lavagens -->
            <div class="table-container">
                <div class="table-header">
                    <h3>Histórico de Lavagens</h3>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="exportLavagens()">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table" id="lavagensTa ble">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Veículo</th>
                                <th>Placa</th>
                                <th>Tipo</th>
                                <th>Local</th>
                                <th>Responsável</th>
                                <th>Valor</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="lavagensTableBody">
                            <!-- Dados serão carregados via JavaScript -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-container">
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination" id="pagination">
                            <!-- Paginação será gerada via JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nova Lavagem -->
    <div class="modal fade" id="lavagemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Nova Lavagem</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <form id="lavagemForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="veiculo">Veículo *</label>
                                    <select id="veiculo" name="veiculo" class="form-select" required>
                                        <option value="">Selecione o veículo</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dataLavagem">Data/Hora da Lavagem *</label>
                                    <input type="datetime-local" id="dataLavagem" name="dataLavagem" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tipoLavagem">Tipo de Lavagem *</label>
                                    <select id="tipoLavagem" name="tipoLavagem" class="form-select" required>
                                        <option value="">Selecione</option>
                                        <option value="simples">Simples</option>
                                        <option value="completa">Completa</option>
                                        <option value="enceramento">Enceramento</option>
                                        <option value="detalhamento">Detalhamento</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="localLavagem">Local *</label>
                                    <select id="localLavagem" name="localLavagem" class="form-select" required>
                                        <option value="">Selecione</option>
                                        <option value="interno">Interno (Garagem)</option>
                                        <option value="externo">Externo (Lava-jato)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row" id="lavajatoFields" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nomeLavajato">Nome do Lava-jato</label>
                                    <input type="text" id="nomeLavajato" name="nomeLavajato" class="form-control">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="enderecoLavajato">Endereço</label>
                                    <input type="text" id="enderecoLavajato" name="enderecoLavajato" class="form-control">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="responsavel">Responsável *</label>
                                    <input type="text" id="responsavel" name="responsavel" class="form-control" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="valor">Valor *</label>
                                    <input type="number" id="valor" name="valor" class="form-control" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="kmAtual">Quilometragem Atual</label>
                                    <input type="number" id="kmAtual" name="kmAtual" class="form-control">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" name="status" class="form-select">
                                        <option value="agendada">Agendada</option>
                                        <option value="realizada">Realizada</option>
                                        <option value="cancelada">Cancelada</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="servicos">Serviços Inclusos</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" name="servicos" value="lavagem_externa"> Lavagem Externa
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="servicos" value="lavagem_interna"> Lavagem Interna
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="servicos" value="aspiracao"> Aspiração
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="servicos" value="enceramento"> Enceramento
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="servicos" value="pneus"> Pretinho nos Pneus
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="servicos" value="painel"> Limpeza do Painel
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="observacoes">Observações</label>
                            <textarea id="observacoes" name="observacoes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar Lavagem</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/lavagem.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
