// Sistema de Configurações
class ConfiguracaoSystem {
    constructor() {
        this.categorias = {
            receitas: [],
            despesas: []
        };
        this.init();
    }

    init() {
        console.log('🔧 Sistema de Configurações inicializado');
        this.carregarCategorias();
        this.atualizarInformacoes();
    }

    // Atualizar informações do sistema
    atualizarInformacoes() {
        try {
            console.log('📊 Atualizando informações do sistema...');
            
            // Contar registros
            let totalRegistros = 0;
            let espacoUtilizado = 0;
            
            // Verificar cada módulo
            const modulos = [
                'abastecimentos',
                'manutencoes', 
                'lavagens',
                'revisoes',
                'caixa_transacoes',
                'veiculos',
                'fornecedores',
                'usuarios'
            ];
            
            modulos.forEach(modulo => {
                const dados = localStorage.getItem(modulo);
                if (dados) {
                    try {
                        const parsed = JSON.parse(dados);
                        if (Array.isArray(parsed)) {
                            totalRegistros += parsed.length;
                        } else {
                            totalRegistros += 1;
                        }
                        espacoUtilizado += dados.length;
                    } catch (e) {
                        console.warn(`Erro ao processar ${modulo}:`, e);
                    }
                }
            });
            
            // Atualizar interface principal
            const totalElement = document.getElementById('totalRegistros');
            const espacoElement = document.getElementById('espacoUtilizado');
            const ultimaElement = document.getElementById('ultimaAtualizacao');

            if (totalElement) totalElement.textContent = totalRegistros.toLocaleString('pt-BR');
            if (espacoElement) espacoElement.textContent = this.formatBytes(espacoUtilizado);
            if (ultimaElement) ultimaElement.textContent = new Date().toLocaleString('pt-BR');

            // Atualizar cards de estatísticas
            const totalCardElement = document.getElementById('totalRegistrosCard');
            const espacoCardElement = document.getElementById('espacoUtilizadoCard');
            const ultimaCardElement = document.getElementById('ultimaAtualizacaoCard');

            if (totalCardElement) {
                this.animateNumber(totalCardElement, 0, totalRegistros, 1000);
            }
            if (espacoCardElement) {
                espacoCardElement.textContent = this.formatBytes(espacoUtilizado);
            }
            if (ultimaCardElement) {
                const agora = new Date();
                ultimaCardElement.textContent = agora.toLocaleTimeString('pt-BR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            console.log(`✅ Informações atualizadas - ${totalRegistros} registros, ${this.formatBytes(espacoUtilizado)}`);

        } catch (error) {
            console.error('❌ Erro ao atualizar informações:', error);
            this.showToast('Erro ao atualizar informações do sistema', 'error');
        }
    }

    // Formatar bytes em formato legível
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Animação de números
    animateNumber(element, start, end, duration) {
        const startTime = performance.now();
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (end - start) * this.easeOutCubic(progress));
            element.textContent = current.toLocaleString('pt-BR');

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        requestAnimationFrame(animate);
    }

    // Função de easing
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    // Sistema de notificações toast
    showToast(message, type = 'success', duration = 3000) {
        // Criar container se não existir
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }

        // Criar toast
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        toast.style.transition = 'all 0.3s ease';
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-${this.getToastIcon(type)} me-2"></i>
                <strong class="me-auto">${this.getToastTitle(type)}</strong>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        container.appendChild(toast);

        // Mostrar toast
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        // Remover toast automaticamente
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }, duration);
    }

    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    getToastTitle(type) {
        const titles = {
            success: 'Sucesso',
            error: 'Erro',
            warning: 'Aviso',
            info: 'Informação'
        };
        return titles[type] || 'Informação';
    }

    // Fazer backup completo
    fazerBackup() {
        try {
            console.log('💾 Iniciando backup completo...');
            
            const backup = {
                versao: '1.0.0',
                dataBackup: new Date().toISOString(),
                dados: {}
            };
            
            // Coletar todos os dados do localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                
                // Tentar parsear como JSON, se falhar manter como string
                try {
                    backup.dados[key] = JSON.parse(value);
                } catch (e) {
                    backup.dados[key] = value;
                }
            }
            
            // Criar arquivo para download
            const dataStr = JSON.stringify(backup, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `backup_gestao_frotas_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            console.log('✅ Backup criado com sucesso');
            this.showToast('Backup criado com sucesso! Arquivo baixado para Downloads.', 'success', 4000);

        } catch (error) {
            console.error('❌ Erro ao fazer backup:', error);
            this.showToast('Erro ao criar backup: ' + error.message, 'error');
        }
    }

    // Restaurar backup
    restaurarBackup() {
        const fileInput = document.getElementById('backupFile');
        const file = fileInput.files[0];
        
        if (!file) {
            alert('⚠️ Selecione um arquivo de backup primeiro.');
            return;
        }
        
        if (!confirm('⚠️ ATENÇÃO: Restaurar backup irá substituir TODOS os dados atuais.\n\nDeseja continuar?')) {
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                console.log('📥 Restaurando backup...');
                
                const backup = JSON.parse(e.target.result);
                
                // Validar estrutura do backup
                if (!backup.dados || !backup.versao) {
                    throw new Error('Arquivo de backup inválido');
                }
                
                // Limpar localStorage atual
                localStorage.clear();
                
                // Restaurar dados
                Object.entries(backup.dados).forEach(([key, value]) => {
                    localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
                });
                
                console.log('✅ Backup restaurado com sucesso');
                alert('✅ Backup restaurado com sucesso!\n\nA página será recarregada.');
                
                // Recarregar página
                window.location.reload();
                
            } catch (error) {
                console.error('❌ Erro ao restaurar backup:', error);
                alert('❌ Erro ao restaurar backup: ' + error.message);
            }
        };
        
        reader.readAsText(file);
    }

    // Limpar cache do sistema
    limparCache() {
        if (!confirm('Deseja limpar o cache do sistema?\n\nIsso pode melhorar a performance mas não afetará seus dados.')) {
            return;
        }
        
        try {
            console.log('🧹 Limpando cache...');
            
            // Limpar cache específico (se houver)
            const cacheKeys = ['cache_', 'temp_', 'session_'];
            
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (cacheKeys.some(prefix => key.startsWith(prefix))) {
                    localStorage.removeItem(key);
                }
            }
            
            // Limpar sessionStorage
            sessionStorage.clear();
            
            console.log('✅ Cache limpo com sucesso');
            this.showToast('Cache limpo com sucesso!', 'success');
            this.atualizarInformacoes();

        } catch (error) {
            console.error('❌ Erro ao limpar cache:', error);
            this.showToast('Erro ao limpar cache: ' + error.message, 'error');
        }
    }

    // Reset configurações
    resetConfiguracoes() {
        if (!confirm('Deseja restaurar todas as configurações para os valores padrão?\n\nIsso não afetará seus dados, apenas as configurações.')) {
            return;
        }
        
        try {
            console.log('⚙️ Resetando configurações...');
            
            // Remover configurações específicas
            const configKeys = ['config_', 'settings_', 'preferences_'];
            
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (configKeys.some(prefix => key.startsWith(prefix))) {
                    localStorage.removeItem(key);
                }
            }
            
            console.log('✅ Configurações resetadas');
            alert('✅ Configurações restauradas para os valores padrão!');
            this.atualizarInformacoes();
            
        } catch (error) {
            console.error('❌ Erro ao resetar configurações:', error);
            alert('❌ Erro ao resetar configurações: ' + error.message);
        }
    }

    // Mostrar modal para seleção de dados
    mostrarOpcoesDados() {
        const modal = new bootstrap.Modal(document.getElementById('modalDados'));
        modal.show();
    }

    // Limpar dados selecionados
    limparDadosSelecionados() {
        const checkboxes = {
            'checkAbastecimento': 'abastecimentos',
            'checkManutencao': 'manutencoes',
            'checkLavagem': 'lavagens',
            'checkCaixa': ['caixa_transacoes', 'caixa_saldo_inicial'],
            'checkVeiculos': 'veiculos',
            'checkFornecedores': 'fornecedores'
        };
        
        const selecionados = [];
        Object.entries(checkboxes).forEach(([checkId, storageKey]) => {
            if (document.getElementById(checkId).checked) {
                selecionados.push(storageKey);
            }
        });
        
        if (selecionados.length === 0) {
            alert('⚠️ Selecione pelo menos um tipo de dados para limpar.');
            return;
        }
        
        const confirmMsg = `⚠️ ATENÇÃO: Você está prestes a remover os seguintes dados:\n\n${selecionados.join('\n')}\n\nEsta ação é irreversível. Deseja continuar?`;
        
        if (!confirm(confirmMsg)) {
            return;
        }
        
        try {
            console.log('🗑️ Limpando dados selecionados...');
            
            selecionados.forEach(key => {
                if (Array.isArray(key)) {
                    key.forEach(k => localStorage.removeItem(k));
                } else {
                    localStorage.removeItem(key);
                }
            });
            
            console.log('✅ Dados selecionados removidos');
            alert('✅ Dados selecionados removidos com sucesso!');
            
            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalDados'));
            modal.hide();
            
            this.atualizarInformacoes();
            
        } catch (error) {
            console.error('❌ Erro ao limpar dados:', error);
            alert('❌ Erro ao limpar dados: ' + error.message);
        }
    }

    // Reset completo do sistema
    resetCompleto() {
        const confirmacao1 = confirm('⚠️ ATENÇÃO CRÍTICA!\n\nVocê está prestes a APAGAR TODOS OS DADOS do sistema.\n\nEsta ação é IRREVERSÍVEL e removerá:\n- Todos os abastecimentos\n- Todas as manutenções\n- Todos os dados de caixa\n- Todos os veículos\n- Todos os fornecedores\n- Todas as configurações\n\nDeseja continuar?');
        
        if (!confirmacao1) return;
        
        const confirmacao2 = confirm('⚠️ ÚLTIMA CONFIRMAÇÃO!\n\nTem certeza ABSOLUTA que deseja apagar TODOS os dados?\n\nEsta é sua última chance de cancelar!');
        
        if (!confirmacao2) return;
        
        // Solicitar confirmação por texto
        const textoConfirmacao = prompt('Para confirmar, digite "APAGAR TUDO" (sem aspas):');
        
        if (textoConfirmacao !== 'APAGAR TUDO') {
            alert('❌ Confirmação incorreta. Reset cancelado.');
            return;
        }
        
        try {
            console.log('💥 Executando reset completo do sistema...');
            
            // Limpar completamente o localStorage
            localStorage.clear();
            
            // Limpar sessionStorage
            sessionStorage.clear();
            
            console.log('✅ Reset completo executado');
            alert('✅ Reset completo executado!\n\nTodos os dados foram removidos.\nA página será recarregada.');
            
            // Recarregar página
            window.location.reload();

        } catch (error) {
            console.error('❌ Erro no reset completo:', error);
            alert('❌ Erro no reset completo: ' + error.message);
        }
    }

    // ========================================
    // GERENCIAMENTO DE CATEGORIAS
    // ========================================

    // Carregar categorias do localStorage
    carregarCategorias() {
        try {
            const categoriasReceitas = localStorage.getItem('categorias_receitas');
            const categoriasDespesas = localStorage.getItem('categorias_despesas');

            this.categorias.receitas = categoriasReceitas ? JSON.parse(categoriasReceitas) : this.getCategoriasReceitasPadrao();
            this.categorias.despesas = categoriasDespesas ? JSON.parse(categoriasDespesas) : this.getCategoriasDespesasPadrao();

            // Se não existem categorias salvas, criar as padrão
            if (!categoriasReceitas) {
                this.salvarCategoriasReceitas();
            }
            if (!categoriasDespesas) {
                this.salvarCategoriasDespesas();
            }

            this.renderizarCategorias();
            console.log('✅ Categorias carregadas:', this.categorias);
        } catch (error) {
            console.error('❌ Erro ao carregar categorias:', error);
            this.categorias.receitas = this.getCategoriasReceitasPadrao();
            this.categorias.despesas = this.getCategoriasDespesasPadrao();
            this.renderizarCategorias();
        }
    }

    // Categorias padrão de receitas
    getCategoriasReceitasPadrao() {
        return [
            {
                id: 'receita_1',
                nome: 'Vendas',
                descricao: 'Receitas de vendas de produtos/serviços',
                cor: 'success',
                ativo: true
            },
            {
                id: 'receita_2',
                nome: 'Serviços',
                descricao: 'Receitas de prestação de serviços',
                cor: 'info',
                ativo: true
            }
        ];
    }

    // Categorias padrão de despesas
    getCategoriasDespesasPadrao() {
        return [
            {
                id: 'despesa_1',
                nome: 'Combustível',
                descricao: 'Gastos com combustível dos veículos',
                cor: 'warning',
                ativo: true
            },
            {
                id: 'despesa_2',
                nome: 'Manutenção',
                descricao: 'Gastos com manutenção dos veículos',
                cor: 'danger',
                ativo: true
            },
            {
                id: 'despesa_3',
                nome: 'Lavagem',
                descricao: 'Gastos com lavagem dos veículos',
                cor: 'primary',
                ativo: true
            }
        ];
    }

    // Salvar categorias no localStorage
    salvarCategoriasReceitas() {
        localStorage.setItem('categorias_receitas', JSON.stringify(this.categorias.receitas));
    }

    salvarCategoriasDespesas() {
        localStorage.setItem('categorias_despesas', JSON.stringify(this.categorias.despesas));
    }

    // Renderizar categorias na interface
    renderizarCategorias() {
        this.renderizarListaReceitas();
        this.renderizarListaDespesas();
    }

    // Renderizar lista de receitas
    renderizarListaReceitas() {
        const container = document.getElementById('listaReceitas');
        if (!container) return;

        if (this.categorias.receitas.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Nenhuma categoria de receita cadastrada.</p>';
            return;
        }

        container.innerHTML = this.categorias.receitas.map(categoria => `
            <div class="category-item">
                <div class="category-info">
                    <h6 class="category-name">
                        <span class="category-badge bg-${categoria.cor}">${categoria.nome}</span>
                    </h6>
                    <p class="category-description">${categoria.descricao || 'Sem descrição'}</p>
                </div>
                <div class="category-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editarCategoria('${categoria.id}', 'receita')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="excluirCategoria('${categoria.id}', 'receita')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Renderizar lista de despesas
    renderizarListaDespesas() {
        const container = document.getElementById('listaDespesas');
        if (!container) return;

        if (this.categorias.despesas.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Nenhuma categoria de despesa cadastrada.</p>';
            return;
        }

        container.innerHTML = this.categorias.despesas.map(categoria => `
            <div class="category-item">
                <div class="category-info">
                    <h6 class="category-name">
                        <span class="category-badge bg-${categoria.cor}">${categoria.nome}</span>
                    </h6>
                    <p class="category-description">${categoria.descricao || 'Sem descrição'}</p>
                </div>
                <div class="category-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editarCategoria('${categoria.id}', 'despesa')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="excluirCategoria('${categoria.id}', 'despesa')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Adicionar nova categoria
    adicionarCategoria(tipo) {
        console.log(`➕ Adicionando nova categoria de ${tipo}`);

        // Limpar formulário
        document.getElementById('categoriaForm').reset();
        document.getElementById('categoriaId').value = '';
        document.getElementById('categoriaTipo').value = tipo;

        // Configurar modal
        const modalTitle = document.getElementById('categoriaModalTitle');
        modalTitle.textContent = tipo === 'receita' ? 'Nova Categoria de Receita' : 'Nova Categoria de Despesa';

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('categoriaModal'));
        modal.show();
    }

    // Editar categoria existente
    editarCategoria(id, tipo) {
        console.log(`✏️ Editando categoria ${id} do tipo ${tipo}`);

        const categorias = tipo === 'receita' ? this.categorias.receitas : this.categorias.despesas;
        const categoria = categorias.find(c => c.id === id);

        if (!categoria) {
            alert('❌ Categoria não encontrada!');
            return;
        }

        // Preencher formulário
        document.getElementById('categoriaId').value = categoria.id;
        document.getElementById('categoriaTipo').value = tipo;
        document.getElementById('categoriaNome').value = categoria.nome;
        document.getElementById('categoriaDescricao').value = categoria.descricao || '';
        document.getElementById('categoriaCor').value = categoria.cor;

        // Configurar modal
        const modalTitle = document.getElementById('categoriaModalTitle');
        modalTitle.textContent = `Editar Categoria: ${categoria.nome}`;

        // Mostrar modal
        const modal = new bootstrap.Modal(document.getElementById('categoriaModal'));
        modal.show();
    }

    // Excluir categoria
    excluirCategoria(id, tipo) {
        console.log(`🗑️ Excluindo categoria ${id} do tipo ${tipo}`);

        const categorias = tipo === 'receita' ? this.categorias.receitas : this.categorias.despesas;
        const categoria = categorias.find(c => c.id === id);

        if (!categoria) {
            alert('❌ Categoria não encontrada!');
            return;
        }

        const confirmacao = confirm(`⚠️ Tem certeza que deseja excluir a categoria "${categoria.nome}"?\n\nEsta ação não pode ser desfeita.`);

        if (!confirmacao) return;

        try {
            // Remover categoria do array
            const index = categorias.findIndex(c => c.id === id);
            if (index > -1) {
                categorias.splice(index, 1);

                // Salvar no localStorage
                if (tipo === 'receita') {
                    this.salvarCategoriasReceitas();
                } else {
                    this.salvarCategoriasDespesas();
                }

                // Atualizar interface
                this.renderizarCategorias();

                console.log(`✅ Categoria "${categoria.nome}" excluída com sucesso`);
                this.mostrarToast('Categoria excluída com sucesso!', 'success');
            }
        } catch (error) {
            console.error('❌ Erro ao excluir categoria:', error);
            alert('❌ Erro ao excluir categoria: ' + error.message);
        }
    }

    // Salvar categoria (nova ou editada)
    salvarCategoria() {
        try {
            const id = document.getElementById('categoriaId').value;
            const tipo = document.getElementById('categoriaTipo').value;
            const nome = document.getElementById('categoriaNome').value.trim();
            const descricao = document.getElementById('categoriaDescricao').value.trim();
            const cor = document.getElementById('categoriaCor').value;

            // Validações
            if (!nome) {
                alert('❌ Nome da categoria é obrigatório!');
                return;
            }

            if (!tipo) {
                alert('❌ Tipo da categoria não foi definido!');
                return;
            }

            const categorias = tipo === 'receita' ? this.categorias.receitas : this.categorias.despesas;

            // Verificar se já existe categoria com mesmo nome (exceto a atual sendo editada)
            const categoriaExistente = categorias.find(c => c.nome.toLowerCase() === nome.toLowerCase() && c.id !== id);
            if (categoriaExistente) {
                alert(`❌ Já existe uma categoria com o nome "${nome}"!`);
                return;
            }

            if (id) {
                // Editar categoria existente
                const categoria = categorias.find(c => c.id === id);
                if (categoria) {
                    categoria.nome = nome;
                    categoria.descricao = descricao;
                    categoria.cor = cor;

                    console.log(`✅ Categoria "${nome}" atualizada`);
                    this.mostrarToast('Categoria atualizada com sucesso!', 'success');
                }
            } else {
                // Criar nova categoria
                const novaCategoria = {
                    id: `${tipo}_${Date.now()}`,
                    nome: nome,
                    descricao: descricao,
                    cor: cor,
                    ativo: true
                };

                // CORREÇÃO: Adicionar na lista correta baseada no tipo
                if (tipo === 'receita') {
                    this.categorias.receitas.push(novaCategoria);
                } else if (tipo === 'despesa') {
                    this.categorias.despesas.push(novaCategoria);
                }

                console.log(`✅ Nova categoria "${nome}" criada no tipo "${tipo}"`);
                this.mostrarToast(`Categoria de ${tipo} criada com sucesso!`, 'success');
            }

            // Salvar no localStorage
            if (tipo === 'receita') {
                this.salvarCategoriasReceitas();
            } else {
                this.salvarCategoriasDespesas();
            }

            // Atualizar interface
            this.renderizarCategorias();

            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('categoriaModal'));
            if (modal) {
                modal.hide();
            }

        } catch (error) {
            console.error('❌ Erro ao salvar categoria:', error);
            alert('❌ Erro ao salvar categoria: ' + error.message);
        }
    }

    // Mostrar toast de notificação
    mostrarToast(mensagem, tipo = 'info') {
        const toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) return;

        const toastId = 'toast_' + Date.now();
        const toastHtml = `
            <div class="toast ${tipo}" id="${toastId}" role="alert">
                <div class="toast-body d-flex align-items-center">
                    <i class="fas fa-${tipo === 'success' ? 'check-circle' : tipo === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${mensagem}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Remover toast após ser ocultado
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

// Funções globais para os botões
function atualizarInformacoes() {
    if (window.configSystem) {
        window.configSystem.atualizarInformacoes();
    }
}

function fazerBackup() {
    if (window.configSystem) {
        window.configSystem.fazerBackup();
    }
}

function restaurarBackup() {
    if (window.configSystem) {
        window.configSystem.restaurarBackup();
    }
}

function limparCache() {
    if (window.configSystem) {
        window.configSystem.limparCache();
    }
}

function resetConfiguracoes() {
    if (window.configSystem) {
        window.configSystem.resetConfiguracoes();
    }
}

function mostrarOpcoesDados() {
    if (window.configSystem) {
        window.configSystem.mostrarOpcoesDados();
    }
}

function limparDadosSelecionados() {
    if (window.configSystem) {
        window.configSystem.limparDadosSelecionados();
    }
}

function resetCompleto() {
    if (window.configSystem) {
        window.configSystem.resetCompleto();
    }
}

// Funções de Gerenciamento de Categorias
function adicionarCategoria(tipo) {
    if (window.configSystem) {
        window.configSystem.adicionarCategoria(tipo);
    }
}

function editarCategoria(id, tipo) {
    if (window.configSystem) {
        window.configSystem.editarCategoria(id, tipo);
    }
}

function excluirCategoria(id, tipo) {
    if (window.configSystem) {
        window.configSystem.excluirCategoria(id, tipo);
    }
}

function salvarCategoria() {
    if (window.configSystem) {
        window.configSystem.salvarCategoria();
    }
}

// Inicializar sistema quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.configSystem = new ConfiguracaoSystem();
});
