// Sistema de Configurações
class ConfiguracaoSystem {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔧 Sistema de Configurações inicializado');
        this.atualizarInformacoes();
    }

    // Atualizar informações do sistema
    atualizarInformacoes() {
        try {
            console.log('📊 Atualizando informações do sistema...');
            
            // Contar registros
            let totalRegistros = 0;
            let espacoUtilizado = 0;
            
            // Verificar cada módulo
            const modulos = [
                'abastecimentos',
                'manutencoes', 
                'lavagens',
                'revisoes',
                'caixa_transacoes',
                'veiculos',
                'fornecedores',
                'usuarios'
            ];
            
            modulos.forEach(modulo => {
                const dados = localStorage.getItem(modulo);
                if (dados) {
                    try {
                        const parsed = JSON.parse(dados);
                        if (Array.isArray(parsed)) {
                            totalRegistros += parsed.length;
                        } else {
                            totalRegistros += 1;
                        }
                        espacoUtilizado += dados.length;
                    } catch (e) {
                        console.warn(`Erro ao processar ${modulo}:`, e);
                    }
                }
            });
            
            // Atualizar interface
            document.getElementById('totalRegistros').textContent = totalRegistros.toLocaleString('pt-BR');
            document.getElementById('espacoUtilizado').textContent = this.formatBytes(espacoUtilizado);
            document.getElementById('ultimaAtualizacao').textContent = new Date().toLocaleString('pt-BR');
            
            console.log(`✅ Informações atualizadas - ${totalRegistros} registros, ${this.formatBytes(espacoUtilizado)}`);
            
        } catch (error) {
            console.error('❌ Erro ao atualizar informações:', error);
        }
    }

    // Formatar bytes em formato legível
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Fazer backup completo
    fazerBackup() {
        try {
            console.log('💾 Iniciando backup completo...');
            
            const backup = {
                versao: '1.0.0',
                dataBackup: new Date().toISOString(),
                dados: {}
            };
            
            // Coletar todos os dados do localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                
                // Tentar parsear como JSON, se falhar manter como string
                try {
                    backup.dados[key] = JSON.parse(value);
                } catch (e) {
                    backup.dados[key] = value;
                }
            }
            
            // Criar arquivo para download
            const dataStr = JSON.stringify(backup, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `backup_gestao_frotas_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            console.log('✅ Backup criado com sucesso');
            alert('✅ Backup criado com sucesso!\nO arquivo foi baixado para sua pasta de Downloads.');
            
        } catch (error) {
            console.error('❌ Erro ao fazer backup:', error);
            alert('❌ Erro ao criar backup: ' + error.message);
        }
    }

    // Restaurar backup
    restaurarBackup() {
        const fileInput = document.getElementById('backupFile');
        const file = fileInput.files[0];
        
        if (!file) {
            alert('⚠️ Selecione um arquivo de backup primeiro.');
            return;
        }
        
        if (!confirm('⚠️ ATENÇÃO: Restaurar backup irá substituir TODOS os dados atuais.\n\nDeseja continuar?')) {
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                console.log('📥 Restaurando backup...');
                
                const backup = JSON.parse(e.target.result);
                
                // Validar estrutura do backup
                if (!backup.dados || !backup.versao) {
                    throw new Error('Arquivo de backup inválido');
                }
                
                // Limpar localStorage atual
                localStorage.clear();
                
                // Restaurar dados
                Object.entries(backup.dados).forEach(([key, value]) => {
                    localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
                });
                
                console.log('✅ Backup restaurado com sucesso');
                alert('✅ Backup restaurado com sucesso!\n\nA página será recarregada.');
                
                // Recarregar página
                window.location.reload();
                
            } catch (error) {
                console.error('❌ Erro ao restaurar backup:', error);
                alert('❌ Erro ao restaurar backup: ' + error.message);
            }
        };
        
        reader.readAsText(file);
    }

    // Limpar cache do sistema
    limparCache() {
        if (!confirm('Deseja limpar o cache do sistema?\n\nIsso pode melhorar a performance mas não afetará seus dados.')) {
            return;
        }
        
        try {
            console.log('🧹 Limpando cache...');
            
            // Limpar cache específico (se houver)
            const cacheKeys = ['cache_', 'temp_', 'session_'];
            
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (cacheKeys.some(prefix => key.startsWith(prefix))) {
                    localStorage.removeItem(key);
                }
            }
            
            // Limpar sessionStorage
            sessionStorage.clear();
            
            console.log('✅ Cache limpo com sucesso');
            alert('✅ Cache limpo com sucesso!');
            this.atualizarInformacoes();
            
        } catch (error) {
            console.error('❌ Erro ao limpar cache:', error);
            alert('❌ Erro ao limpar cache: ' + error.message);
        }
    }

    // Reset configurações
    resetConfiguracoes() {
        if (!confirm('Deseja restaurar todas as configurações para os valores padrão?\n\nIsso não afetará seus dados, apenas as configurações.')) {
            return;
        }
        
        try {
            console.log('⚙️ Resetando configurações...');
            
            // Remover configurações específicas
            const configKeys = ['config_', 'settings_', 'preferences_'];
            
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (configKeys.some(prefix => key.startsWith(prefix))) {
                    localStorage.removeItem(key);
                }
            }
            
            console.log('✅ Configurações resetadas');
            alert('✅ Configurações restauradas para os valores padrão!');
            this.atualizarInformacoes();
            
        } catch (error) {
            console.error('❌ Erro ao resetar configurações:', error);
            alert('❌ Erro ao resetar configurações: ' + error.message);
        }
    }

    // Mostrar modal para seleção de dados
    mostrarOpcoesDados() {
        const modal = new bootstrap.Modal(document.getElementById('modalDados'));
        modal.show();
    }

    // Limpar dados selecionados
    limparDadosSelecionados() {
        const checkboxes = {
            'checkAbastecimento': 'abastecimentos',
            'checkManutencao': 'manutencoes',
            'checkLavagem': 'lavagens',
            'checkCaixa': ['caixa_transacoes', 'caixa_saldo_inicial'],
            'checkVeiculos': 'veiculos',
            'checkFornecedores': 'fornecedores'
        };
        
        const selecionados = [];
        Object.entries(checkboxes).forEach(([checkId, storageKey]) => {
            if (document.getElementById(checkId).checked) {
                selecionados.push(storageKey);
            }
        });
        
        if (selecionados.length === 0) {
            alert('⚠️ Selecione pelo menos um tipo de dados para limpar.');
            return;
        }
        
        const confirmMsg = `⚠️ ATENÇÃO: Você está prestes a remover os seguintes dados:\n\n${selecionados.join('\n')}\n\nEsta ação é irreversível. Deseja continuar?`;
        
        if (!confirm(confirmMsg)) {
            return;
        }
        
        try {
            console.log('🗑️ Limpando dados selecionados...');
            
            selecionados.forEach(key => {
                if (Array.isArray(key)) {
                    key.forEach(k => localStorage.removeItem(k));
                } else {
                    localStorage.removeItem(key);
                }
            });
            
            console.log('✅ Dados selecionados removidos');
            alert('✅ Dados selecionados removidos com sucesso!');
            
            // Fechar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalDados'));
            modal.hide();
            
            this.atualizarInformacoes();
            
        } catch (error) {
            console.error('❌ Erro ao limpar dados:', error);
            alert('❌ Erro ao limpar dados: ' + error.message);
        }
    }

    // Reset completo do sistema
    resetCompleto() {
        const confirmacao1 = confirm('⚠️ ATENÇÃO CRÍTICA!\n\nVocê está prestes a APAGAR TODOS OS DADOS do sistema.\n\nEsta ação é IRREVERSÍVEL e removerá:\n- Todos os abastecimentos\n- Todas as manutenções\n- Todos os dados de caixa\n- Todos os veículos\n- Todos os fornecedores\n- Todas as configurações\n\nDeseja continuar?');
        
        if (!confirmacao1) return;
        
        const confirmacao2 = confirm('⚠️ ÚLTIMA CONFIRMAÇÃO!\n\nTem certeza ABSOLUTA que deseja apagar TODOS os dados?\n\nEsta é sua última chance de cancelar!');
        
        if (!confirmacao2) return;
        
        // Solicitar confirmação por texto
        const textoConfirmacao = prompt('Para confirmar, digite "APAGAR TUDO" (sem aspas):');
        
        if (textoConfirmacao !== 'APAGAR TUDO') {
            alert('❌ Confirmação incorreta. Reset cancelado.');
            return;
        }
        
        try {
            console.log('💥 Executando reset completo do sistema...');
            
            // Limpar completamente o localStorage
            localStorage.clear();
            
            // Limpar sessionStorage
            sessionStorage.clear();
            
            console.log('✅ Reset completo executado');
            alert('✅ Reset completo executado!\n\nTodos os dados foram removidos.\nA página será recarregada.');
            
            // Recarregar página
            window.location.reload();
            
        } catch (error) {
            console.error('❌ Erro no reset completo:', error);
            alert('❌ Erro no reset completo: ' + error.message);
        }
    }
}

// Funções globais para os botões
function atualizarInformacoes() {
    if (window.configSystem) {
        window.configSystem.atualizarInformacoes();
    }
}

function fazerBackup() {
    if (window.configSystem) {
        window.configSystem.fazerBackup();
    }
}

function restaurarBackup() {
    if (window.configSystem) {
        window.configSystem.restaurarBackup();
    }
}

function limparCache() {
    if (window.configSystem) {
        window.configSystem.limparCache();
    }
}

function resetConfiguracoes() {
    if (window.configSystem) {
        window.configSystem.resetConfiguracoes();
    }
}

function mostrarOpcoesDados() {
    if (window.configSystem) {
        window.configSystem.mostrarOpcoesDados();
    }
}

function limparDadosSelecionados() {
    if (window.configSystem) {
        window.configSystem.limparDadosSelecionados();
    }
}

function resetCompleto() {
    if (window.configSystem) {
        window.configSystem.resetCompleto();
    }
}

// Inicializar sistema quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.configSystem = new ConfiguracaoSystem();
});
