import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { supabase } from '../config/supabase';
import { authAPI } from '../services/api';
import toast from 'react-hot-toast';

// Estado inicial
const initialState = {
  user: null,
  session: null,
  loading: true,
  error: null,
};

// Actions
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_SESSION: 'SET_SESSION',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        loading: false,
        error: null,
      };
    case AUTH_ACTIONS.SET_SESSION:
      return {
        ...state,
        session: action.payload,
      };
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        loading: false,
      };
    default:
      return state;
  }
};

// Context
const AuthContext = createContext();

// Provider
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Verificar sessão inicial
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Erro ao obter sessão:', error);
          dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: error.message });
          return;
        }

        if (session) {
          dispatch({ type: AUTH_ACTIONS.SET_SESSION, payload: session });
          
          // Buscar dados do usuário
          try {
            const userData = await authAPI.getProfile();
            dispatch({ type: AUTH_ACTIONS.SET_USER, payload: userData.user });
          } catch (profileError) {
            console.error('Erro ao buscar perfil:', profileError);
            // Se falhar ao buscar perfil, fazer logout
            await logout();
          }
        } else {
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } catch (error) {
        console.error('Erro na inicialização da autenticação:', error);
        dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: error.message });
      }
    };

    initializeAuth();

    // Listener para mudanças na autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session);
        
        if (event === 'SIGNED_IN' && session) {
          dispatch({ type: AUTH_ACTIONS.SET_SESSION, payload: session });
          
          try {
            const userData = await authAPI.getProfile();
            dispatch({ type: AUTH_ACTIONS.SET_USER, payload: userData.user });
          } catch (error) {
            console.error('Erro ao buscar perfil após login:', error);
            toast.error('Erro ao carregar dados do usuário');
          }
        } else if (event === 'SIGNED_OUT') {
          dispatch({ type: AUTH_ACTIONS.LOGOUT });
        } else if (event === 'TOKEN_REFRESHED' && session) {
          dispatch({ type: AUTH_ACTIONS.SET_SESSION, payload: session });
        }
      }
    );

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  // Função de login
  const login = async (email, password) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: null });

      const response = await authAPI.login({ email, password });
      
      // O Supabase já vai disparar o evento SIGNED_IN
      // que vai atualizar o estado automaticamente
      
      toast.success('Login realizado com sucesso!');
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || 'Erro ao fazer login';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Função de registro
  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: null });

      const response = await authAPI.register(userData);
      
      toast.success('Conta criada com sucesso! Faça login para continuar.');
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || 'Erro ao criar conta';
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Função de logout
  const logout = async () => {
    try {
      await authAPI.logout();
      // O evento SIGNED_OUT será disparado automaticamente
      toast.success('Logout realizado com sucesso!');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      // Mesmo com erro, limpar o estado local
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      toast.error('Erro ao fazer logout, mas você foi desconectado localmente');
    }
  };

  // Função para atualizar perfil
  const updateProfile = async (profileData) => {
    try {
      const response = await authAPI.updateProfile(profileData);
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.user });
      toast.success('Perfil atualizado com sucesso!');
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || 'Erro ao atualizar perfil';
      toast.error(errorMessage);
      throw error;
    }
  };

  // Função para verificar se é admin
  const isAdmin = () => {
    return state.user?.role === 'admin';
  };

  // Função para obter token de acesso
  const getAccessToken = () => {
    return state.session?.access_token;
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    isAdmin,
    getAccessToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook personalizado
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};
