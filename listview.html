<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Listview - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <style>
        .listview-container {
            padding: 20px;
        }
        .data-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section-title h3 {
            margin: 0;
            font-size: 1.4rem;
        }
        .section-actions {
            display: flex;
            gap: 10px;
        }
        .totals-bar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
        }
        .total-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        .total-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }
        .total-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .data-table {
            margin: 0;
        }
        .data-table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #495057;
            padding: 15px;
        }
        .data-table td {
            padding: 12px 15px;
            vertical-align: middle;
        }
        .data-table tbody tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-maintenance { background: #fff3cd; color: #856404; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #d4edda; color: #155724; }
        .filters-bar {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .filter-group label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #495057;
        }
        .btn-export {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        .btn-export:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
            color: white;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="listview.html" class="active"><i class="fas fa-list"></i> Listview</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="top-bar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Listview - Dados Detalhados</h1>
            </div>
            
            <div class="top-bar-right">
                <div class="user-info">
                    <span id="userName">Usuário</span>
                    <i class="fas fa-user-circle"></i>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            <div class="listview-container">
                <!-- Filtros -->
                <div class="filters-bar">
                    <div class="filter-group">
                        <label>Período:</label>
                        <select class="form-select form-select-sm" id="periodFilter">
                            <option value="7">Últimos 7 dias</option>
                            <option value="30" selected>Últimos 30 dias</option>
                            <option value="90">Últimos 3 meses</option>
                            <option value="365">Último ano</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Categoria:</label>
                        <select class="form-select form-select-sm" id="categoryFilter">
                            <option value="all">Todas</option>
                            <option value="fuel">Combustível</option>
                            <option value="maintenance">Manutenção</option>
                            <option value="washing">Lavagem</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Veículo:</label>
                        <select class="form-select form-select-sm" id="vehicleFilter">
                            <option value="all">Todos</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>&nbsp;</label>
                        <div class="btn-group">
                            <button class="btn btn-primary btn-sm" onclick="applyFilters()">
                                <i class="fas fa-filter"></i> Filtrar
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                <i class="fas fa-times"></i> Limpar
                            </button>
                            <button class="btn btn-export btn-sm" onclick="exportData()">
                                <i class="fas fa-download"></i> Exportar
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Seção de Abastecimento -->
                <div class="data-section" id="fuelSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-gas-pump"></i>
                            <h3>Abastecimento</h3>
                        </div>
                        <div class="section-actions">
                            <button class="btn btn-light btn-sm" onclick="toggleSection('fuel')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="totals-bar">
                        <div class="total-item">
                            <div class="total-value" id="totalFuelLiters">0</div>
                            <div class="total-label">Litros</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="totalFuelCost">R$ 0,00</div>
                            <div class="total-label">Custo Total</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="avgFuelPrice">R$ 0,00</div>
                            <div class="total-label">Preço Médio/L</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="totalFuelRecords">0</div>
                            <div class="total-label">Registros</div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Veículo</th>
                                    <th>Litros</th>
                                    <th>Preço/L</th>
                                    <th>Total</th>
                                    <th>Posto</th>
                                    <th>KM</th>
                                </tr>
                            </thead>
                            <tbody id="fuelTableBody">
                                <!-- Dados carregados via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Seção de Manutenção -->
                <div class="data-section" id="maintenanceSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-wrench"></i>
                            <h3>Manutenção</h3>
                        </div>
                        <div class="section-actions">
                            <button class="btn btn-light btn-sm" onclick="toggleSection('maintenance')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="totals-bar">
                        <div class="total-item">
                            <div class="total-value" id="totalMaintenanceCost">R$ 0,00</div>
                            <div class="total-label">Custo Total</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="avgMaintenanceCost">R$ 0,00</div>
                            <div class="total-label">Custo Médio</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="totalMaintenanceRecords">0</div>
                            <div class="total-label">Serviços</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="pendingMaintenance">0</div>
                            <div class="total-label">Pendentes</div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Veículo</th>
                                    <th>Serviço</th>
                                    <th>Custo</th>
                                    <th>Status</th>
                                    <th>Prioridade</th>
                                    <th>Próxima</th>
                                </tr>
                            </thead>
                            <tbody id="maintenanceTableBody">
                                <!-- Dados carregados via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Seção de Lavagem -->
                <div class="data-section" id="washingSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-tint"></i>
                            <h3>Lavagem</h3>
                        </div>
                        <div class="section-actions">
                            <button class="btn btn-light btn-sm" onclick="toggleSection('washing')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="totals-bar">
                        <div class="total-item">
                            <div class="total-value" id="totalWashingCost">R$ 0,00</div>
                            <div class="total-label">Custo Total</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="avgWashingCost">R$ 0,00</div>
                            <div class="total-label">Custo Médio</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="totalWashingRecords">0</div>
                            <div class="total-label">Lavagens</div>
                        </div>
                        <div class="total-item">
                            <div class="total-value" id="washingFrequency">0</div>
                            <div class="total-label">Por Mês</div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Veículo</th>
                                    <th>Tipo</th>
                                    <th>Custo</th>
                                    <th>Local</th>
                                    <th>Observações</th>
                                </tr>
                            </thead>
                            <tbody id="washingTableBody">
                                <!-- Dados carregados via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/listview.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
