<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revisão - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
    <link href="css/font-size-adjustments.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html" class="active"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                        <li><a href="configuracoes.html"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Revisão</h1>
            </div>
            
            <div class="topbar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revisão Content -->
        <div class="dashboard-content">
            <!-- Action Bar -->
            <div class="action-bar">
                <button class="btn btn-primary" onclick="openRevisaoModal()">
                    <i class="fas fa-plus"></i> Nova Revisão
                </button>
                
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Buscar por veículo, placa..." id="searchInput">
                    </div>
                    
                    <select id="filterStatus" class="form-select">
                        <option value="">Todos os status</option>
                        <option value="agendada">Agendada</option>
                        <option value="em_andamento">Em Andamento</option>
                        <option value="concluida">Concluída</option>
                        <option value="vencida">Vencida</option>
                    </select>
                    
                    <select id="filterTipo" class="form-select">
                        <option value="">Todos os tipos</option>
                        <option value="preventiva">Preventiva</option>
                        <option value="periodica">Periódica</option>
                        <option value="obrigatoria">Obrigatória</option>
                    </select>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalRevisoes">24</h3>
                        <p>Total de Revisões</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="revisoesAgendadas">8</h3>
                        <p>Agendadas</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="revisoesConcluidas">14</h3>
                        <p>Concluídas</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="revisoesVencidas">2</h3>
                        <p>Vencidas</p>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="tabs-container">
                <ul class="nav nav-tabs" id="revisaoTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="proximas-tab" data-bs-toggle="tab" data-bs-target="#proximas" type="button" role="tab">
                            <i class="fas fa-calendar-alt"></i> Próximas Revisões
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="historico-tab" data-bs-toggle="tab" data-bs-target="#historico" type="button" role="tab">
                            <i class="fas fa-history"></i> Histórico
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="calendario-tab" data-bs-toggle="tab" data-bs-target="#calendario" type="button" role="tab">
                            <i class="fas fa-calendar"></i> Calendário
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="revisaoTabContent">
                    <!-- Próximas Revisões -->
                    <div class="tab-pane fade show active" id="proximas" role="tabpanel">
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table" id="proximasRevisoesTable">
                                    <thead>
                                        <tr>
                                            <th>Veículo</th>
                                            <th>Placa</th>
                                            <th>Tipo</th>
                                            <th>Data Prevista</th>
                                            <th>KM Atual</th>
                                            <th>KM Revisão</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="proximasRevisoesTableBody">
                                        <!-- Dados serão carregados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Histórico -->
                    <div class="tab-pane fade" id="historico" role="tabpanel">
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table" id="historicoRevisoesTable">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th>Veículo</th>
                                            <th>Placa</th>
                                            <th>Tipo</th>
                                            <th>Oficina</th>
                                            <th>Valor</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="historicoRevisoesTableBody">
                                        <!-- Dados serão carregados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Calendário -->
                    <div class="tab-pane fade" id="calendario" role="tabpanel">
                        <div class="calendar-container">
                            <div id="calendar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nova Revisão -->
    <div class="modal fade" id="revisaoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Nova Revisão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <form id="revisaoForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="veiculo">Veículo *</label>
                                    <select id="veiculo" name="veiculo" class="form-select" required>
                                        <option value="">Selecione o veículo</option>
                                    </select>
                                    <div id="kmAtualDisplay" style="display: none;" class="mt-2"></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tipoRevisao">Tipo de Revisão *</label>
                                    <select id="tipoRevisao" name="tipoRevisao" class="form-select" required>
                                        <option value="">Selecione</option>
                                        <option value="preventiva">Preventiva</option>
                                        <option value="periodica">Periódica</option>
                                        <option value="obrigatoria">Obrigatória</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dataAgendamento">Data de Agendamento *</label>
                                    <input type="datetime-local" id="dataAgendamento" name="dataAgendamento" class="form-control" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="kmRevisao">Quilometragem da Revisão</label>
                                    <input type="number" id="kmRevisao" name="kmRevisao" class="form-control">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="oficina">Oficina/Concessionária *</label>
                                    <input type="text" id="oficina" name="oficina" class="form-control" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contato">Contato</label>
                                    <input type="text" id="contato" name="contato" class="form-control">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="valorEstimado">Valor Estimado</label>
                                    <input type="number" id="valorEstimado" name="valorEstimado" class="form-control" step="0.01">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" name="status" class="form-select">
                                        <option value="agendada">Agendada</option>
                                        <option value="em_andamento">Em Andamento</option>
                                        <option value="concluida">Concluída</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="itensRevisao">Itens da Revisão</label>
                            <textarea id="itensRevisao" name="itensRevisao" class="form-control" rows="3" placeholder="Ex: Troca de óleo, filtros, velas..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="observacoes">Observações</label>
                            <textarea id="observacoes" name="observacoes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar Revisão</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/revisao.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
