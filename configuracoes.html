<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --shadow-light: 0 2px 10px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.12);
            --shadow-heavy: 0 8px 30px rgba(0,0,0,0.15);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .content {
            padding: 30px;
        }

        /* Seções de Configuração Melhoradas */
        .config-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-medium);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .config-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 20px 20px 0 0;
        }

        .config-section:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .config-header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 25px;
            position: relative;
        }

        .config-header h4 {
            color: var(--secondary-color);
            margin: 0;
            font-weight: 600;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .config-header h4 i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.2em;
        }

        .config-header p {
            color: #6c757d;
            margin: 8px 0 0 0;
            font-size: 0.95em;
            line-height: 1.5;
        }

        /* Zona de Perigo Melhorada */
        .danger-zone {
            border: 2px solid var(--danger-color);
            border-radius: 20px;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            position: relative;
            overflow: hidden;
        }

        .danger-zone::before {
            background: var(--gradient-danger);
        }

        .danger-zone .config-header {
            border-bottom-color: var(--danger-color);
        }

        .danger-zone .config-header h4 {
            color: var(--danger-color);
        }

        .danger-zone .config-header h4 i {
            background: var(--gradient-danger);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Opções de Reset Melhoradas */
        .reset-option {
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .reset-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .reset-option:hover {
            background: #f8f9fa;
            border-color: var(--primary-color);
            transform: translateX(5px);
            box-shadow: var(--shadow-light);
        }

        .reset-option:hover::before {
            left: 100%;
        }

        .reset-option h6 {
            margin: 0 0 10px 0;
            color: var(--secondary-color);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .reset-option p {
            margin: 0;
            font-size: 0.9em;
            color: #6c757d;
            line-height: 1.4;
        }

        /* Botões Melhorados */
        .btn {
            border-radius: 12px;
            font-weight: 500;
            padding: 12px 24px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-reset {
            background: var(--gradient-danger);
            color: white;
            font-weight: 600;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
            color: white;
        }

        .btn-success {
            background: var(--gradient-success);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
            color: white;
        }

        .btn-warning {
            background: var(--gradient-warning);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        /* Informações do Sistema Melhoradas */
        .system-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
            position: relative;
        }

        .system-info h6 {
            color: var(--secondary-color);
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .system-info .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-size: 0.95em;
        }

        .system-info .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .system-info .info-label {
            color: #6c757d;
            font-weight: 500;
        }

        .system-info .info-value {
            color: var(--secondary-color);
            font-weight: 600;
            background: white;
            padding: 4px 12px;
            border-radius: 20px;
            box-shadow: var(--shadow-light);
        }

        /* Cards de Estatísticas */
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stats-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.5rem;
            color: white;
        }

        .stats-card.primary .icon {
            background: var(--gradient-primary);
        }

        .stats-card.success .icon {
            background: var(--gradient-success);
        }

        .stats-card.warning .icon {
            background: var(--gradient-warning);
        }

        .stats-card.danger .icon {
            background: var(--gradient-danger);
        }

        .stats-card h3 {
            margin: 0 0 5px 0;
            color: var(--secondary-color);
            font-weight: 700;
        }

        .stats-card p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9em;
        }

        /* Animações */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .config-section {
            animation: fadeInUp 0.6s ease forwards;
        }

        .config-section:nth-child(1) { animation-delay: 0.1s; }
        .config-section:nth-child(2) { animation-delay: 0.2s; }
        .config-section:nth-child(3) { animation-delay: 0.3s; }
        .config-section:nth-child(4) { animation-delay: 0.4s; }

        /* Responsividade */
        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }

            .config-section {
                padding: 20px;
                margin-bottom: 20px;
            }

            .reset-option {
                padding: 15px;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-medium);
            border: none;
            min-width: 300px;
            margin-bottom: 10px;
        }

        .toast.success {
            border-left: 4px solid var(--success-color);
        }

        .toast.error {
            border-left: 4px solid var(--danger-color);
        }

        .toast.warning {
            border-left: 4px solid var(--warning-color);
        }

        /* Melhorias nos Modais */
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: var(--shadow-heavy);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px 20px 0 0;
            border-bottom: none;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        .modal-body {
            padding: 30px;
        }

        .modal-footer {
            border-top: 1px solid #e9ecef;
            padding: 20px 30px;
        }

        .form-check {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .form-check:hover {
            border-color: var(--primary-color);
            background: #f8f9fa;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-label {
            font-weight: 500;
            color: var(--secondary-color);
            cursor: pointer;
        }

        .form-check-label i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        /* Efeitos de Hover nos Botões */
        .btn:not(:disabled):not(.disabled):active,
        .btn:not(:disabled):not(.disabled).active {
            transform: translateY(1px);
        }

        /* Melhorias na Responsividade */
        @media (max-width: 576px) {
            .stats-card h3 {
                font-size: 1.5rem;
            }

            .config-section {
                margin-bottom: 20px;
            }

            .reset-option {
                flex-direction: column;
                text-align: center;
            }

            .reset-option .btn {
                margin-top: 15px;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-truck"></i> Gestão de Frotas</h3>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
            <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
            <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
            <li><a href="lavagem.html"><i class="fas fa-spray-can"></i> Lavagem</a></li>
            <li><a href="revisao.html"><i class="fas fa-clipboard-check"></i> Revisão</a></li>
            <li><a href="caixa.html"><i class="fas fa-wallet"></i> Controle de Caixa</a></li>
            <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
            <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
            <li><a href="listview.html"><i class="fas fa-list"></i> Listview</a></li>
            <li class="has-submenu">
                <a href="#" class="active"><i class="fas fa-cog"></i> Configurações</a>
                <ul class="submenu" style="display: block;">
                    <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                    <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                    <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    <li><a href="configuracoes.html" class="active"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Configurações do Sistema</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Cards de Estatísticas Rápidas -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stats-card primary">
                        <div class="icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 id="totalRegistrosCard">0</h3>
                        <p>Total de Registros</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card success">
                        <div class="icon">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <h3 id="espacoUtilizadoCard">0 KB</h3>
                        <p>Espaço Utilizado</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card warning">
                        <div class="icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3 id="ultimaAtualizacaoCard">--</h3>
                        <p>Última Atualização</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card danger">
                        <div class="icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>LocalStorage</h3>
                        <p>Tipo de Armazenamento</p>
                    </div>
                </div>
            </div>
            <!-- Informações do Sistema -->
            <div class="config-section">
                <div class="config-header">
                    <h4><i class="fas fa-info-circle"></i> Informações do Sistema</h4>
                    <p>Informações gerais sobre o sistema e armazenamento de dados</p>
                </div>
                
                <div class="system-info">
                    <h6>Status do Sistema</h6>
                    <div class="info-item">
                        <span class="info-label">Versão:</span>
                        <span class="info-value">1.0.0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tipo de Armazenamento:</span>
                        <span class="info-value">LocalStorage (Navegador)</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total de Registros:</span>
                        <span class="info-value" id="totalRegistros">Calculando...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Espaço Utilizado:</span>
                        <span class="info-value" id="espacoUtilizado">Calculando...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Última Atualização:</span>
                        <span class="info-value" id="ultimaAtualizacao">-</span>
                    </div>
                </div>
                
                <button class="btn btn-outline-primary" onclick="atualizarInformacoes()">
                    <i class="fas fa-sync-alt"></i> Atualizar Informações
                </button>
            </div>

            <!-- Backup e Restauração -->
            <div class="config-section">
                <div class="config-header">
                    <h4><i class="fas fa-download"></i> Backup e Restauração</h4>
                    <p>Faça backup dos seus dados ou restaure de um backup anterior</p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Fazer Backup</h6>
                        <p class="text-muted">Baixe todos os dados do sistema em um arquivo JSON</p>
                        <button class="btn btn-success" onclick="fazerBackup()">
                            <i class="fas fa-download"></i> Baixar Backup
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>Restaurar Backup</h6>
                        <p class="text-muted">Carregue dados de um arquivo de backup</p>
                        <input type="file" id="backupFile" accept=".json" class="form-control mb-2">
                        <button class="btn btn-warning" onclick="restaurarBackup()">
                            <i class="fas fa-upload"></i> Restaurar Backup
                        </button>
                    </div>
                </div>
            </div>

            <!-- Reset do Sistema - ZONA DE PERIGO -->
            <div class="config-section danger-zone">
                <div class="config-header">
                    <h4><i class="fas fa-exclamation-triangle"></i> Reset do Sistema</h4>
                    <p>⚠️ ATENÇÃO: Estas ações são irreversíveis e podem apagar dados permanentemente</p>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Limpar Cache do Sistema</h6>
                            <p>Remove dados temporários e cache. Não afeta os dados principais.</p>
                        </div>
                        <button class="btn btn-outline-warning" onclick="limparCache()">
                            <i class="fas fa-broom"></i> Limpar Cache
                        </button>
                    </div>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Reset Configurações</h6>
                            <p>Restaura todas as configurações para os valores padrão.</p>
                        </div>
                        <button class="btn btn-warning" onclick="resetConfiguracoes()">
                            <i class="fas fa-undo"></i> Reset Configurações
                        </button>
                    </div>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Limpar Dados Específicos</h6>
                            <p>Remove dados de módulos específicos (abastecimento, manutenção, etc.)</p>
                        </div>
                        <button class="btn btn-warning" onclick="mostrarOpcoesDados()">
                            <i class="fas fa-filter"></i> Selecionar Dados
                        </button>
                    </div>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Reset Completo do Sistema</h6>
                            <p><strong>⚠️ CUIDADO:</strong> Remove TODOS os dados do sistema. Esta ação é irreversível!</p>
                        </div>
                        <button class="btn btn-reset" onclick="resetCompleto()">
                            <i class="fas fa-trash-alt"></i> Reset Completo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Seleção de Dados -->
    <div class="modal fade" id="modalDados" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Selecionar Dados para Limpar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-warning">⚠️ Selecione quais dados deseja remover:</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkAbastecimento">
                        <label class="form-check-label" for="checkAbastecimento">
                            <i class="fas fa-gas-pump"></i> Dados de Abastecimento
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkManutencao">
                        <label class="form-check-label" for="checkManutencao">
                            <i class="fas fa-wrench"></i> Dados de Manutenção
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkLavagem">
                        <label class="form-check-label" for="checkLavagem">
                            <i class="fas fa-spray-can"></i> Dados de Lavagem
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkCaixa">
                        <label class="form-check-label" for="checkCaixa">
                            <i class="fas fa-wallet"></i> Dados de Controle de Caixa
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkVeiculos">
                        <label class="form-check-label" for="checkVeiculos">
                            <i class="fas fa-car"></i> Dados de Veículos
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkFornecedores">
                        <label class="form-check-label" for="checkFornecedores">
                            <i class="fas fa-building"></i> Dados de Fornecedores
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-warning" onclick="limparDadosSelecionados()">
                        <i class="fas fa-trash"></i> Limpar Selecionados
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Container para Notificações Toast -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/configuracoes.js"></script>
</body>
</html>
