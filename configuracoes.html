<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .config-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .config-header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .config-header h4 {
            color: #2c3e50;
            margin: 0;
        }
        
        .config-header p {
            color: #6c757d;
            margin: 5px 0 0 0;
            font-size: 0.9em;
        }
        
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 10px;
            background: #fff5f5;
        }
        
        .danger-zone .config-header {
            border-bottom-color: #dc3545;
        }
        
        .danger-zone .config-header h4 {
            color: #dc3545;
        }
        
        .reset-option {
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .reset-option:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }
        
        .reset-option h6 {
            margin: 0 0 8px 0;
            color: #495057;
        }
        
        .reset-option p {
            margin: 0;
            font-size: 0.85em;
            color: #6c757d;
        }
        
        .btn-reset {
            background: #dc3545;
            border-color: #dc3545;
            color: white;
            font-weight: 500;
        }
        
        .btn-reset:hover {
            background: #c82333;
            border-color: #bd2130;
            color: white;
        }
        
        .system-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .system-info h6 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .system-info .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .system-info .info-label {
            color: #6c757d;
        }
        
        .system-info .info-value {
            color: #495057;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-truck"></i> Gestão de Frotas</h3>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
            <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
            <li><a href="manutencao.html"><i class="fas fa-wrench"></i> Manutenção</a></li>
            <li><a href="lavagem.html"><i class="fas fa-car-wash"></i> Lavagem</a></li>
            <li><a href="revisao.html"><i class="fas fa-clipboard-check"></i> Revisão</a></li>
            <li><a href="caixa.html"><i class="fas fa-wallet"></i> Controle de Caixa</a></li>
            <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
            <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
            <li><a href="listview.html"><i class="fas fa-list"></i> Listview</a></li>
            <li class="has-submenu">
                <a href="#" class="active"><i class="fas fa-cog"></i> Configurações</a>
                <ul class="submenu" style="display: block;">
                    <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                    <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                    <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    <li><a href="configuracoes.html" class="active"><i class="fas fa-sliders-h"></i> Sistema</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Configurações do Sistema</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Informações do Sistema -->
            <div class="config-section">
                <div class="config-header">
                    <h4><i class="fas fa-info-circle"></i> Informações do Sistema</h4>
                    <p>Informações gerais sobre o sistema e armazenamento de dados</p>
                </div>
                
                <div class="system-info">
                    <h6>Status do Sistema</h6>
                    <div class="info-item">
                        <span class="info-label">Versão:</span>
                        <span class="info-value">1.0.0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tipo de Armazenamento:</span>
                        <span class="info-value">LocalStorage (Navegador)</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Total de Registros:</span>
                        <span class="info-value" id="totalRegistros">Calculando...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Espaço Utilizado:</span>
                        <span class="info-value" id="espacoUtilizado">Calculando...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Última Atualização:</span>
                        <span class="info-value" id="ultimaAtualizacao">-</span>
                    </div>
                </div>
                
                <button class="btn btn-outline-primary" onclick="atualizarInformacoes()">
                    <i class="fas fa-sync-alt"></i> Atualizar Informações
                </button>
            </div>

            <!-- Backup e Restauração -->
            <div class="config-section">
                <div class="config-header">
                    <h4><i class="fas fa-download"></i> Backup e Restauração</h4>
                    <p>Faça backup dos seus dados ou restaure de um backup anterior</p>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Fazer Backup</h6>
                        <p class="text-muted">Baixe todos os dados do sistema em um arquivo JSON</p>
                        <button class="btn btn-success" onclick="fazerBackup()">
                            <i class="fas fa-download"></i> Baixar Backup
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>Restaurar Backup</h6>
                        <p class="text-muted">Carregue dados de um arquivo de backup</p>
                        <input type="file" id="backupFile" accept=".json" class="form-control mb-2">
                        <button class="btn btn-warning" onclick="restaurarBackup()">
                            <i class="fas fa-upload"></i> Restaurar Backup
                        </button>
                    </div>
                </div>
            </div>

            <!-- Reset do Sistema - ZONA DE PERIGO -->
            <div class="config-section danger-zone">
                <div class="config-header">
                    <h4><i class="fas fa-exclamation-triangle"></i> Reset do Sistema</h4>
                    <p>⚠️ ATENÇÃO: Estas ações são irreversíveis e podem apagar dados permanentemente</p>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Limpar Cache do Sistema</h6>
                            <p>Remove dados temporários e cache. Não afeta os dados principais.</p>
                        </div>
                        <button class="btn btn-outline-warning" onclick="limparCache()">
                            <i class="fas fa-broom"></i> Limpar Cache
                        </button>
                    </div>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Reset Configurações</h6>
                            <p>Restaura todas as configurações para os valores padrão.</p>
                        </div>
                        <button class="btn btn-warning" onclick="resetConfiguracoes()">
                            <i class="fas fa-undo"></i> Reset Configurações
                        </button>
                    </div>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Limpar Dados Específicos</h6>
                            <p>Remove dados de módulos específicos (abastecimento, manutenção, etc.)</p>
                        </div>
                        <button class="btn btn-warning" onclick="mostrarOpcoesDados()">
                            <i class="fas fa-filter"></i> Selecionar Dados
                        </button>
                    </div>
                </div>
                
                <div class="reset-option">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Reset Completo do Sistema</h6>
                            <p><strong>⚠️ CUIDADO:</strong> Remove TODOS os dados do sistema. Esta ação é irreversível!</p>
                        </div>
                        <button class="btn btn-reset" onclick="resetCompleto()">
                            <i class="fas fa-trash-alt"></i> Reset Completo
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Seleção de Dados -->
    <div class="modal fade" id="modalDados" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Selecionar Dados para Limpar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-warning">⚠️ Selecione quais dados deseja remover:</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkAbastecimento">
                        <label class="form-check-label" for="checkAbastecimento">
                            <i class="fas fa-gas-pump"></i> Dados de Abastecimento
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkManutencao">
                        <label class="form-check-label" for="checkManutencao">
                            <i class="fas fa-wrench"></i> Dados de Manutenção
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkLavagem">
                        <label class="form-check-label" for="checkLavagem">
                            <i class="fas fa-car-wash"></i> Dados de Lavagem
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkCaixa">
                        <label class="form-check-label" for="checkCaixa">
                            <i class="fas fa-wallet"></i> Dados de Controle de Caixa
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkVeiculos">
                        <label class="form-check-label" for="checkVeiculos">
                            <i class="fas fa-car"></i> Dados de Veículos
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkFornecedores">
                        <label class="form-check-label" for="checkFornecedores">
                            <i class="fas fa-building"></i> Dados de Fornecedores
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-warning" onclick="limparDadosSelecionados()">
                        <i class="fas fa-trash"></i> Limpar Selecionados
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/configuracoes.js"></script>
</body>
</html>
