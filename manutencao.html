<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manutenção - Sistema de Gestão de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-truck-moving"></i>
            <h4>Gestão de Frotas</h4>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="abastecimento.html"><i class="fas fa-gas-pump"></i> Abastecimento</a></li>
                <li><a href="revisao.html"><i class="fas fa-tools"></i> Revisão</a></li>
                <li><a href="manutencao.html" class="active"><i class="fas fa-wrench"></i> Manutenção</a></li>
                <li><a href="lavagem.html"><i class="fas fa-car-wash"></i> Lavagem</a></li>
                <li><a href="caixa.html"><i class="fas fa-wallet"></i> Controle de Caixa</a></li>
                <li><a href="relatorios.html"><i class="fas fa-chart-bar"></i> Relatórios</a></li>
                <li><a href="graficos.html"><i class="fas fa-chart-line"></i> Gráficos</a></li>
                <li class="has-submenu">
                    <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                    <ul class="submenu">
                        <li><a href="usuarios.html"><i class="fas fa-users"></i> Usuários</a></li>
                        <li><a href="veiculos.html"><i class="fas fa-car"></i> Veículos</a></li>
                        <li><a href="fornecedores.html"><i class="fas fa-building"></i> Fornecedores</a></li>
                    </ul>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Manutenção</h1>
            </div>
            
            <div class="topbar-right">
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </div>
                
                <div class="user-menu">
                    <img src="https://via.placeholder.com/40" alt="User" class="user-avatar">
                    <span class="user-name" id="userName">Usuário</span>
                    <div class="dropdown">
                        <i class="fas fa-chevron-down"></i>
                        <div class="dropdown-content">
                            <a href="#"><i class="fas fa-user"></i> Perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configurações</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Sair</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manutenção Content -->
        <div class="dashboard-content">
            <!-- Action Bar -->
            <div class="action-bar">
                <button class="btn btn-primary" onclick="openManutencaoModal()">
                    <i class="fas fa-plus"></i> Nova Manutenção
                </button>
                
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Buscar por veículo, placa..." id="searchInput">
                    </div>
                    
                    <select id="filterStatus" class="form-select">
                        <option value="">Todos os status</option>
                        <option value="agendada">Agendada</option>
                        <option value="em_andamento">Em Andamento</option>
                        <option value="concluida">Concluída</option>
                        <option value="cancelada">Cancelada</option>
                    </select>
                    
                    <select id="filterTipo" class="form-select">
                        <option value="">Todos os tipos</option>
                        <option value="preventiva">Preventiva</option>
                        <option value="corretiva">Corretiva</option>
                        <option value="emergencial">Emergencial</option>
                    </select>
                    
                    <select id="filterPrioridade" class="form-select">
                        <option value="">Todas as prioridades</option>
                        <option value="baixa">Baixa</option>
                        <option value="media">Média</option>
                        <option value="alta">Alta</option>
                        <option value="critica">Crítica</option>
                    </select>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalManutencoes">32</h3>
                        <p>Total de Manutenções</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="manutencoesAndamento">5</h3>
                        <p>Em Andamento</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="manutencoesCriticas">3</h3>
                        <p>Críticas</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="custoTotal">R$ 8.450</h3>
                        <p>Custo Total</p>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="tabs-container">
                <ul class="nav nav-tabs" id="manutencaoTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="pendentes-tab" data-bs-toggle="tab" data-bs-target="#pendentes" type="button" role="tab">
                            <i class="fas fa-clock"></i> Pendentes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="andamento-tab" data-bs-toggle="tab" data-bs-target="#andamento" type="button" role="tab">
                            <i class="fas fa-cog"></i> Em Andamento
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="historico-tab" data-bs-toggle="tab" data-bs-target="#historico" type="button" role="tab">
                            <i class="fas fa-history"></i> Histórico
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="manutencaoTabContent">
                    <!-- Pendentes -->
                    <div class="tab-pane fade show active" id="pendentes" role="tabpanel">
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table" id="pendentesTable">
                                    <thead>
                                        <tr>
                                            <th>Veículo</th>
                                            <th>Placa</th>
                                            <th>Tipo</th>
                                            <th>Problema</th>
                                            <th>Prioridade</th>
                                            <th>Data Agendada</th>
                                            <th>Oficina</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pendentesTableBody">
                                        <!-- Dados serão carregados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Em Andamento -->
                    <div class="tab-pane fade" id="andamento" role="tabpanel">
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table" id="andamentoTable">
                                    <thead>
                                        <tr>
                                            <th>Veículo</th>
                                            <th>Placa</th>
                                            <th>Tipo</th>
                                            <th>Problema</th>
                                            <th>Oficina</th>
                                            <th>Início</th>
                                            <th>Previsão</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="andamentoTableBody">
                                        <!-- Dados serão carregados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Histórico -->
                    <div class="tab-pane fade" id="historico" role="tabpanel">
                        <div class="table-container">
                            <div class="table-responsive">
                                <table class="table" id="historicoTable">
                                    <thead>
                                        <tr>
                                            <th>Data</th>
                                            <th>Veículo</th>
                                            <th>Placa</th>
                                            <th>Tipo</th>
                                            <th>Problema</th>
                                            <th>Oficina</th>
                                            <th>Valor</th>
                                            <th>Status</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="historicoTableBody">
                                        <!-- Dados serão carregados via JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nova Manutenção -->
    <div class="modal fade" id="manutencaoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Nova Manutenção</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <form id="manutencaoForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="veiculo">Veículo *</label>
                                    <select id="veiculo" name="veiculo" class="form-select" required>
                                        <option value="">Selecione o veículo</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tipoManutencao">Tipo de Manutenção *</label>
                                    <select id="tipoManutencao" name="tipoManutencao" class="form-select" required>
                                        <option value="">Selecione</option>
                                        <option value="preventiva">Preventiva</option>
                                        <option value="corretiva">Corretiva</option>
                                        <option value="emergencial">Emergencial</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="prioridade">Prioridade *</label>
                                    <select id="prioridade" name="prioridade" class="form-select" required>
                                        <option value="">Selecione</option>
                                        <option value="baixa">Baixa</option>
                                        <option value="media">Média</option>
                                        <option value="alta">Alta</option>
                                        <option value="critica">Crítica</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dataAgendamento">Data de Agendamento *</label>
                                    <input type="datetime-local" id="dataAgendamento" name="dataAgendamento" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="problema">Descrição do Problema *</label>
                            <textarea id="problema" name="problema" class="form-control" rows="3" required placeholder="Descreva o problema ou serviço a ser realizado"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="oficina">Oficina/Prestador *</label>
                                    <input type="text" id="oficina" name="oficina" class="form-control" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contato">Contato</label>
                                    <input type="text" id="contato" name="contato" class="form-control">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="valorEstimado">Valor Estimado</label>
                                    <input type="number" id="valorEstimado" name="valorEstimado" class="form-control" step="0.01">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="previsaoConclusao">Previsão de Conclusão</label>
                                    <input type="datetime-local" id="previsaoConclusao" name="previsaoConclusao" class="form-control">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="pecasNecessarias">Peças Necessárias</label>
                            <textarea id="pecasNecessarias" name="pecasNecessarias" class="form-control" rows="2" placeholder="Liste as peças que serão necessárias"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="observacoes">Observações</label>
                            <textarea id="observacoes" name="observacoes" class="form-control" rows="2"></textarea>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar Manutenção</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/manutencao.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
