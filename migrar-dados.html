<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migração de Dados - Sistema de Frotas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-success {
            background: linear-gradient(45deg, #51cf66, #40c057);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .progress {
            height: 25px;
            border-radius: 15px;
        }
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success { background-color: rgba(72, 187, 120, 0.2); }
        .log-warning { background-color: rgba(237, 137, 54, 0.2); }
        .log-error { background-color: rgba(245, 101, 101, 0.2); }
        .log-info { background-color: rgba(66, 153, 225, 0.2); }
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-badge {
            font-size: 0.9em;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="text-center text-white mb-4">
                    <h1><i class="fas fa-exchange-alt me-3"></i>Migração de Dados</h1>
                    <p class="lead">Migre seus dados do localStorage para o sistema de arquivos</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Status Atual -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle me-2"></i>Status Atual</h5>
                    </div>
                    <div class="card-body">
                        <div id="statusAtual">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Carregando...</span>
                                </div>
                                <p class="mt-2">Verificando sistema...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ações -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-cogs me-2"></i>Ações Disponíveis</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="btnVerificar" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Verificar Dados
                            </button>
                            <button id="btnMigrar" class="btn btn-success" disabled>
                                <i class="fas fa-arrow-right me-2"></i>Iniciar Migração
                            </button>
                            <button id="btnLimparLocal" class="btn btn-warning" disabled>
                                <i class="fas fa-broom me-2"></i>Limpar localStorage
                            </button>
                            <button id="btnStats" class="btn btn-info">
                                <i class="fas fa-chart-bar me-2"></i>Ver Estatísticas
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progresso -->
        <div class="row" id="progressContainer" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-tasks me-2"></i>Progresso da Migração</h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-center">
                            Preparando migração...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estatísticas -->
        <div class="row" id="statsContainer" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h5><i class="fas fa-chart-pie me-2"></i>Estatísticas de Armazenamento</h5>
                    </div>
                    <div class="card-body">
                        <div id="statsContent">
                            <!-- Estatísticas serão inseridas aqui -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log -->
        <div class="row" id="logContainer" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-terminal me-2"></i>Log de Operações</h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="logContent" class="log-container">
                            <!-- Log entries serão inseridas aqui -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/file-storage.js"></script>
    <script src="js/storage-adapter.js"></script>
    <script>
        let migrationInProgress = false;

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logContent = document.getElementById('logContent');
            
            logContainer.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateProgress(percentage, text) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            progressContainer.style.display = 'block';
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${percentage}%`;
            progressText.textContent = text;
        }

        async function verificarStatus() {
            try {
                log('🔍 Verificando status do sistema...', 'info');
                
                // Verificar localStorage
                const localStorageInfo = storageAdapter.getLocalStorageInfo();
                
                // Verificar FileStorage
                await storageAdapter.waitForReady();
                const fileStorageStats = await storageAdapter.getStats();
                
                const statusHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="stats-card">
                                <h6><i class="fas fa-database me-2"></i>localStorage</h6>
                                <p class="mb-1"><strong>${localStorageInfo.totalKeys}</strong> itens</p>
                                <p class="mb-0"><strong>${(localStorageInfo.totalSize / 1024).toFixed(2)}</strong> KB</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stats-card">
                                <h6><i class="fas fa-file me-2"></i>FileStorage</h6>
                                <p class="mb-1"><strong>${fileStorageStats ? fileStorageStats.totalKeys : 0}</strong> arquivos</p>
                                <p class="mb-0"><strong>${fileStorageStats ? (fileStorageStats.totalSize / 1024).toFixed(2) : 0}</strong> KB</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="status-badge ${localStorageInfo.totalKeys > 0 ? 'bg-warning' : 'bg-success'}">
                            ${localStorageInfo.totalKeys > 0 ? 'Migração Necessária' : 'Sistema Atualizado'}
                        </span>
                    </div>
                `;
                
                document.getElementById('statusAtual').innerHTML = statusHtml;
                
                // Habilitar botões conforme necessário
                document.getElementById('btnMigrar').disabled = localStorageInfo.totalKeys === 0;
                document.getElementById('btnLimparLocal').disabled = localStorageInfo.totalKeys === 0;
                
                log(`✅ Status verificado: ${localStorageInfo.totalKeys} itens no localStorage, ${fileStorageStats ? fileStorageStats.totalKeys : 0} arquivos no FileStorage`, 'success');
                
            } catch (error) {
                log(`❌ Erro ao verificar status: ${error.message}`, 'error');
                document.getElementById('statusAtual').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erro ao verificar status: ${error.message}
                    </div>
                `;
            }
        }

        async function iniciarMigracao() {
            if (migrationInProgress) return;
            
            try {
                migrationInProgress = true;
                document.getElementById('btnMigrar').disabled = true;
                
                log('🚀 Iniciando migração de dados...', 'info');
                updateProgress(0, 'Preparando migração...');
                
                // Simular progresso
                updateProgress(10, 'Verificando dados do localStorage...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                updateProgress(30, 'Iniciando transferência...');
                const result = await storageAdapter.migrateFromLocalStorage();
                
                updateProgress(80, 'Finalizando migração...');
                await new Promise(resolve => setTimeout(resolve, 500));
                
                updateProgress(100, 'Migração concluída!');
                
                if (result.success) {
                    log(`🎉 Migração concluída com sucesso! ${result.totalMigrated} itens migrados`, 'success');
                    
                    if (result.errors.length > 0) {
                        log(`⚠️ Alguns erros ocorreram: ${result.errors.join(', ')}`, 'warning');
                    }
                    
                    // Habilitar botão de limpeza
                    document.getElementById('btnLimparLocal').disabled = false;
                    
                    // Atualizar status
                    await verificarStatus();
                } else {
                    log(`❌ Falha na migração: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Erro durante migração: ${error.message}`, 'error');
                updateProgress(0, 'Erro na migração');
            } finally {
                migrationInProgress = false;
                document.getElementById('btnMigrar').disabled = false;
            }
        }

        async function limparLocalStorage() {
            if (!confirm('Tem certeza que deseja limpar o localStorage? Esta ação não pode ser desfeita.')) {
                return;
            }
            
            try {
                log('🧹 Limpando localStorage...', 'info');
                
                const result = await storageAdapter.clearLocalStorageAfterMigration();
                
                if (result.success) {
                    log(`✅ localStorage limpo: ${result.totalCleared} itens removidos`, 'success');
                    await verificarStatus();
                } else {
                    log(`❌ Erro ao limpar localStorage: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Erro ao limpar localStorage: ${error.message}`, 'error');
            }
        }

        async function mostrarEstatisticas() {
            try {
                log('📊 Carregando estatísticas...', 'info');
                
                const stats = await storageAdapter.getStats();
                const localInfo = storageAdapter.getLocalStorageInfo();
                
                let statsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-database me-2"></i>localStorage</h6>
                            <ul class="list-group">
                `;
                
                localInfo.keys.forEach(item => {
                    statsHtml += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            ${item.key}
                            <span class="badge bg-primary rounded-pill">${(item.size / 1024).toFixed(2)} KB</span>
                        </li>
                    `;
                });
                
                statsHtml += `
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-file me-2"></i>FileStorage</h6>
                            <ul class="list-group">
                `;
                
                if (stats && stats.keys) {
                    stats.keys.forEach(item => {
                        statsHtml += `
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                ${item.key}
                                <div>
                                    <span class="badge bg-success rounded-pill me-1">${item.itemCount} itens</span>
                                    <span class="badge bg-info rounded-pill">${(item.size / 1024).toFixed(2)} KB</span>
                                </div>
                            </li>
                        `;
                    });
                }
                
                statsHtml += `
                            </ul>
                        </div>
                    </div>
                `;
                
                document.getElementById('statsContent').innerHTML = statsHtml;
                document.getElementById('statsContainer').style.display = 'block';
                
                log('✅ Estatísticas carregadas', 'success');
                
            } catch (error) {
                log(`❌ Erro ao carregar estatísticas: ${error.message}`, 'error');
            }
        }

        // Event listeners
        document.getElementById('btnVerificar').addEventListener('click', verificarStatus);
        document.getElementById('btnMigrar').addEventListener('click', iniciarMigracao);
        document.getElementById('btnLimparLocal').addEventListener('click', limparLocalStorage);
        document.getElementById('btnStats').addEventListener('click', mostrarEstatisticas);

        // Inicializar página
        window.addEventListener('load', function() {
            log('🚀 Sistema de migração carregado', 'info');
            verificarStatus();
        });
    </script>
</body>
</html>
