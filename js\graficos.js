// Sistema de Gráficos e Análises
class ChartsManager {
    constructor() {
        this.charts = {};
        this.data = this.loadData();
        this.init();
    }

    init() {
        try {
            console.log('🚀 Inicializando sistema de gráficos...');

            // Verificar se Chart.js está carregado
            if (typeof Chart === 'undefined') {
                throw new Error('Chart.js não está carregado');
            }

            this.populateVehicleFilter();
            this.createAllCharts();
            this.updateStatistics();
            this.setupEventListeners();

            console.log('✅ Sistema de gráficos inicializado com sucesso');
            this.showNotification('Gráficos carregados com sucesso!', 'success');

        } catch (error) {
            console.error('❌ Erro ao inicializar sistema de gráficos:', error);
            this.showNotification('Erro ao carregar gráficos. Verifique a conexão.', 'error');
        }
    }

    // Configurar event listeners
    setupEventListeners() {
        // Filtros
        const periodFilter = document.getElementById('periodFilter');
        const vehicleFilter = document.getElementById('vehicleFilter');
        const chartTypeFilter = document.getElementById('chartTypeFilter');

        if (periodFilter) {
            periodFilter.addEventListener('change', () => this.updateCharts());
        }

        if (vehicleFilter) {
            vehicleFilter.addEventListener('change', () => this.updateCharts());
        }

        if (chartTypeFilter) {
            chartTypeFilter.addEventListener('change', () => this.updateCharts());
        }
    }

    // Carregar dados reais do sistema
    loadData() {
        console.log('📊 Carregando dados para gráficos...');

        const realData = {
            vehicles: this.loadVehiclesFromStorage(),
            fuelData: this.loadFuelDataFromStorage(),
            costsData: this.loadCostsDataFromStorage(),
            maintenanceData: this.loadMaintenanceDataFromStorage(),
            mileageData: this.loadMileageDataFromStorage()
        };

        console.log('📊 Dados reais carregados:', realData);

        // Se não há dados suficientes, gerar dados simulados
        const hasEnoughData = realData.vehicles.length > 0 || realData.fuelData.length > 0;

        if (!hasEnoughData) {
            console.log('⚠️ Dados insuficientes, gerando dados simulados...');
            return this.generateSimulatedData();
        }

        // Combinar dados reais com simulados para garantir visualização
        const simulatedData = this.generateSimulatedData();
        return this.combineRealAndSimulatedData(realData, simulatedData);
    }

    // Carregar veículos do localStorage
    loadVehiclesFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                return JSON.parse(savedVehicles);
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
            }
        }
        return [];
    }

    // Carregar dados de combustível do localStorage
    loadFuelDataFromStorage() {
        const savedFuel = localStorage.getItem('abastecimentos');
        if (savedFuel) {
            try {
                return JSON.parse(savedFuel);
            } catch (error) {
                console.error('Erro ao carregar dados de combustível:', error);
            }
        }
        return [];
    }

    // Carregar dados de custos do localStorage
    loadCostsDataFromStorage() {
        const abastecimentos = this.loadFuelDataFromStorage();
        const manutencoes = this.loadMaintenanceDataFromStorage();

        const combustivel = abastecimentos.reduce((total, item) => total + (item.valor || 0), 0);
        const manutencao = manutencoes.reduce((total, item) => total + (item.valorReal || item.valorEstimado || 0), 0);

        return {
            combustivel: combustivel,
            manutencao: manutencao,
            lavagem: 0, // Será implementado quando houver dados de lavagem
            outros: 0
        };
    }

    // Carregar dados de manutenção do localStorage
    loadMaintenanceDataFromStorage() {
        const savedMaintenance = localStorage.getItem('manutencoes');
        if (savedMaintenance) {
            try {
                return JSON.parse(savedMaintenance);
            } catch (error) {
                console.error('Erro ao carregar dados de manutenção:', error);
            }
        }
        return [];
    }

    // Carregar dados de quilometragem do localStorage
    loadMileageDataFromStorage() {
        const vehicles = this.loadVehiclesFromStorage();
        return vehicles.map(vehicle => ({
            vehicle: vehicle.plate,
            km: vehicle.km || 0
        }));
    }

    // Gerar dados simulados para demonstração
    generateSimulatedData() {
        console.log('🎲 Gerando dados simulados...');

        const today = new Date();
        const dates = [];
        const fuelData = [];
        const costsData = [];
        const maintenanceData = [];
        const mileageData = [];

        // Gerar datas dos últimos 30 dias
        for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            dates.push(date);
        }

        // Dados de combustível simulados
        dates.forEach((date, index) => {
            fuelData.push({
                date: date.toISOString().split('T')[0],
                liters: Math.random() * 50 + 30, // 30-80 litros
                cost: Math.random() * 300 + 150, // R$ 150-450
                vehicle: `ABC-${1234 + (index % 5)}`
            });
        });

        // Dados de custos simulados
        costsData.push(
            { category: 'Combustível', value: 15000, color: '#007bff' },
            { category: 'Manutenção', value: 8500, color: '#28a745' },
            { category: 'Lavagem', value: 2300, color: '#ffc107' },
            { category: 'Revisão', value: 4200, color: '#dc3545' },
            { category: 'Outros', value: 1800, color: '#6c757d' }
        );

        // Dados de manutenção simulados
        const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];
        months.forEach(month => {
            maintenanceData.push({
                month: month,
                count: Math.floor(Math.random() * 15) + 5, // 5-20 manutenções
                cost: Math.random() * 5000 + 2000 // R$ 2000-7000
            });
        });

        // Dados de quilometragem simulados
        const vehicles = ['ABC-1234', 'DEF-5678', 'GHI-9012', 'JKL-3456', 'MNO-7890'];
        vehicles.forEach(vehicle => {
            mileageData.push({
                vehicle: vehicle,
                km: Math.floor(Math.random() * 50000) + 10000 // 10k-60k km
            });
        });

        return {
            vehicles: vehicles.map(plate => ({ plate, brand: 'Toyota', model: 'Corolla' })),
            fuelData: fuelData,
            costsData: costsData,
            maintenanceData: maintenanceData,
            mileageData: mileageData
        };
    }

    // Combinar dados reais com simulados
    combineRealAndSimulatedData(realData, simulatedData) {
        console.log('🔄 Combinando dados reais e simulados...');

        return {
            vehicles: realData.vehicles.length > 0 ? realData.vehicles : simulatedData.vehicles,
            fuelData: realData.fuelData.length > 0 ? realData.fuelData : simulatedData.fuelData,
            costsData: this.processCostsData(realData.costsData, simulatedData.costsData),
            maintenanceData: realData.maintenanceData.length > 0 ? realData.maintenanceData : simulatedData.maintenanceData,
            mileageData: realData.mileageData.length > 0 ? realData.mileageData : simulatedData.mileageData
        };
    }

    // Processar dados de custos
    processCostsData(realCosts, simulatedCosts) {
        if (!realCosts || (realCosts.combustivel === 0 && realCosts.manutencao === 0)) {
            return simulatedCosts;
        }

        return [
            { category: 'Combustível', value: realCosts.combustivel || 0, color: '#007bff' },
            { category: 'Manutenção', value: realCosts.manutencao || 0, color: '#28a745' },
            { category: 'Lavagem', value: realCosts.lavagem || 0, color: '#ffc107' },
            { category: 'Outros', value: realCosts.outros || 0, color: '#6c757d' }
        ].filter(item => item.value > 0);
    }



    // Popular filtro de veículos
    populateVehicleFilter() {
        const select = document.getElementById('vehicleFilter');
        if (select) {
            select.innerHTML = '<option value="">Todos os veículos</option>';

            const savedVehicles = localStorage.getItem('frotas_vehicles');
            if (savedVehicles) {
                try {
                    const vehicles = JSON.parse(savedVehicles);
                    vehicles.forEach(vehicle => {
                        const option = document.createElement('option');
                        option.value = vehicle.id;
                        option.textContent = `${vehicle.plate} - ${vehicle.brand} ${vehicle.model}`;
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('Erro ao carregar veículos:', error);
                }
            }
        }
    }

    // Criar todos os gráficos
    createAllCharts() {
        const chartMethods = [
            { name: 'Consumo de Combustível', method: () => this.createFuelConsumptionChart() },
            { name: 'Custos por Categoria', method: () => this.createCostsByCategoryChart() },
            { name: 'Evolução de Custos', method: () => this.createCostsEvolutionChart() },
            { name: 'Status da Frota', method: () => this.createFleetStatusChart() },
            { name: 'Manutenções', method: () => this.createMaintenanceChart() },
            { name: 'Quilometragem', method: () => this.createMileageChart() },
            { name: 'Análise Comparativa', method: () => this.createComparativeChart() }
        ];

        chartMethods.forEach(chart => {
            try {
                chart.method();
                console.log(`✅ Gráfico "${chart.name}" criado com sucesso`);
            } catch (error) {
                console.error(`❌ Erro ao criar gráfico "${chart.name}":`, error);
                this.showNotification(`Erro ao carregar gráfico: ${chart.name}`, 'error');
            }
        });
    }

    // Gráfico de Consumo de Combustível
    createFuelConsumptionChart() {
        const ctx = document.getElementById('fuelConsumptionChart').getContext('2d');
        
        this.charts.fuelConsumption = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.fuelData.map(d => {
                    const date = new Date(d.date);
                    return date.getDate() + '/' + (date.getMonth() + 1);
                }),
                datasets: [{
                    label: 'Litros',
                    data: this.data.fuelData.map(d => d.liters),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Litros'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Custos por Categoria
    createCostsByCategoryChart() {
        const ctx = document.getElementById('costsByCategoryChart');
        if (!ctx) {
            console.error('❌ Canvas costsByCategoryChart não encontrado');
            return;
        }

        const context = ctx.getContext('2d');

        // Verificar se costsData é array ou objeto
        let labels, data, colors;

        if (Array.isArray(this.data.costsData)) {
            labels = this.data.costsData.map(item => item.category);
            data = this.data.costsData.map(item => item.value);
            colors = this.data.costsData.map(item => item.color);
        } else {
            labels = ['Combustível', 'Manutenção', 'Lavagem', 'Outros'];
            data = [
                this.data.costsData.combustivel || 0,
                this.data.costsData.manutencao || 0,
                this.data.costsData.lavagem || 0,
                this.data.costsData.outros || 0
            ];
            colors = ['#007bff', '#28a745', '#ffc107', '#6c757d'];
        }

        this.charts.costsByCategory = new Chart(context, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label}: R$ ${value.toLocaleString('pt-BR')} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Evolução de Custos
    createCostsEvolutionChart() {
        const ctx = document.getElementById('costsEvolutionChart');
        if (!ctx) {
            console.error('❌ Canvas costsEvolutionChart não encontrado');
            return;
        }

        const context = ctx.getContext('2d');

        // Usar dados de combustível para evolução de custos
        const labels = this.data.fuelData.slice(0, 15).map(d => {
            const date = new Date(d.date);
            return `${date.getDate()}/${date.getMonth() + 1}`;
        });

        const costs = this.data.fuelData.slice(0, 15).map(d => d.cost || 0);

        this.charts.costsEvolution = new Chart(context, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Custos (R$)',
                    data: costs,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#28a745',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Custo: R$ ${context.parsed.y.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Reais (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Período'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Status da Frota
    createFleetStatusChart() {
        const ctx = document.getElementById('fleetStatusChart');
        if (!ctx) {
            console.error('❌ Canvas fleetStatusChart não encontrado');
            return;
        }

        const context = ctx.getContext('2d');

        // Calcular status dos veículos
        const vehicles = this.data.vehicles;
        let ativos = 0, manutencao = 0, inativos = 0;

        if (vehicles.length > 0) {
            vehicles.forEach(vehicle => {
                const status = vehicle.status || 'ativo';
                switch (status.toLowerCase()) {
                    case 'ativo':
                        ativos++;
                        break;
                    case 'manutencao':
                    case 'manutenção':
                        manutencao++;
                        break;
                    case 'inativo':
                        inativos++;
                        break;
                    default:
                        ativos++;
                }
            });
        } else {
            // Dados simulados se não há veículos
            ativos = 15;
            manutencao = 3;
            inativos = 2;
        }

        this.charts.fleetStatus = new Chart(context, {
            type: 'pie',
            data: {
                labels: ['Ativos', 'Manutenção', 'Inativos'],
                datasets: [{
                    data: [ativos, manutencao, inativos],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label}: ${value} veículos (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Manutenções
    createMaintenanceChart() {
        const ctx = document.getElementById('maintenanceChart');
        if (!ctx) {
            console.error('❌ Canvas maintenanceChart não encontrado');
            return;
        }

        const context = ctx.getContext('2d');

        // Verificar se maintenanceData é array
        let labels, data;

        if (Array.isArray(this.data.maintenanceData) && this.data.maintenanceData.length > 0) {
            labels = this.data.maintenanceData.map(d => d.month || d.mes || 'N/A');
            data = this.data.maintenanceData.map(d => d.count || d.quantidade || 0);
        } else {
            // Dados simulados
            labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];
            data = [8, 12, 6, 15, 9, 11];
        }

        this.charts.maintenance = new Chart(context, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Manutenções',
                    data: data,
                    backgroundColor: '#ffc107',
                    borderColor: '#e0a800',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Manutenções: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantidade'
                        },
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Mês'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Quilometragem
    createMileageChart() {
        const ctx = document.getElementById('mileageChart');
        if (!ctx) {
            console.error('❌ Canvas mileageChart não encontrado');
            return;
        }

        const context = ctx.getContext('2d');

        // Verificar se mileageData existe e tem dados
        let labels, data;

        if (Array.isArray(this.data.mileageData) && this.data.mileageData.length > 0) {
            labels = this.data.mileageData.map(d => d.vehicle || d.placa || 'N/A');
            data = this.data.mileageData.map(d => d.km || d.quilometragem || 0);
        } else {
            // Dados simulados
            labels = ['ABC-1234', 'DEF-5678', 'GHI-9012', 'JKL-3456', 'MNO-7890'];
            data = [45200, 32100, 28900, 51300, 39800];
        }

        this.charts.mileage = new Chart(context, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Quilômetros',
                    data: data,
                    backgroundColor: '#17a2b8',
                    borderColor: '#138496',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Quilometragem: ${context.parsed.x.toLocaleString('pt-BR')} km`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quilômetros'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('pt-BR') + ' km';
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Veículos'
                        }
                    }
                }
            }
        });
    }

    // Gráfico Comparativo
    createComparativeChart() {
        const ctx = document.getElementById('comparativeChart');
        if (!ctx) {
            console.error('❌ Canvas comparativeChart não encontrado');
            return;
        }

        const context = ctx.getContext('2d');

        // Dados comparativos dos últimos 6 meses
        const labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];

        // Gerar dados baseados nos dados reais ou simulados
        const fuelData = labels.map(() => Math.floor(Math.random() * 500) + 1000); // 1000-1500L
        const costsData = labels.map(() => Math.floor(Math.random() * 2000) + 4000); // R$ 4000-6000
        const maintenanceData = labels.map(() => Math.floor(Math.random() * 10) + 5); // 5-15 manutenções

        this.charts.comparative = new Chart(context, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Combustível (L)',
                        data: fuelData,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        yAxisID: 'y',
                        tension: 0.4,
                        pointBackgroundColor: '#007bff',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    },
                    {
                        label: 'Custos (R$)',
                        data: costsData,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.4,
                        pointBackgroundColor: '#28a745',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    },
                    {
                        label: 'Manutenções',
                        data: maintenanceData,
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        yAxisID: 'y2',
                        tension: 0.4,
                        pointBackgroundColor: '#ffc107',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Mês'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Litros'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Reais (R$)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    y2: {
                        type: 'linear',
                        display: false,
                        position: 'right',
                    }
                }
            }
        });
    }

    // Atualizar estatísticas
    updateStatistics() {
        const period = document.getElementById('periodFilter').value;
        const periodText = this.getPeriodText(period);
        
        // Calcular totais baseados no período
        const totalFuel = this.data.fuelData.reduce((sum, d) => sum + d.liters, 0);
        const totalCosts = Object.values(this.data.costsData).reduce((sum, cost) => sum + cost, 0);
        const totalMaintenances = this.data.maintenanceData.reduce((sum, d) => sum + d.count, 0);
        const totalKm = this.data.mileageData.reduce((sum, d) => sum + d.km, 0);
        
        document.getElementById('totalFuelConsumed').textContent = totalFuel.toLocaleString('pt-BR') + 'L';
        document.getElementById('totalCosts').textContent = 'R$ ' + totalCosts.toLocaleString('pt-BR');
        document.getElementById('totalMaintenances').textContent = totalMaintenances;
        document.getElementById('totalKilometers').textContent = totalKm.toLocaleString('pt-BR') + ' km';
        
        // Atualizar textos de período
        document.getElementById('fuelPeriod').textContent = periodText;
        document.getElementById('costsPeriod').textContent = periodText;
        document.getElementById('maintenancePeriod').textContent = periodText;
        document.getElementById('kmPeriod').textContent = periodText;
    }

    // Obter texto do período
    getPeriodText(days) {
        switch (days) {
            case '7': return 'Últimos 7 dias';
            case '30': return 'Últimos 30 dias';
            case '90': return 'Últimos 3 meses';
            case '365': return 'Último ano';
            default: return 'Período selecionado';
        }
    }

    // Atualizar gráficos
    updateCharts() {
        try {
            // Mostrar loading
            this.showLoading(true);

            // Regenerar dados baseados nos filtros
            this.data.fuelData = this.generateFuelData();
            this.data.costsData = this.generateCostsData();
            this.data.maintenanceData = this.generateMaintenanceData();
            this.data.mileageData = this.generateMileageData();

            // Atualizar cada gráfico
            Object.values(this.charts).forEach(chart => {
                if (chart && typeof chart.destroy === 'function') {
                    chart.destroy();
                }
            });

            // Limpar referências
            this.charts = {};

            // Recriar gráficos
            this.createAllCharts();
            this.updateStatistics();

            // Esconder loading
            this.showLoading(false);

            // Mostrar notificação
            this.showNotification('Gráficos atualizados com sucesso!', 'success');

        } catch (error) {
            console.error('Erro ao atualizar gráficos:', error);
            this.showLoading(false);
            this.showNotification('Erro ao atualizar gráficos. Tente novamente.', 'error');
        }
    }

    // Resetar filtros
    resetFilters() {
        document.getElementById('periodFilter').value = '30';
        document.getElementById('vehicleFilter').value = '';
        document.getElementById('chartTypeFilter').value = 'all';
        this.updateCharts();
    }

    // Atualizar gráficos
    refreshCharts() {
        this.updateCharts();
    }

    // Exportar gráficos
    exportCharts() {
        try {
            this.showLoading(true);

            // Simular processo de exportação
            setTimeout(() => {
                this.showLoading(false);

                // Criar link de download simulado
                const link = document.createElement('a');
                link.href = '#';
                link.download = `graficos_frota_${new Date().toISOString().split('T')[0]}.pdf`;

                this.showNotification('Exportação concluída! Download iniciado.', 'success');

                // Simular download
                console.log('Download simulado:', link.download);
            }, 2000);

        } catch (error) {
            console.error('Erro ao exportar gráficos:', error);
            this.showLoading(false);
            this.showNotification('Erro ao exportar gráficos. Tente novamente.', 'error');
        }
    }

    // Mostrar/esconder loading
    showLoading(show) {
        const loadingElements = document.querySelectorAll('.chart-loading');
        loadingElements.forEach(element => {
            element.style.display = show ? 'flex' : 'none';
        });

        // Se não existir loading, criar um simples
        if (show && loadingElements.length === 0) {
            const charts = document.querySelectorAll('.chart-card');
            charts.forEach(chart => {
                chart.style.opacity = '0.6';
                chart.style.pointerEvents = 'none';
            });
        } else if (!show) {
            const charts = document.querySelectorAll('.chart-card');
            charts.forEach(chart => {
                chart.style.opacity = '1';
                chart.style.pointerEvents = 'auto';
            });
        }
    }

    // Mostrar notificação
    showNotification(message, type = 'info') {
        // Tentar usar o sistema de notificações do sistema principal
        if (window.frotasSystem && window.frotasSystem.showNotification) {
            window.frotasSystem.showNotification(message, type);
            return;
        }

        // Fallback: criar notificação simples
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';

        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // Auto remover após 5 segundos
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Funções globais
function updateCharts() {
    if (window.chartsManager) {
        window.chartsManager.updateCharts();
    }
}

function refreshCharts() {
    if (window.chartsManager) {
        window.chartsManager.refreshCharts();
    }
}

function exportCharts() {
    if (window.chartsManager) {
        window.chartsManager.exportCharts();
    }
}

function resetFilters() {
    if (window.chartsManager) {
        window.chartsManager.resetFilters();
    }
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 DOM carregado, inicializando sistema de gráficos...');

    // Aguardar um pouco para garantir que todos os scripts foram carregados
    setTimeout(() => {
        try {
            window.chartsManager = new ChartsManager();
            console.log('✅ ChartsManager criado e disponível globalmente');
        } catch (error) {
            console.error('❌ Erro ao criar ChartsManager:', error);
        }
    }, 100);
});

// Fallback para inicialização manual
window.initializeCharts = function() {
    if (!window.chartsManager) {
        console.log('🔄 Inicialização manual dos gráficos...');
        window.chartsManager = new ChartsManager();
    } else {
        console.log('ℹ️ Sistema de gráficos já inicializado');
    }
};
