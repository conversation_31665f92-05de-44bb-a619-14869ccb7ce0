// Sistema de Gráficos e Análises
class ChartsManager {
    constructor() {
        this.charts = {};
        this.data = this.loadData();
        this.init();
    }

    init() {
        try {
            console.log('🚀 Inicializando sistema de gráficos...');

            // Verificar se Chart.js está carregado
            if (typeof Chart === 'undefined') {
                throw new Error('Chart.js não está carregado');
            }

            this.populateVehicleFilter();
            this.createAllCharts();
            this.updateStatistics();
            this.setupEventListeners();

            console.log('✅ Sistema de gráficos inicializado com sucesso');
            this.showNotification('Gráficos carregados com sucesso!', 'success');

        } catch (error) {
            console.error('❌ Erro ao inicializar sistema de gráficos:', error);
            this.showNotification('Erro ao carregar gráficos. Verifique a conexão.', 'error');
        }
    }

    // Configurar event listeners
    setupEventListeners() {
        // Filtros
        const periodFilter = document.getElementById('periodFilter');
        const vehicleFilter = document.getElementById('vehicleFilter');
        const chartTypeFilter = document.getElementById('chartTypeFilter');

        if (periodFilter) {
            periodFilter.addEventListener('change', () => this.updateCharts());
        }

        if (vehicleFilter) {
            vehicleFilter.addEventListener('change', () => this.updateCharts());
        }

        if (chartTypeFilter) {
            chartTypeFilter.addEventListener('change', () => this.updateCharts());
        }
    }

    // Carregar dados reais do sistema
    loadData() {
        return {
            vehicles: this.loadVehiclesFromStorage(),
            fuelData: this.loadFuelDataFromStorage(),
            costsData: this.loadCostsDataFromStorage(),
            maintenanceData: this.loadMaintenanceDataFromStorage(),
            mileageData: this.loadMileageDataFromStorage()
        };
    }

    // Carregar veículos do localStorage
    loadVehiclesFromStorage() {
        const savedVehicles = localStorage.getItem('frotas_vehicles');
        if (savedVehicles) {
            try {
                return JSON.parse(savedVehicles);
            } catch (error) {
                console.error('Erro ao carregar veículos:', error);
            }
        }
        return [];
    }

    // Carregar dados de combustível do localStorage
    loadFuelDataFromStorage() {
        const savedFuel = localStorage.getItem('abastecimentos');
        if (savedFuel) {
            try {
                return JSON.parse(savedFuel);
            } catch (error) {
                console.error('Erro ao carregar dados de combustível:', error);
            }
        }
        return [];
    }

    // Carregar dados de custos do localStorage
    loadCostsDataFromStorage() {
        const abastecimentos = this.loadFuelDataFromStorage();
        const manutencoes = this.loadMaintenanceDataFromStorage();

        const combustivel = abastecimentos.reduce((total, item) => total + (item.valor || 0), 0);
        const manutencao = manutencoes.reduce((total, item) => total + (item.valorReal || item.valorEstimado || 0), 0);

        return {
            combustivel: combustivel,
            manutencao: manutencao,
            lavagem: 0, // Será implementado quando houver dados de lavagem
            outros: 0
        };
    }

    // Carregar dados de manutenção do localStorage
    loadMaintenanceDataFromStorage() {
        const savedMaintenance = localStorage.getItem('manutencoes');
        if (savedMaintenance) {
            try {
                return JSON.parse(savedMaintenance);
            } catch (error) {
                console.error('Erro ao carregar dados de manutenção:', error);
            }
        }
        return [];
    }

    // Carregar dados de quilometragem do localStorage
    loadMileageDataFromStorage() {
        const vehicles = this.loadVehiclesFromStorage();
        return vehicles.map(vehicle => ({
            vehicle: vehicle.plate,
            km: vehicle.km || 0
        }));
    }



    // Popular filtro de veículos
    populateVehicleFilter() {
        const select = document.getElementById('vehicleFilter');
        if (select) {
            select.innerHTML = '<option value="">Todos os veículos</option>';

            const savedVehicles = localStorage.getItem('frotas_vehicles');
            if (savedVehicles) {
                try {
                    const vehicles = JSON.parse(savedVehicles);
                    vehicles.forEach(vehicle => {
                        const option = document.createElement('option');
                        option.value = vehicle.id;
                        option.textContent = `${vehicle.plate} - ${vehicle.brand} ${vehicle.model}`;
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('Erro ao carregar veículos:', error);
                }
            }
        }
    }

    // Criar todos os gráficos
    createAllCharts() {
        const chartMethods = [
            { name: 'Consumo de Combustível', method: () => this.createFuelConsumptionChart() },
            { name: 'Custos por Categoria', method: () => this.createCostsByCategoryChart() },
            { name: 'Evolução de Custos', method: () => this.createCostsEvolutionChart() },
            { name: 'Status da Frota', method: () => this.createFleetStatusChart() },
            { name: 'Manutenções', method: () => this.createMaintenanceChart() },
            { name: 'Quilometragem', method: () => this.createMileageChart() },
            { name: 'Análise Comparativa', method: () => this.createComparativeChart() }
        ];

        chartMethods.forEach(chart => {
            try {
                chart.method();
                console.log(`✅ Gráfico "${chart.name}" criado com sucesso`);
            } catch (error) {
                console.error(`❌ Erro ao criar gráfico "${chart.name}":`, error);
                this.showNotification(`Erro ao carregar gráfico: ${chart.name}`, 'error');
            }
        });
    }

    // Gráfico de Consumo de Combustível
    createFuelConsumptionChart() {
        const ctx = document.getElementById('fuelConsumptionChart').getContext('2d');
        
        this.charts.fuelConsumption = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.fuelData.map(d => {
                    const date = new Date(d.date);
                    return date.getDate() + '/' + (date.getMonth() + 1);
                }),
                datasets: [{
                    label: 'Litros',
                    data: this.data.fuelData.map(d => d.liters),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Litros'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Custos por Categoria
    createCostsByCategoryChart() {
        const ctx = document.getElementById('costsByCategoryChart').getContext('2d');
        
        this.charts.costsByCategory = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Combustível', 'Manutenção', 'Lavagem', 'Outros'],
                datasets: [{
                    data: [
                        this.data.costsData.combustivel,
                        this.data.costsData.manutencao,
                        this.data.costsData.lavagem,
                        this.data.costsData.outros
                    ],
                    backgroundColor: [
                        '#007bff',
                        '#ffc107',
                        '#28a745',
                        '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Gráfico de Evolução de Custos
    createCostsEvolutionChart() {
        const ctx = document.getElementById('costsEvolutionChart').getContext('2d');
        
        this.charts.costsEvolution = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.fuelData.map(d => {
                    const date = new Date(d.date);
                    return date.getDate() + '/' + (date.getMonth() + 1);
                }),
                datasets: [{
                    label: 'Custos (R$)',
                    data: this.data.fuelData.map(d => d.cost),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Reais (R$)'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Status da Frota
    createFleetStatusChart() {
        const ctx = document.getElementById('fleetStatusChart').getContext('2d');
        
        this.charts.fleetStatus = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Ativos', 'Manutenção', 'Inativos'],
                datasets: [{
                    data: [15, 3, 2],
                    backgroundColor: [
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Gráfico de Manutenções
    createMaintenanceChart() {
        const ctx = document.getElementById('maintenanceChart').getContext('2d');
        
        this.charts.maintenance = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.data.maintenanceData.map(d => d.month),
                datasets: [{
                    label: 'Manutenções',
                    data: this.data.maintenanceData.map(d => d.count),
                    backgroundColor: '#ffc107',
                    borderColor: '#ffc107',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantidade'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Quilometragem
    createMileageChart() {
        const ctx = document.getElementById('mileageChart').getContext('2d');

        this.charts.mileage = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.data.mileageData.map(d => d.vehicle),
                datasets: [{
                    label: 'Quilômetros',
                    data: this.data.mileageData.map(d => d.km),
                    backgroundColor: '#17a2b8',
                    borderColor: '#17a2b8',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quilômetros'
                        }
                    }
                }
            }
        });
    }

    // Gráfico Comparativo
    createComparativeChart() {
        const ctx = document.getElementById('comparativeChart').getContext('2d');
        
        this.charts.comparative = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [
                    {
                        label: 'Combustível (L)',
                        data: [1200, 1350, 1100, 1400, 1250, 1300],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        yAxisID: 'y'
                    },
                    {
                        label: 'Custos (R$)',
                        data: [5000, 5500, 4800, 6200, 5300, 5800],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y1'
                    },
                    {
                        label: 'Manutenções',
                        data: [8, 12, 6, 15, 9, 11],
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        yAxisID: 'y2'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Mês'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Litros'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Reais (R$)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    y2: {
                        type: 'linear',
                        display: false,
                        position: 'right',
                    }
                }
            }
        });
    }

    // Atualizar estatísticas
    updateStatistics() {
        const period = document.getElementById('periodFilter').value;
        const periodText = this.getPeriodText(period);
        
        // Calcular totais baseados no período
        const totalFuel = this.data.fuelData.reduce((sum, d) => sum + d.liters, 0);
        const totalCosts = Object.values(this.data.costsData).reduce((sum, cost) => sum + cost, 0);
        const totalMaintenances = this.data.maintenanceData.reduce((sum, d) => sum + d.count, 0);
        const totalKm = this.data.mileageData.reduce((sum, d) => sum + d.km, 0);
        
        document.getElementById('totalFuelConsumed').textContent = totalFuel.toLocaleString('pt-BR') + 'L';
        document.getElementById('totalCosts').textContent = 'R$ ' + totalCosts.toLocaleString('pt-BR');
        document.getElementById('totalMaintenances').textContent = totalMaintenances;
        document.getElementById('totalKilometers').textContent = totalKm.toLocaleString('pt-BR') + ' km';
        
        // Atualizar textos de período
        document.getElementById('fuelPeriod').textContent = periodText;
        document.getElementById('costsPeriod').textContent = periodText;
        document.getElementById('maintenancePeriod').textContent = periodText;
        document.getElementById('kmPeriod').textContent = periodText;
    }

    // Obter texto do período
    getPeriodText(days) {
        switch (days) {
            case '7': return 'Últimos 7 dias';
            case '30': return 'Últimos 30 dias';
            case '90': return 'Últimos 3 meses';
            case '365': return 'Último ano';
            default: return 'Período selecionado';
        }
    }

    // Atualizar gráficos
    updateCharts() {
        try {
            // Mostrar loading
            this.showLoading(true);

            // Regenerar dados baseados nos filtros
            this.data.fuelData = this.generateFuelData();
            this.data.costsData = this.generateCostsData();
            this.data.maintenanceData = this.generateMaintenanceData();
            this.data.mileageData = this.generateMileageData();

            // Atualizar cada gráfico
            Object.values(this.charts).forEach(chart => {
                if (chart && typeof chart.destroy === 'function') {
                    chart.destroy();
                }
            });

            // Limpar referências
            this.charts = {};

            // Recriar gráficos
            this.createAllCharts();
            this.updateStatistics();

            // Esconder loading
            this.showLoading(false);

            // Mostrar notificação
            this.showNotification('Gráficos atualizados com sucesso!', 'success');

        } catch (error) {
            console.error('Erro ao atualizar gráficos:', error);
            this.showLoading(false);
            this.showNotification('Erro ao atualizar gráficos. Tente novamente.', 'error');
        }
    }

    // Resetar filtros
    resetFilters() {
        document.getElementById('periodFilter').value = '30';
        document.getElementById('vehicleFilter').value = '';
        document.getElementById('chartTypeFilter').value = 'all';
        this.updateCharts();
    }

    // Atualizar gráficos
    refreshCharts() {
        this.updateCharts();
    }

    // Exportar gráficos
    exportCharts() {
        try {
            this.showLoading(true);

            // Simular processo de exportação
            setTimeout(() => {
                this.showLoading(false);

                // Criar link de download simulado
                const link = document.createElement('a');
                link.href = '#';
                link.download = `graficos_frota_${new Date().toISOString().split('T')[0]}.pdf`;

                this.showNotification('Exportação concluída! Download iniciado.', 'success');

                // Simular download
                console.log('Download simulado:', link.download);
            }, 2000);

        } catch (error) {
            console.error('Erro ao exportar gráficos:', error);
            this.showLoading(false);
            this.showNotification('Erro ao exportar gráficos. Tente novamente.', 'error');
        }
    }

    // Mostrar/esconder loading
    showLoading(show) {
        const loadingElements = document.querySelectorAll('.chart-loading');
        loadingElements.forEach(element => {
            element.style.display = show ? 'flex' : 'none';
        });

        // Se não existir loading, criar um simples
        if (show && loadingElements.length === 0) {
            const charts = document.querySelectorAll('.chart-card');
            charts.forEach(chart => {
                chart.style.opacity = '0.6';
                chart.style.pointerEvents = 'none';
            });
        } else if (!show) {
            const charts = document.querySelectorAll('.chart-card');
            charts.forEach(chart => {
                chart.style.opacity = '1';
                chart.style.pointerEvents = 'auto';
            });
        }
    }

    // Mostrar notificação
    showNotification(message, type = 'info') {
        // Tentar usar o sistema de notificações do sistema principal
        if (window.frotasSystem && window.frotasSystem.showNotification) {
            window.frotasSystem.showNotification(message, type);
            return;
        }

        // Fallback: criar notificação simples
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';

        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(notification);

        // Auto remover após 5 segundos
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Funções globais
function updateCharts() {
    if (window.chartsManager) {
        window.chartsManager.updateCharts();
    }
}

function refreshCharts() {
    if (window.chartsManager) {
        window.chartsManager.refreshCharts();
    }
}

function exportCharts() {
    if (window.chartsManager) {
        window.chartsManager.exportCharts();
    }
}

function resetFilters() {
    if (window.chartsManager) {
        window.chartsManager.resetFilters();
    }
}

// Inicializar quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 DOM carregado, inicializando sistema de gráficos...');

    // Aguardar um pouco para garantir que todos os scripts foram carregados
    setTimeout(() => {
        try {
            window.chartsManager = new ChartsManager();
            console.log('✅ ChartsManager criado e disponível globalmente');
        } catch (error) {
            console.error('❌ Erro ao criar ChartsManager:', error);
        }
    }, 100);
});

// Fallback para inicialização manual
window.initializeCharts = function() {
    if (!window.chartsManager) {
        console.log('🔄 Inicialização manual dos gráficos...');
        window.chartsManager = new ChartsManager();
    } else {
        console.log('ℹ️ Sistema de gráficos já inicializado');
    }
};
