const { createClient } = require('@supabase/supabase-js');

// Verificar se as variáveis de ambiente estão definidas
if (!process.env.SUPABASE_URL) {
  throw new Error('SUPABASE_URL não está definida nas variáveis de ambiente');
}

if (!process.env.SUPABASE_ANON_KEY) {
  throw new Error('SUPABASE_ANON_KEY não está definida nas variáveis de ambiente');
}

// Cliente Supabase para operações públicas (com RLS)
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: false
    }
  }
);

// Cliente Supabase para operações administrativas (bypass RLS)
let supabaseAdmin = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  supabaseAdmin = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

module.exports = {
  supabase,
  supabaseAdmin
};
