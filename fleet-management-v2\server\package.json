{"name": "fleet-management-server", "version": "1.0.0", "description": "Backend API para Sistema de Gestão de Frotas", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix", "db:setup": "node scripts/setup-database.js"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "date-fns": "^2.30.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0"}, "engines": {"node": ">=18.0.0"}}