// Sistema de Autenticação
class AuthSystem {
    constructor() {
        this.users = JSON.parse(localStorage.getItem('users')) || [];
        this.currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
        this.initializeDefaultUsers();
    }

    // Inicializar apenas usuário admin padrão
    initializeDefaultUsers() {
        if (this.users.length === 0) {
            const adminUser = {
                id: 1,
                name: 'Administrador',
                email: '<EMAIL>',
                password: 'admin123',
                role: 'admin',
                permissions: ['all'],
                createdAt: new Date().toISOString()
            };

            this.users = [adminUser];
            localStorage.setItem('users', JSON.stringify(this.users));
        }
    }

    // Login
    login(email, password) {
        const user = this.users.find(u => u.email === email && u.password === password);
        
        if (user) {
            this.currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));
            return { success: true, user: user };
        }
        
        return { success: false, message: 'Email ou senha incorretos' };
    }

    // Logout
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    }

    // Registrar novo usuário
    register(userData) {
        // Verificar se email já existe
        if (this.users.find(u => u.email === userData.email)) {
            return { success: false, message: 'Email já cadastrado' };
        }

        // Verificar se senhas coincidem
        if (userData.password !== userData.confirmPassword) {
            return { success: false, message: 'Senhas não coincidem' };
        }

        // Definir permissões baseadas no role
        let permissions = [];
        switch (userData.role) {
            case 'admin':
                permissions = ['all'];
                break;
            case 'supervisor':
                permissions = ['dashboard', 'abastecimento', 'revisao', 'manutencao', 'lavagem', 'relatorios', 'graficos'];
                break;
            case 'operador':
                permissions = ['dashboard', 'abastecimento', 'lavagem'];
                break;
        }

        const newUser = {
            id: this.users.length + 1,
            name: userData.name,
            email: userData.email,
            password: userData.password,
            role: userData.role,
            permissions: permissions,
            createdAt: new Date().toISOString()
        };

        this.users.push(newUser);
        localStorage.setItem('users', JSON.stringify(this.users));

        return { success: true, message: 'Usuário cadastrado com sucesso' };
    }

    // Verificar se usuário está logado
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Verificar permissão
    hasPermission(permission) {
        if (!this.currentUser) return false;
        if (this.currentUser.permissions.includes('all')) return true;
        return this.currentUser.permissions.includes(permission);
    }

    // Obter usuário atual
    getCurrentUser() {
        return this.currentUser;
    }

    // Obter todos os usuários (apenas admin)
    getAllUsers() {
        if (this.hasPermission('all')) {
            return this.users;
        }
        return [];
    }

    // Atualizar usuário
    updateUser(userId, userData) {
        if (!this.hasPermission('all')) {
            return { success: false, message: 'Sem permissão' };
        }

        const userIndex = this.users.findIndex(u => u.id === userId);
        if (userIndex === -1) {
            return { success: false, message: 'Usuário não encontrado' };
        }

        this.users[userIndex] = { ...this.users[userIndex], ...userData };
        localStorage.setItem('users', JSON.stringify(this.users));

        return { success: true, message: 'Usuário atualizado com sucesso' };
    }

    // Deletar usuário
    deleteUser(userId) {
        if (!this.hasPermission('all')) {
            return { success: false, message: 'Sem permissão' };
        }

        this.users = this.users.filter(u => u.id !== userId);
        localStorage.setItem('users', JSON.stringify(this.users));

        return { success: true, message: 'Usuário removido com sucesso' };
    }
}

// Instância global do sistema de autenticação
const auth = new AuthSystem();

// Funções para o formulário de login
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

function showRegisterForm() {
    document.getElementById('registerModal').style.display = 'block';
}

function closeRegisterModal() {
    document.getElementById('registerModal').style.display = 'none';
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se está na página de login
    if (document.getElementById('loginForm')) {
        // Se já está logado, redirecionar para dashboard
        if (auth.isLoggedIn()) {
            window.location.href = 'dashboard.html';
            return;
        }

        // Form de login
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const result = auth.login(email, password);
            
            if (result.success) {
                window.location.href = 'dashboard.html';
            } else {
                alert(result.message);
            }
        });

        // Form de registro
        if (document.getElementById('registerForm')) {
            document.getElementById('registerForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const userData = Object.fromEntries(formData);
                
                const result = auth.register(userData);
                
                if (result.success) {
                    alert(result.message);
                    closeRegisterModal();
                    this.reset();
                } else {
                    alert(result.message);
                }
            });
        }
    }

    // Verificar se está em páginas protegidas
    if (window.location.pathname !== '/index.html' && !window.location.pathname.endsWith('index.html') && window.location.pathname !== '/') {
        if (!auth.isLoggedIn()) {
            window.location.href = 'index.html';
            return;
        }

        // Atualizar nome do usuário no header
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = auth.getCurrentUser().name;
        }
    }
});

// Função de logout
function logout() {
    if (confirm('Deseja realmente sair do sistema?')) {
        auth.logout();
    }
}

// Fechar modal ao clicar fora
window.onclick = function(event) {
    const modal = document.getElementById('registerModal');
    if (event.target === modal) {
        closeRegisterModal();
    }
}
