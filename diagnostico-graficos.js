// Diagnóstico do Sistema de Gráficos
console.log('🔍 Iniciando diagnóstico do sistema de gráficos...');

function diagnosticoCompleto() {
    const resultado = {
        timestamp: new Date().toISOString(),
        chartJs: false,
        chartsManager: false,
        canvasElements: [],
        instanciaManager: false,
        erros: [],
        recomendacoes: []
    };

    // 1. Verificar Chart.js
    console.log('📊 Verificando Chart.js...');
    if (typeof Chart !== 'undefined') {
        resultado.chartJs = true;
        console.log(`✅ Chart.js disponível - Versão: ${Chart.version}`);
    } else {
        resultado.erros.push('Chart.js não está carregado');
        resultado.recomendacoes.push('Verificar se o CDN do Chart.js está acessível');
        console.log('❌ Chart.js não disponível');
    }

    // 2. Verificar ChartsManager
    console.log('🔧 Verificando ChartsManager...');
    if (typeof ChartsManager !== 'undefined') {
        resultado.chartsManager = true;
        console.log('✅ ChartsManager disponível');
    } else {
        resultado.erros.push('ChartsManager não está definido');
        resultado.recomendacoes.push('Verificar se js/graficos.js foi carregado corretamente');
        console.log('❌ ChartsManager não disponível');
    }

    // 3. Verificar instância do ChartsManager
    console.log('🎯 Verificando instância do ChartsManager...');
    if (window.chartsManager) {
        resultado.instanciaManager = true;
        console.log('✅ Instância do ChartsManager existe');
        
        if (window.chartsManager.charts) {
            const chartsCount = Object.keys(window.chartsManager.charts).length;
            console.log(`📈 ${chartsCount} gráficos na instância`);
        }
    } else {
        resultado.erros.push('Instância do ChartsManager não existe');
        resultado.recomendacoes.push('Executar inicialização manual do ChartsManager');
        console.log('❌ Instância do ChartsManager não existe');
    }

    // 4. Verificar elementos canvas
    console.log('🖼️ Verificando elementos canvas...');
    const canvasIds = [
        'fuelConsumptionChart',
        'costsByCategoryChart', 
        'costsEvolutionChart',
        'fleetStatusChart',
        'maintenanceChart',
        'mileageChart',
        'comparativeChart'
    ];

    canvasIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            resultado.canvasElements.push({
                id: id,
                existe: true,
                tipo: element.tagName,
                contexto: element.getContext ? 'disponível' : 'indisponível'
            });
            console.log(`✅ Canvas ${id} encontrado`);
        } else {
            resultado.canvasElements.push({
                id: id,
                existe: false
            });
            console.log(`❌ Canvas ${id} não encontrado`);
        }
    });

    // 5. Verificar erros no console
    console.log('🚨 Verificando erros...');
    if (resultado.erros.length === 0) {
        console.log('✅ Nenhum erro crítico detectado');
    } else {
        console.log(`❌ ${resultado.erros.length} erro(s) detectado(s):`);
        resultado.erros.forEach(erro => console.log(`   - ${erro}`));
    }

    // 6. Recomendações
    if (resultado.recomendacoes.length > 0) {
        console.log('💡 Recomendações:');
        resultado.recomendacoes.forEach(rec => console.log(`   - ${rec}`));
    }

    return resultado;
}

function tentarCorrecaoAutomatica() {
    console.log('🔧 Tentando correção automática...');
    
    const diagnostico = diagnosticoCompleto();
    
    // Se Chart.js não está disponível, tentar carregar
    if (!diagnostico.chartJs) {
        console.log('📦 Tentando carregar Chart.js...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js';
        script.onload = function() {
            console.log('✅ Chart.js carregado com sucesso');
            setTimeout(tentarCorrecaoAutomatica, 500);
        };
        script.onerror = function() {
            console.error('❌ Falha ao carregar Chart.js');
        };
        document.head.appendChild(script);
        return;
    }
    
    // Se ChartsManager está disponível mas não há instância, criar
    if (diagnostico.chartsManager && !diagnostico.instanciaManager) {
        console.log('🚀 Criando instância do ChartsManager...');
        try {
            window.chartsManager = new ChartsManager();
            console.log('✅ ChartsManager criado com sucesso');
        } catch (error) {
            console.error('❌ Erro ao criar ChartsManager:', error);
        }
    }
    
    // Se nada funcionou, usar sistema simplificado
    if (!diagnostico.instanciaManager && diagnostico.chartJs) {
        console.log('🔄 Usando sistema simplificado...');
        criarGraficosSimplificados();
    }
}

function criarGraficosSimplificados() {
    console.log('📊 Criando gráficos simplificados...');
    
    const configs = [
        { id: 'fuelConsumptionChart', tipo: 'line', titulo: 'Consumo de Combustível' },
        { id: 'costsByCategoryChart', tipo: 'doughnut', titulo: 'Custos por Categoria' },
        { id: 'fleetStatusChart', tipo: 'pie', titulo: 'Status da Frota' },
        { id: 'maintenanceChart', tipo: 'bar', titulo: 'Manutenções' }
    ];
    
    configs.forEach(config => {
        const canvas = document.getElementById(config.id);
        if (canvas) {
            try {
                criarGraficoSimples(canvas, config);
                console.log(`✅ ${config.titulo} criado`);
            } catch (error) {
                console.error(`❌ Erro ao criar ${config.titulo}:`, error);
            }
        }
    });
}

function criarGraficoSimples(canvas, config) {
    const ctx = canvas.getContext('2d');
    
    let chartConfig;
    
    switch (config.tipo) {
        case 'line':
            chartConfig = {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                    datasets: [{
                        label: config.titulo,
                        data: [65, 59, 80, 81, 56, 55],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { y: { beginAtZero: true } }
                }
            };
            break;
            
        case 'doughnut':
            chartConfig = {
                type: 'doughnut',
                data: {
                    labels: ['Combustível', 'Manutenção', 'Lavagem', 'Outros'],
                    datasets: [{
                        data: [15000, 8500, 2300, 1800],
                        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#6c757d']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            };
            break;
            
        case 'pie':
            chartConfig = {
                type: 'pie',
                data: {
                    labels: ['Ativos', 'Manutenção', 'Inativos'],
                    datasets: [{
                        data: [15, 3, 2],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            };
            break;
            
        case 'bar':
            chartConfig = {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                    datasets: [{
                        label: config.titulo,
                        data: [12, 19, 3, 5, 2, 3],
                        backgroundColor: '#ffc107'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { y: { beginAtZero: true } }
                }
            };
            break;
    }
    
    new Chart(ctx, chartConfig);
}

// Funções globais para uso no console
window.diagnosticoGraficos = diagnosticoCompleto;
window.corrigirGraficos = tentarCorrecaoAutomatica;
window.graficosSimplificados = criarGraficosSimplificados;

// Executar diagnóstico automaticamente
setTimeout(() => {
    console.log('🔍 Executando diagnóstico automático...');
    const resultado = diagnosticoCompleto();
    
    if (resultado.erros.length > 0) {
        console.log('🔧 Tentando correção automática...');
        tentarCorrecaoAutomatica();
    } else {
        console.log('✅ Sistema de gráficos funcionando corretamente');
    }
}, 1000);

console.log('✅ Diagnóstico carregado. Use diagnosticoGraficos() no console para verificar o status.');
